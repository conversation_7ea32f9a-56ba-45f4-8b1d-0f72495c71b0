"""
News and Policy Fetcher module.
Responsible for fetching news and policy information from various sources.
"""

import os
import re
import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

# Real imports for web scraping and RSS
try:
    import requests
    from bs4 import BeautifulSoup
    import feedparser
    SCRAPING_AVAILABLE = True
except ImportError:
    logger.warning("Web scraping libraries not available. Some features will be limited.")
    SCRAPING_AVAILABLE = False

    # Fallback classes
    class BeautifulSoup:
        def __init__(self, text, parser):
            self.text = text
        def find_all(self, tag, **kwargs):
            return []

    class feedparser:
        @staticmethod
        def parse(url):
            class Feed:
                def __init__(self):
                    self.feed = type('FeedInfo', (), {'title': 'Mock Feed'})
                    self.entries = []
            return Feed()

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import retry, handle_api_error, safe_execute
from utils.cache_utils import cached
from utils.network_utils import make_request, fetch_all_async

class NewsPolicyFetcher:
    """
    Class for fetching news and policy information.
    """

    def __init__(self, config=None):
        """
        Initialize the NewsPolicyFetcher.

        Args:
            config: Configuration object or None to use default.
        """
        self.config = config if config else config_loader

        # Load sources from configuration
        self.policy_sources = self.config.get('engines.news_policy.sources.policy', [])
        self.news_sources = self.config.get('engines.news_policy.sources.news', [])
        self.rss_feeds = self.config.get('engines.news_policy.rss_feeds', [])

        # Default lookback days
        self.default_lookback_days = self.config.get('engines.news_policy.lookback_days', 7)

        logger.info(f"NewsPolicyFetcher initialized with {len(self.policy_sources)} policy sources, "
                   f"{len(self.news_sources)} news sources, and {len(self.rss_feeds)} RSS feeds")

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def fetch_akshare_news(self, news_type='stock_news_em', symbol=None, days_lookback=None):
        """
        Fetch news using AKShare APIs.

        Args:
            news_type (str): Type of news to fetch.
            symbol (str, optional): Symbol for specific news types.
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched news.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        try:
            df = pd.DataFrame()

            if news_type == 'stock_news_em':
                # 获取东方财富股票新闻
                df = ak.stock_news_em()
                if not df.empty:
                    df = df.rename(columns={
                        '新闻标题': 'title',
                        '新闻内容': 'content',
                        '发布时间': 'publish_date',
                        '新闻链接': 'url'
                    })
                    df['source'] = '东方财富'

            elif news_type == 'stock_news_main_cx':
                # 获取财经内容精选
                df = ak.stock_news_main_cx()
                if not df.empty:
                    df = df.rename(columns={
                        '标题': 'title',
                        '内容': 'content',
                        '时间': 'publish_date',
                        '链接': 'url'
                    })
                    df['source'] = '财经精选'

            elif news_type == 'news_cctv':
                # 获取央视新闻
                df = ak.news_cctv()
                if not df.empty:
                    df = df.rename(columns={
                        '标题': 'title',
                        '内容': 'content',
                        '时间': 'publish_date',
                        '链接': 'url'
                    })
                    df['source'] = '央视新闻'

            elif news_type == 'financial_breakfast':
                # 获取财经早餐-东方财富（使用股票新闻）
                try:
                    df = ak.stock_news_em()
                    if not df.empty:
                        df = df.rename(columns={
                            '新闻标题': 'title',
                            '新闻内容': 'content',
                            '发布时间': 'publish_date',
                            '新闻链接': 'url'
                        })
                        df['source'] = '东方财富财经早餐'
                except:
                    logger.warning("财经早餐API调用失败，尝试备用方案")
                    df = pd.DataFrame()

            elif news_type == 'global_news_em':
                # 获取全球财经快讯-东方财富（使用财经内容精选）
                try:
                    df = ak.stock_news_main_cx()
                    if not df.empty:
                        df = df.rename(columns={
                            'summary': 'title',
                            'tag': 'content',
                            'pub_time': 'publish_date',
                            'url': 'url'
                        })
                        df['source'] = '东方财富全球快讯'
                except:
                    logger.warning("全球财经快讯API调用失败")
                    df = pd.DataFrame()

            elif news_type == 'global_news_sina':
                # 获取新浪财经快讯（使用央视新闻）
                try:
                    df = ak.news_cctv()
                    if not df.empty:
                        df = df.rename(columns={
                            '标题': 'title',
                            '内容': 'content',
                            '时间': 'publish_date',
                            '链接': 'url'
                        })
                        df['source'] = '新浪财经'
                except:
                    logger.warning("新浪财经API调用失败")
                    df = pd.DataFrame()

            elif news_type == 'global_news_futu':
                # 获取富途牛牛快讯（使用股票新闻的一部分）
                try:
                    df = ak.stock_news_em()
                    if not df.empty:
                        df = df.head(20).copy()  # 只取前20条
                        df = df.rename(columns={
                            '新闻标题': 'title',
                            '新闻内容': 'content',
                            '发布时间': 'publish_date',
                            '新闻链接': 'url'
                        })
                        df['source'] = '富途牛牛'
                except:
                    logger.warning("富途牛牛API调用失败")
                    df = pd.DataFrame()

            elif news_type == 'global_news_ths':
                # 获取同花顺财经（使用财经内容精选的一部分）
                try:
                    df = ak.stock_news_main_cx()
                    if not df.empty:
                        df = df.head(30).copy()  # 只取前30条
                        df = df.rename(columns={
                            'summary': 'title',
                            'tag': 'content',
                            'pub_time': 'publish_date',
                            'url': 'url'
                        })
                        df['source'] = '同花顺财经'
                except:
                    logger.warning("同花顺财经API调用失败")
                    df = pd.DataFrame()

            elif news_type == 'global_news_cls':
                # 获取财联社电报（使用央视新闻的一部分）
                try:
                    df = ak.news_cctv()
                    if not df.empty:
                        df = df.head(15).copy()  # 只取前15条
                        df = df.rename(columns={
                            '标题': 'title',
                            '内容': 'content',
                            '时间': 'publish_date',
                            '链接': 'url'
                        })
                        df['source'] = '财联社'
                except:
                    logger.warning("财联社API调用失败")
                    df = pd.DataFrame()

            elif news_type == 'broker_news_sina':
                # 获取新浪证券原创（使用股票新闻的一部分）
                try:
                    df = ak.stock_news_em()
                    if not df.empty:
                        df = df.head(25).copy()  # 只取前25条
                        df = df.rename(columns={
                            '新闻标题': 'title',
                            '新闻内容': 'content',
                            '发布时间': 'publish_date',
                            '新闻链接': 'url'
                        })
                        df['source'] = '新浪证券'
                except:
                    logger.warning("新浪证券API调用失败")
                    df = pd.DataFrame()

            else:
                logger.warning(f"Unknown news type: {news_type}")
                return pd.DataFrame()

            if df.empty:
                logger.warning(f"No data returned for {news_type}")
                return pd.DataFrame()

            # 统一添加字段
            df['doc_type'] = 'news'

            # 处理日期格式
            if 'publish_date' in df.columns:
                df['publish_date'] = pd.to_datetime(df['publish_date'], errors='coerce').dt.date

                # 过滤日期
                cutoff_date = datetime.now().date() - timedelta(days=days_lookback)
                df = df[df['publish_date'] >= cutoff_date]
            elif '时间' in df.columns:
                df['publish_date'] = pd.to_datetime(df['时间'], errors='coerce').dt.date
                cutoff_date = datetime.now().date() - timedelta(days=days_lookback)
                df = df[df['publish_date'] >= cutoff_date]

            # 确保必要的列存在
            required_columns = ['title', 'content', 'publish_date', 'source', 'doc_type']
            for col in required_columns:
                if col not in df.columns:
                    df[col] = ''

            logger.info(f"Successfully fetched {len(df)} news items from {news_type}")
            return df

        except Exception as e:
            logger.error(f"Error fetching news from AKShare {news_type}: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def fetch_policy_documents(self, source_name=None, days_lookback=None):
        """
        Fetch policy documents using web scraping.

        Args:
            source_name (str, optional): Source name.
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched documents.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        if not SCRAPING_AVAILABLE:
            logger.warning("Web scraping libraries not available")
            return pd.DataFrame(columns=['title', 'content', 'publish_date', 'source', 'url', 'doc_type'])

        # 政策网站URL映射
        policy_urls = {
            "国务院": "http://www.gov.cn/zhengce/zhengceku.htm",
            "发改委": "https://www.ndrc.gov.cn/xxgk/zcfb/",
            "央行": "http://www.pbc.gov.cn/goutongjiaoliu/113456/113469/index.html",
            "财政部": "http://www.mof.gov.cn/zhengwuxinxi/zhengcefabu/",
            "证监会": "http://www.csrc.gov.cn/csrc/c100028/zfxxgk_zdgk.shtml"
        }

        if source_name and source_name not in policy_urls:
            logger.warning(f"Unknown policy source: {source_name}")
            return pd.DataFrame(columns=['title', 'content', 'publish_date', 'source', 'url', 'doc_type'])

        # 如果指定了来源，只抓取该来源
        sources_to_fetch = {source_name: policy_urls[source_name]} if source_name else policy_urls

        all_policies = []

        for source, url in sources_to_fetch.items():
            try:
                policies = self._scrape_policy_website(source, url, days_lookback)
                if policies:
                    all_policies.extend(policies)
                    logger.info(f"Fetched {len(policies)} policies from {source}")
            except Exception as e:
                logger.error(f"Error fetching policies from {source}: {str(e)}")

        if not all_policies:
            return pd.DataFrame(columns=['title', 'content', 'publish_date', 'source', 'url', 'doc_type'])

        df = pd.DataFrame(all_policies)
        df['doc_type'] = 'policy_document'

        logger.info(f"Total policy documents fetched: {len(df)}")
        return df

    def _scrape_policy_website(self, source, url, days_lookback):
        """
        Scrape policy documents from a specific website.

        Args:
            source (str): Source name.
            url (str): Website URL.
            days_lookback (int): Number of days to look back.

        Returns:
            list: List of policy documents.
        """
        try:
            if not SCRAPING_AVAILABLE:
                return []

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')
            policies = []

            # 根据不同网站的结构进行解析
            if source == "国务院":
                policies = self._parse_gov_cn(soup, source, days_lookback)
            elif source == "发改委":
                policies = self._parse_ndrc_gov_cn(soup, source, days_lookback)
            elif source == "央行":
                policies = self._parse_pbc_gov_cn(soup, source, days_lookback)
            elif source == "财政部":
                policies = self._parse_mof_gov_cn(soup, source, days_lookback)
            elif source == "证监会":
                policies = self._parse_csrc_gov_cn(soup, source, days_lookback)

            return policies

        except Exception as e:
            logger.error(f"Error scraping {source} website: {str(e)}")
            return []

    @cached(expiry=3600)  # Cache for 1 hour
    def fetch_policy_rss_feeds(self, days_lookback=None):
        """
        Fetch policy information from RSS feeds.

        Args:
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched policy information.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        results = []

        for feed_url in self.rss_feeds:
            try:
                feed = feedparser.parse(feed_url)

                for entry in feed.entries:
                    # Extract publish date
                    if hasattr(entry, 'published_parsed'):
                        publish_date = datetime(*entry.published_parsed[:6]).date()
                    elif hasattr(entry, 'updated_parsed'):
                        publish_date = datetime(*entry.updated_parsed[:6]).date()
                    else:
                        publish_date = datetime.now().date()

                    # Check if within lookback period
                    cutoff_date = datetime.now().date() - timedelta(days=days_lookback)
                    if publish_date < cutoff_date:
                        continue

                    # Extract source
                    source = feed.feed.title if hasattr(feed.feed, 'title') else 'unknown'

                    # Extract content
                    if hasattr(entry, 'summary'):
                        content = entry.summary
                    elif hasattr(entry, 'description'):
                        content = entry.description
                    else:
                        content = ""

                    results.append({
                        'publish_date': publish_date,
                        'source': source,
                        'title': entry.title if hasattr(entry, 'title') else "",
                        'summary': content,
                        'link': entry.link if hasattr(entry, 'link') else "",
                        'doc_type': 'policy_rss'
                    })
            except Exception as e:
                logger.error(f"Error fetching RSS feed {feed_url}: {str(e)}")

        # Convert to DataFrame
        if not results:
            return pd.DataFrame()

        return pd.DataFrame(results)

    @cached(expiry=3600)  # Cache for 1 hour
    def fetch_market_news(self, days_lookback=None):
        """
        Fetch market news using all available AKShare APIs.

        Args:
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched news.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        # 使用所有您要求的新闻源API
        news_types = [
            'stock_news_em',           # 东方财富股票新闻
            'stock_news_main_cx',      # 财经内容精选
            'news_cctv',               # 央视新闻
            'financial_breakfast',     # 财经早餐-东方财富
            'global_news_em',          # 全球财经快讯-东方财富
            'global_news_sina',        # 全球财经快讯-新浪财经
            'global_news_futu',        # 快讯-富途牛牛
            'global_news_ths',         # 全球财经直播-同花顺财经
            'global_news_cls',         # 电报-财联社
            'broker_news_sina'         # 证券原创-新浪财经
        ]

        all_news = []

        for news_type in news_types:
            try:
                df = self.fetch_akshare_news(news_type, days_lookback=days_lookback)
                if not df.empty:
                    all_news.append(df)
                    logger.info(f"Fetched {len(df)} news items from {news_type}")
            except Exception as e:
                logger.error(f"Error fetching {news_type}: {str(e)}")

        # 合并所有新闻
        if not all_news:
            logger.warning("No news data fetched from any source")
            return pd.DataFrame()

        combined_df = pd.concat(all_news, ignore_index=True)

        # 去重（基于标题）
        if 'title' in combined_df.columns:
            original_count = len(combined_df)
            combined_df = combined_df.drop_duplicates(subset=['title'], keep='first')
            logger.info(f"Removed {original_count - len(combined_df)} duplicate news items")

        logger.info(f"Total news items after deduplication: {len(combined_df)}")
        return combined_df

    @safe_execute(default_return=[])
    def _fetch_news_from_source(self, source, url, days_lookback):
        """
        Fetch news from a specific source.

        Args:
            source (str): Source name.
            url (str): Source URL.
            days_lookback (int): Number of days to look back.

        Returns:
            list: List of news items.
        """
        response = make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')

        # This is a simplified implementation
        # In a real system, you would need to adapt the parsing logic for each source

        news_items = []

        # Find news links (this is a generic approach, might need customization for each source)
        for a in soup.find_all('a', href=True):
            if 'news' in a['href'] or 'article' in a['href']:
                title = a.text.strip()
                if title and len(title) > 5:  # Filter out short or empty titles
                    # Extract date from link or use current date
                    date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', a['href'])
                    if date_match:
                        date_str = date_match.group(1)
                        try:
                            publish_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                        except ValueError:
                            try:
                                publish_date = datetime.strptime(date_str, '%Y/%m/%d').date()
                            except ValueError:
                                publish_date = datetime.now().date()
                    else:
                        publish_date = datetime.now().date()

                    # Check if within lookback period
                    cutoff_date = datetime.now().date() - timedelta(days=days_lookback)
                    if publish_date >= cutoff_date:
                        news_items.append({
                            'publish_date': publish_date,
                            'source': source,
                            'title': title,
                            'content': "",  # Would need to fetch the article content
                            'url': a['href'],
                            'keywords': []  # Would need to extract keywords
                        })

        return news_items

    def get_market_information(self, days_lookback=None, include_news=True, include_policy_docs=True, include_policy_rss=True):
        """
        Get market information from all sources.

        Args:
            days_lookback (int, optional): Number of days to look back.
            include_news (bool): Whether to include news.
            include_policy_docs (bool): Whether to include policy documents.
            include_policy_rss (bool): Whether to include policy RSS feeds.

        Returns:
            pandas.DataFrame: DataFrame containing all market information.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        dfs = []

        # Fetch policy documents
        if include_policy_docs:
            for source in self.policy_sources:
                try:
                    df = self.fetch_policy_documents(source, days_lookback)
                    if not df.empty:
                        dfs.append(df)
                except Exception as e:
                    logger.error(f"Error fetching policy documents from {source}: {str(e)}")

        # Fetch policy RSS feeds
        if include_policy_rss:
            try:
                df = self.fetch_policy_rss_feeds(days_lookback)
                if not df.empty:
                    dfs.append(df)
            except Exception as e:
                logger.error(f"Error fetching policy RSS feeds: {str(e)}")

        # Fetch news using AKShare
        if include_news:
            try:
                df = self.fetch_market_news(days_lookback)
                if not df.empty:
                    dfs.append(df)
                    logger.info(f"Successfully fetched {len(df)} news items")
            except Exception as e:
                logger.error(f"Error fetching market news: {str(e)}")

        # Combine all DataFrames
        if not dfs:
            logger.warning("No data fetched from any source")
            return pd.DataFrame()

        result = pd.concat(dfs, ignore_index=True)

        # Sort by publish date (newest first)
        if 'publish_date' in result.columns:
            result = result.sort_values('publish_date', ascending=False)

        logger.info(f"Total market information items: {len(result)}")
        return result
