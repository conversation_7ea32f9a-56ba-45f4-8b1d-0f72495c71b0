"""
测试统一后的新闻政策模块
验证所有重复模块已清理，仅保留一种实现
"""

import pandas as pd
import time
from datetime import datetime
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unified_news_policy_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_unified_policy_data_source():
    """测试统一的PolicyDataSource"""
    
    logger.info("=" * 80)
    logger.info("测试统一的新闻政策模块")
    logger.info("=" * 80)
    
    # 1. 测试直接导入
    logger.info("\n1. 测试直接导入 PolicyDataSource...")
    try:
        from data_sources.policy_data import PolicyDataSource
        policy_data = PolicyDataSource()
        logger.info("✅ 直接导入成功")
    except Exception as e:
        logger.error(f"❌ 直接导入失败: {e}")
        return False
    
    # 2. 测试通过engines模块导入（向后兼容）
    logger.info("\n2. 测试通过engines模块导入...")
    try:
        from engines.news_policy import NewsPolicyFetcher
        fetcher = NewsPolicyFetcher()
        logger.info("✅ engines模块导入成功")
        
        # 验证是否是同一个类
        if NewsPolicyFetcher == PolicyDataSource:
            logger.info("✅ 确认是同一个类，向后兼容正常")
        else:
            logger.warning("⚠️ 不是同一个类，可能存在问题")
    except Exception as e:
        logger.error(f"❌ engines模块导入失败: {e}")
    
    # 3. 测试所有新闻API（您要求的接口）
    logger.info("\n3. 测试所有新闻API...")
    
    news_apis = [
        ('get_financial_breakfast', '财经早餐-东方财富'),
        ('get_global_news_em', '全球财经快讯-东方财富'),
        ('get_global_news_sina', '全球财经快讯-新浪财经'),
        ('get_global_news_futu', '快讯-富途牛牛'),
        ('get_global_news_ths', '全球财经直播-同花顺财经'),
        ('get_global_news_cls', '电报-财联社'),
        ('get_broker_news_sina', '证券原创-新浪财经'),
        ('get_stock_news_em', '个股新闻-东方财富'),
        ('get_news_main_cx', '财经内容精选')
    ]
    
    total_news = 0
    successful_apis = 0
    
    for method_name, description in news_apis:
        try:
            method = getattr(policy_data, method_name)
            
            # 根据方法调用参数
            if method_name == 'get_global_news_cls':
                df = method(symbol='全部')
            elif method_name == 'get_broker_news_sina':
                df = method(page='1')
            else:
                df = method()
            
            if not df.empty:
                logger.info(f"✅ {description}: {len(df)} 条")
                total_news += len(df)
                successful_apis += 1
            else:
                logger.warning(f"⚠️ {description}: 0 条（可能是刷新控制或API问题）")
                
        except Exception as e:
            logger.error(f"❌ {description}: 失败 - {e}")
    
    # 4. 测试政策API
    logger.info("\n4. 测试政策API...")
    
    try:
        # 测试国务院政策（限制数量）
        gov_policies = policy_data.get_gov_policy(page=1, limit=10)
        logger.info(f"✅ 国务院政策: {len(gov_policies)} 条")
        
        # 测试发改委政策（限制数量）
        ndrc_policies = policy_data.get_ndrc_policy(page=1, limit=10)
        logger.info(f"✅ 发改委政策: {len(ndrc_policies)} 条")
        
    except Exception as e:
        logger.error(f"❌ 政策API测试失败: {e}")
    
    # 5. 测试统一获取接口
    logger.info("\n5. 测试统一获取接口...")
    
    try:
        all_news = policy_data.get_all_news_with_refresh_control()
        logger.info(f"✅ 统一获取: {len(all_news)} 条新闻")
        
        if not all_news.empty and 'source' in all_news.columns:
            source_counts = all_news['source'].value_counts()
            logger.info("   来源分布:")
            for source, count in source_counts.head(5).items():
                logger.info(f"     {source}: {count} 条")
                
    except Exception as e:
        logger.error(f"❌ 统一获取失败: {e}")
    
    # 6. 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("统一模块测试结果")
    logger.info("=" * 80)
    
    logger.info(f"📊 新闻API总数: {len(news_apis)}")
    logger.info(f"✅ 成功调用: {successful_apis}")
    logger.info(f"📈 总新闻数量: {total_news}")
    
    success_rate = (successful_apis / len(news_apis)) * 100
    logger.info(f"🎯 成功率: {success_rate:.1f}%")
    
    return success_rate > 50  # 50%以上成功率认为测试通过

def test_core_unified_data_collector():
    """测试core模块是否正确使用新的PolicyDataSource"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试core模块集成")
    logger.info("=" * 80)
    
    try:
        from core.unified_data_collector import UnifiedDataCollector
        collector = UnifiedDataCollector()
        logger.info("✅ UnifiedDataCollector 初始化成功")
        
        # 检查是否使用了新的PolicyDataSource
        if hasattr(collector, 'policy_data_source'):
            logger.info("✅ 确认使用新的PolicyDataSource")
            
            # 检查数据源配置是否更新
            news_sources = collector.data_sources.get("新闻数据", {})
            if "financial_breakfast" in news_sources:
                logger.info("✅ 数据源配置已更新为新API")
            else:
                logger.warning("⚠️ 数据源配置可能未完全更新")
        else:
            logger.warning("⚠️ 未找到policy_data_source属性")
            
    except Exception as e:
        logger.error(f"❌ core模块测试失败: {e}")

def check_removed_files():
    """检查重复文件是否已删除"""
    
    logger.info("\n" + "=" * 80)
    logger.info("检查重复文件清理状态")
    logger.info("=" * 80)
    
    import os
    
    # 检查应该被删除的文件
    removed_files = [
        "engines/news_policy/fetcher.py",
        "engines/news_policy/analyzer.py"
    ]
    
    for file_path in removed_files:
        if os.path.exists(file_path):
            logger.warning(f"⚠️ 文件仍存在: {file_path}")
        else:
            logger.info(f"✅ 文件已删除: {file_path}")
    
    # 检查保留的文件
    kept_files = [
        "data_sources/policy_data.py",
        "engines/news_policy/__init__.py"
    ]
    
    for file_path in kept_files:
        if os.path.exists(file_path):
            logger.info(f"✅ 文件已保留: {file_path}")
        else:
            logger.error(f"❌ 重要文件丢失: {file_path}")

if __name__ == "__main__":
    logger.info(f"开始统一新闻政策模块测试 - {datetime.now()}")
    
    # 检查文件清理状态
    check_removed_files()
    
    # 测试统一的PolicyDataSource
    success = test_unified_policy_data_source()
    
    # 测试core模块集成
    test_core_unified_data_collector()
    
    logger.info("\n" + "=" * 80)
    logger.info("测试完成")
    logger.info("=" * 80)
    logger.info(f"测试结束时间: {datetime.now()}")
    
    if success:
        logger.info("🎉 统一新闻政策模块测试通过！")
    else:
        logger.warning("⚠️ 测试存在问题，需要进一步检查")
    
    logger.info("详细日志已保存到: unified_news_policy_test.log")
