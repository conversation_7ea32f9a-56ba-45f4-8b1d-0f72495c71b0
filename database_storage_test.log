2025-05-27 14:19:59,582 - __main__ - INFO - 开始数据库存储流程测试
2025-05-27 14:19:59,582 - __main__ - INFO - 测试时间: 2025-05-27 14:19:59.582385
2025-05-27 14:19:59,582 - __main__ - INFO - ============================================================
2025-05-27 14:19:59,582 - __main__ - INFO - 测试数据库配置
2025-05-27 14:19:59,582 - __main__ - INFO - ============================================================
2025-05-27 14:19:59,582 - __main__ - INFO - ✅ 数据库配置文件存在
2025-05-27 14:19:59,582 - __main__ - INFO -   postgresql: 禁用 (有备用方案)
2025-05-27 14:19:59,582 - __main__ - INFO -   influxdb: 禁用 (有备用方案)
2025-05-27 14:19:59,582 - __main__ - INFO -   mongodb: 禁用 (有备用方案)
2025-05-27 14:19:59,593 - __main__ - INFO -   redis: 禁用 (有备用方案)
2025-05-27 14:19:59,593 - __main__ - INFO -   fallback: 启用
2025-05-27 14:19:59,594 - __main__ - INFO - 
============================================================
2025-05-27 14:19:59,594 - __main__ - INFO - 测试文件存储功能
2025-05-27 14:19:59,594 - __main__ - INFO - ============================================================
2025-05-27 14:19:59,596 - file_storage - INFO - 文件存储管理器初始化完成，基础目录: data
2025-05-27 14:19:59,596 - __main__ - INFO - ✅ FileStorage 初始化成功
2025-05-27 14:19:59,596 - __main__ - INFO - ✅ 新闻内容存储成功: news\2025-01\测试来源\news_20250115_test_news_001.json
2025-05-27 14:19:59,596 - __main__ - WARNING - ⚠️ 新闻文件未找到
2025-05-27 14:19:59,596 - __main__ - INFO - ✅ 政策内容存储成功: policies\2025-01\国务院\policy_20250110_test_policy_001.json
2025-05-27 14:19:59,596 - __main__ - WARNING - ⚠️ 政策文件未找到
2025-05-27 14:19:59,596 - __main__ - INFO - 
============================================================
2025-05-27 14:19:59,596 - __main__ - INFO - 测试统一数据访问接口
2025-05-27 14:19:59,596 - __main__ - INFO - ============================================================
2025-05-27 14:19:59,601 - unified_data_access - INFO - 关系型数据库未启用
2025-05-27 14:19:59,602 - unified_data_access - INFO - 时序数据库未启用
2025-05-27 14:19:59,602 - unified_data_access - INFO - 文档数据库未启用
2025-05-27 14:19:59,602 - unified_data_access - INFO - 内存数据库未启用
2025-05-27 14:19:59,602 - data_sync_service - INFO - 数据同步服务初始化完成
2025-05-27 14:19:59,602 - unified_data_access - INFO - 统一数据访问接口初始化完成
2025-05-27 14:19:59,602 - __main__ - INFO - ✅ UnifiedDataAccess 初始化成功
2025-05-27 14:19:59,602 - __main__ - ERROR - ❌ 统一数据访问测试失败: 'UnifiedDataAccess' object has no attribute 'connect'
2025-05-27 14:19:59,603 - __main__ - INFO - 
============================================================
2025-05-27 14:19:59,603 - __main__ - INFO - 测试内容处理器
2025-05-27 14:19:59,603 - __main__ - INFO - ============================================================
2025-05-27 14:19:59,603 - __main__ - ERROR - ❌ 内容处理器测试失败: ContentProcessor.__init__() missing 2 required positional arguments: 'data_access' and 'file_storage'
2025-05-27 14:19:59,604 - __main__ - INFO - 
================================================================================
2025-05-27 14:19:59,604 - __main__ - INFO - 数据库存储流程测试结果
2025-05-27 14:19:59,604 - __main__ - INFO - ================================================================================
2025-05-27 14:19:59,604 - __main__ - INFO - ✅ 成功 数据库配置
2025-05-27 14:19:59,604 - __main__ - INFO - ✅ 成功 文件存储
2025-05-27 14:19:59,604 - __main__ - INFO - ❌ 失败 统一数据访问
2025-05-27 14:19:59,604 - __main__ - INFO - ❌ 失败 内容处理器
2025-05-27 14:19:59,604 - __main__ - INFO - 
总体结果: 2/4 项测试通过
2025-05-27 14:19:59,604 - __main__ - WARNING - ⚠️ 数据库存储流程存在问题，需要进一步调试
2025-05-27 14:19:59,604 - __main__ - INFO - 
测试完成时间: 2025-05-27 14:19:59.604082
2025-05-27 14:19:59,604 - __main__ - INFO - 详细日志已保存到: database_storage_test.log
