"""
增强版波动率分析器
优化GARCH波动率预测参数，完善政策波动率溢价模型，优化资金流-波动率耦合系数
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Union
from arch import arch_model
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedVolatilityAnalyzer:
    """
    增强版波动率分析器

    功能：
    1. 优化GARCH模型参数和预测精度
    2. 政策波动率溢价模型
    3. 资金流-波动率耦合分析
    4. 多因子波动率预测模型
    5. 波动率制度识别和切换
    """

    def __init__(self, config=None):
        """
        初始化增强版波动率分析器

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # GARCH模型参数优化
        self.garch_params = {
            'p_range': self.config.get('garch_p_range', [1, 2, 3]),
            'q_range': self.config.get('garch_q_range', [1, 2, 3]),
            'distribution': self.config.get('garch_distribution', 'normal'),  # normal, t, skewt
            'mean_model': self.config.get('garch_mean_model', 'Constant')  # Constant, ARX
        }

        # 波动率制度阈值
        self.volatility_regimes = {
            'very_low': 0.10,
            'low': 0.15,
            'normal': 0.25,
            'high': 0.35,
            'very_high': 0.50
        }

        # 政策波动率参数
        self.policy_volatility_params = {
            'policy_impact_window': 5,  # 政策影响窗口期（天）
            'policy_decay_factor': 0.8,  # 政策影响衰减因子
            'policy_amplification_factor': 1.5  # 政策放大因子
        }

        # 资金流-波动率耦合参数
        self.coupling_params = {
            'flow_volatility_window': 10,  # 资金流波动率计算窗口
            'coupling_threshold': 0.3,  # 耦合强度阈值
            'lag_periods': [1, 2, 3, 5]  # 滞后期分析
        }

        logger.info("增强版波动率分析器初始化完成")

    def optimize_garch_parameters(self, returns: pd.Series) -> Dict:
        """
        优化GARCH模型参数

        Args:
            returns: 收益率序列

        Returns:
            Dict: 最优参数和模型信息
        """
        try:
            logger.info("开始GARCH参数优化...")

            if len(returns) < 50:
                logger.warning("数据量不足，使用默认参数")
                return {
                    'optimal_p': 1,
                    'optimal_q': 1,
                    'optimal_distribution': 'normal',
                    'aic': np.inf,
                    'bic': np.inf,
                    'optimization_success': False
                }

            best_aic = np.inf
            best_params = {}
            optimization_results = []

            # 网格搜索最优参数
            for p in self.garch_params['p_range']:
                for q in self.garch_params['q_range']:
                    for dist in ['normal', 't']:  # 简化分布选择
                        try:
                            # 构建GARCH模型
                            model = arch_model(
                                returns * 100,  # 转换为百分比
                                vol='Garch',
                                p=p,
                                q=q,
                                dist=dist,
                                mean=self.garch_params['mean_model']
                            )

                            # 拟合模型
                            fitted_model = model.fit(disp='off', show_warning=False)

                            # 记录结果
                            result = {
                                'p': p,
                                'q': q,
                                'distribution': dist,
                                'aic': fitted_model.aic,
                                'bic': fitted_model.bic,
                                'log_likelihood': fitted_model.loglikelihood
                            }
                            optimization_results.append(result)

                            # 更新最优参数
                            if fitted_model.aic < best_aic:
                                best_aic = fitted_model.aic
                                best_params = {
                                    'optimal_p': p,
                                    'optimal_q': q,
                                    'optimal_distribution': dist,
                                    'aic': fitted_model.aic,
                                    'bic': fitted_model.bic,
                                    'log_likelihood': fitted_model.loglikelihood,
                                    'optimization_success': True,
                                    'fitted_model': fitted_model
                                }

                        except Exception as e:
                            logger.debug(f"GARCH({p},{q}) with {dist} failed: {e}")
                            continue

            if not best_params:
                logger.warning("GARCH参数优化失败，使用默认参数")
                return {
                    'optimal_p': 1,
                    'optimal_q': 1,
                    'optimal_distribution': 'normal',
                    'aic': np.inf,
                    'bic': np.inf,
                    'optimization_success': False
                }

            best_params['optimization_results'] = optimization_results
            logger.info(f"GARCH参数优化完成: p={best_params['optimal_p']}, q={best_params['optimal_q']}, dist={best_params['optimal_distribution']}")

            return best_params

        except Exception as e:
            logger.error(f"GARCH参数优化失败: {e}")
            return {
                'optimal_p': 1,
                'optimal_q': 1,
                'optimal_distribution': 'normal',
                'aic': np.inf,
                'bic': np.inf,
                'optimization_success': False
            }

    def calculate_enhanced_garch_volatility(self, price_data: pd.DataFrame,
                                          forecast_horizon: int = 1) -> Dict:
        """
        计算增强版GARCH波动率预测

        Args:
            price_data: 价格数据
            forecast_horizon: 预测期数

        Returns:
            Dict: 增强版GARCH分析结果
        """
        try:
            logger.info("计算增强版GARCH波动率...")

            if price_data.empty or len(price_data) < 30:
                logger.warning("数据不足，无法计算GARCH波动率")
                return self._get_empty_garch_result()

            # 计算对数收益率
            price_data = price_data.sort_values('date')
            price_data['log_return'] = np.log(price_data['close'] / price_data['close'].shift(1))
            returns = price_data['log_return'].dropna()

            if len(returns) < 30:
                logger.warning("有效收益率数据不足")
                return self._get_empty_garch_result()

            # 优化GARCH参数
            optimization_result = self.optimize_garch_parameters(returns)

            if not optimization_result['optimization_success']:
                logger.warning("GARCH参数优化失败，使用默认参数")
                p, q, dist = 1, 1, 'normal'
            else:
                p = optimization_result['optimal_p']
                q = optimization_result['optimal_q']
                dist = optimization_result['optimal_distribution']

            # 构建最优GARCH模型
            model = arch_model(
                returns * 100,
                vol='Garch',
                p=p,
                q=q,
                dist=dist,
                mean=self.garch_params['mean_model']
            )

            # 拟合模型
            fitted_model = model.fit(disp='off', show_warning=False)

            # 生成预测
            forecast = fitted_model.forecast(horizon=forecast_horizon)

            # 提取预测结果
            volatility_forecast = np.sqrt(forecast.variance.iloc[-1, :forecast_horizon]) / 100
            mean_forecast = forecast.mean.iloc[-1, :forecast_horizon] / 100 if hasattr(forecast, 'mean') else np.zeros(forecast_horizon)

            # 计算置信区间
            residual_forecast = forecast.residual_variance.iloc[-1, :forecast_horizon] if hasattr(forecast, 'residual_variance') else None

            # 模型诊断
            model_diagnostics = self._perform_garch_diagnostics(fitted_model, returns)

            # 年化波动率
            annualized_volatility = volatility_forecast * np.sqrt(252)

            result = {
                'volatility_forecast': volatility_forecast.tolist(),
                'annualized_volatility': annualized_volatility.tolist(),
                'mean_forecast': mean_forecast.tolist(),
                'optimal_parameters': {
                    'p': p,
                    'q': q,
                    'distribution': dist
                },
                'model_fit_quality': {
                    'aic': fitted_model.aic,
                    'bic': fitted_model.bic,
                    'log_likelihood': fitted_model.loglikelihood
                },
                'model_diagnostics': model_diagnostics,
                'forecast_horizon': forecast_horizon,
                'current_volatility': float(annualized_volatility[0]) if len(annualized_volatility) > 0 else 0.0
            }

            logger.info(f"GARCH波动率预测完成，当前年化波动率: {result['current_volatility']:.3f}")
            return result

        except Exception as e:
            logger.error(f"增强版GARCH波动率计算失败: {e}")
            return self._get_empty_garch_result()

    def calculate_policy_volatility_premium(self, price_data: pd.DataFrame,
                                          policy_events: List[Dict]) -> Dict:
        """
        计算政策波动率溢价

        Args:
            price_data: 价格数据
            policy_events: 政策事件列表

        Returns:
            Dict: 政策波动率溢价分析结果
        """
        try:
            logger.info("计算政策波动率溢价...")

            if price_data.empty:
                return self._get_empty_policy_volatility_result()

            # 计算基础波动率
            price_data = price_data.sort_values('date')
            price_data['log_return'] = np.log(price_data['close'] / price_data['close'].shift(1))
            price_data['rolling_volatility'] = price_data['log_return'].rolling(
                window=self.policy_volatility_params['policy_impact_window']
            ).std() * np.sqrt(252)

            # 识别政策事件影响期
            policy_impact_periods = []
            for event in policy_events:
                event_date = pd.to_datetime(event['date'])
                impact_start = event_date
                impact_end = event_date + timedelta(days=self.policy_volatility_params['policy_impact_window'])

                policy_impact_periods.append({
                    'start': impact_start,
                    'end': impact_end,
                    'event_type': event.get('type', 'unknown'),
                    'importance': event.get('importance', 0.5)
                })

            # 计算政策期间和非政策期间的波动率
            price_data['is_policy_period'] = False
            for period in policy_impact_periods:
                mask = (price_data['date'] >= period['start']) & (price_data['date'] <= period['end'])
                price_data.loc[mask, 'is_policy_period'] = True

            # 分别计算波动率
            policy_period_vol = price_data[price_data['is_policy_period']]['rolling_volatility'].mean()
            normal_period_vol = price_data[~price_data['is_policy_period']]['rolling_volatility'].mean()

            # 计算政策波动率溢价
            if normal_period_vol > 0:
                policy_volatility_premium = policy_period_vol - normal_period_vol
                policy_volatility_ratio = policy_period_vol / normal_period_vol
            else:
                policy_volatility_premium = 0.0
                policy_volatility_ratio = 1.0

            # 分析政策类型对波动率的影响
            policy_type_impact = {}
            for event in policy_events:
                event_type = event.get('type', 'unknown')
                if event_type not in policy_type_impact:
                    policy_type_impact[event_type] = []

                event_date = pd.to_datetime(event['date'])
                # 计算事件前后波动率变化
                pre_event_data = price_data[
                    (price_data['date'] >= event_date - timedelta(days=5)) &
                    (price_data['date'] < event_date)
                ]
                post_event_data = price_data[
                    (price_data['date'] >= event_date) &
                    (price_data['date'] <= event_date + timedelta(days=5))
                ]

                if not pre_event_data.empty and not post_event_data.empty:
                    pre_vol = pre_event_data['rolling_volatility'].mean()
                    post_vol = post_event_data['rolling_volatility'].mean()
                    if pre_vol > 0:
                        vol_change = (post_vol - pre_vol) / pre_vol
                        policy_type_impact[event_type].append(vol_change)

            # 计算各政策类型的平均影响
            policy_type_avg_impact = {}
            for policy_type, impacts in policy_type_impact.items():
                if impacts:
                    policy_type_avg_impact[policy_type] = {
                        'avg_impact': np.mean(impacts),
                        'impact_std': np.std(impacts),
                        'event_count': len(impacts)
                    }

            result = {
                'policy_volatility_premium': policy_volatility_premium,
                'policy_volatility_ratio': policy_volatility_ratio,
                'policy_period_volatility': policy_period_vol,
                'normal_period_volatility': normal_period_vol,
                'policy_type_impact': policy_type_avg_impact,
                'policy_events_analyzed': len(policy_events),
                'policy_impact_periods': len(policy_impact_periods)
            }

            logger.info(f"政策波动率溢价: {policy_volatility_premium:.4f}")
            return result

        except Exception as e:
            logger.error(f"政策波动率溢价计算失败: {e}")
            return self._get_empty_policy_volatility_result()

    def analyze_fund_flow_volatility_coupling(self, price_data: pd.DataFrame,
                                            fund_flow_data: pd.DataFrame) -> Dict:
        """
        分析资金流-波动率耦合关系

        Args:
            price_data: 价格数据
            fund_flow_data: 资金流数据

        Returns:
            Dict: 耦合分析结果
        """
        try:
            logger.info("分析资金流-波动率耦合关系...")

            if price_data.empty or fund_flow_data.empty:
                return self._get_empty_coupling_result()

            # 数据预处理
            price_data = price_data.sort_values('date')
            fund_flow_data = fund_flow_data.sort_values('date')

            # 计算价格波动率
            price_data['log_return'] = np.log(price_data['close'] / price_data['close'].shift(1))
            price_data['realized_volatility'] = price_data['log_return'].rolling(
                window=self.coupling_params['flow_volatility_window']
            ).std() * np.sqrt(252)

            # 计算资金流波动率
            if 'net_flow' in fund_flow_data.columns:
                fund_flow_data['flow_volatility'] = fund_flow_data['net_flow'].rolling(
                    window=self.coupling_params['flow_volatility_window']
                ).std()
            else:
                logger.warning("资金流数据缺少net_flow列")
                return self._get_empty_coupling_result()

            # 合并数据
            merged_data = pd.merge(
                price_data[['date', 'realized_volatility']],
                fund_flow_data[['date', 'net_flow', 'flow_volatility']],
                on='date',
                how='inner'
            )

            if merged_data.empty:
                logger.warning("合并后数据为空")
                return self._get_empty_coupling_result()

            # 计算同期相关性
            contemporaneous_corr = merged_data['realized_volatility'].corr(merged_data['flow_volatility'])

            # 计算滞后相关性
            lag_correlations = {}
            for lag in self.coupling_params['lag_periods']:
                if len(merged_data) > lag:
                    lagged_flow_vol = merged_data['flow_volatility'].shift(lag)
                    lag_corr = merged_data['realized_volatility'].corr(lagged_flow_vol)
                    lag_correlations[f'lag_{lag}'] = lag_corr

            # 计算资金流对波动率的影响
            flow_impact_analysis = self._analyze_flow_impact_on_volatility(merged_data)

            # 计算耦合强度
            coupling_strength = abs(contemporaneous_corr)
            if coupling_strength > self.coupling_params['coupling_threshold']:
                coupling_regime = 'strong'
            elif coupling_strength > self.coupling_params['coupling_threshold'] / 2:
                coupling_regime = 'moderate'
            else:
                coupling_regime = 'weak'

            # 分析资金流方向对波动率的影响
            directional_impact = self._analyze_directional_flow_impact(merged_data)

            result = {
                'contemporaneous_correlation': contemporaneous_corr,
                'lag_correlations': lag_correlations,
                'coupling_strength': coupling_strength,
                'coupling_regime': coupling_regime,
                'flow_impact_analysis': flow_impact_analysis,
                'directional_impact': directional_impact,
                'data_points': len(merged_data),
                'analysis_period': {
                    'start': merged_data['date'].min().strftime('%Y-%m-%d'),
                    'end': merged_data['date'].max().strftime('%Y-%m-%d')
                }
            }

            logger.info(f"资金流-波动率耦合强度: {coupling_strength:.3f} ({coupling_regime})")
            return result

        except Exception as e:
            logger.error(f"资金流-波动率耦合分析失败: {e}")
            return self._get_empty_coupling_result()

    def identify_volatility_regime(self, price_data: pd.DataFrame,
                                 window: int = 20) -> Dict:
        """
        识别波动率制度

        Args:
            price_data: 价格数据
            window: 滚动窗口大小

        Returns:
            Dict: 波动率制度分析结果
        """
        try:
            logger.info("识别波动率制度...")

            if price_data.empty or len(price_data) < window:
                return self._get_empty_regime_result()

            # 计算滚动波动率
            price_data = price_data.sort_values('date')
            price_data['log_return'] = np.log(price_data['close'] / price_data['close'].shift(1))
            price_data['rolling_volatility'] = price_data['log_return'].rolling(window=window).std() * np.sqrt(252)

            # 当前波动率
            current_volatility = price_data['rolling_volatility'].iloc[-1]

            # 识别当前制度
            current_regime = self._classify_volatility_regime(current_volatility)

            # 分析制度历史
            regime_history = []
            for _, row in price_data.dropna().iterrows():
                regime = self._classify_volatility_regime(row['rolling_volatility'])
                regime_history.append({
                    'date': row['date'].strftime('%Y-%m-%d'),
                    'volatility': row['rolling_volatility'],
                    'regime': regime
                })

            # 计算制度统计
            regime_stats = self._calculate_regime_statistics(regime_history)

            # 预测制度切换概率
            regime_transition_prob = self._estimate_regime_transition_probability(regime_history)

            result = {
                'current_volatility': current_volatility,
                'current_regime': current_regime,
                'regime_history': regime_history[-30:],  # 最近30个观测
                'regime_statistics': regime_stats,
                'regime_transition_probability': regime_transition_prob,
                'volatility_percentile': self._calculate_volatility_percentile(price_data['rolling_volatility'])
            }

            logger.info(f"当前波动率制度: {current_regime} (波动率: {current_volatility:.3f})")
            return result

        except Exception as e:
            logger.error(f"波动率制度识别失败: {e}")
            return self._get_empty_regime_result()

    def _perform_garch_diagnostics(self, fitted_model, returns: pd.Series) -> Dict:
        """执行GARCH模型诊断"""
        try:
            # 计算残差
            residuals = fitted_model.resid
            standardized_residuals = fitted_model.std_resid

            # Ljung-Box检验（残差自相关）
            from statsmodels.stats.diagnostic import acorr_ljungbox
            ljung_box_result = acorr_ljungbox(residuals, lags=10, return_df=True)

            # ARCH效应检验
            arch_test_pvalue = ljung_box_result['lb_pvalue'].iloc[-1]

            diagnostics = {
                'ljung_box_pvalue': float(arch_test_pvalue),
                'residual_mean': float(residuals.mean()),
                'residual_std': float(residuals.std()),
                'standardized_residual_mean': float(standardized_residuals.mean()),
                'standardized_residual_std': float(standardized_residuals.std()),
                'model_convergence': fitted_model.convergence_flag == 0
            }

            return diagnostics

        except Exception as e:
            logger.warning(f"GARCH诊断失败: {e}")
            return {
                'ljung_box_pvalue': 0.5,
                'residual_mean': 0.0,
                'residual_std': 1.0,
                'standardized_residual_mean': 0.0,
                'standardized_residual_std': 1.0,
                'model_convergence': False
            }

    def _analyze_flow_impact_on_volatility(self, merged_data: pd.DataFrame) -> Dict:
        """分析资金流对波动率的影响"""
        try:
            # 计算资金流强度分位数
            flow_quantiles = merged_data['net_flow'].quantile([0.25, 0.5, 0.75])

            # 分组分析
            low_flow = merged_data[merged_data['net_flow'] <= flow_quantiles[0.25]]
            medium_flow = merged_data[
                (merged_data['net_flow'] > flow_quantiles[0.25]) &
                (merged_data['net_flow'] <= flow_quantiles[0.75])
            ]
            high_flow = merged_data[merged_data['net_flow'] > flow_quantiles[0.75]]

            impact_analysis = {
                'low_flow_avg_volatility': low_flow['realized_volatility'].mean() if not low_flow.empty else 0.0,
                'medium_flow_avg_volatility': medium_flow['realized_volatility'].mean() if not medium_flow.empty else 0.0,
                'high_flow_avg_volatility': high_flow['realized_volatility'].mean() if not high_flow.empty else 0.0,
                'flow_volatility_elasticity': self._calculate_elasticity(
                    merged_data['net_flow'], merged_data['realized_volatility']
                )
            }

            return impact_analysis

        except Exception as e:
            logger.warning(f"资金流影响分析失败: {e}")
            return {
                'low_flow_avg_volatility': 0.0,
                'medium_flow_avg_volatility': 0.0,
                'high_flow_avg_volatility': 0.0,
                'flow_volatility_elasticity': 0.0
            }

    def _analyze_directional_flow_impact(self, merged_data: pd.DataFrame) -> Dict:
        """分析资金流方向对波动率的影响"""
        try:
            # 分离流入和流出
            inflow_data = merged_data[merged_data['net_flow'] > 0]
            outflow_data = merged_data[merged_data['net_flow'] < 0]

            directional_impact = {
                'inflow_avg_volatility': inflow_data['realized_volatility'].mean() if not inflow_data.empty else 0.0,
                'outflow_avg_volatility': outflow_data['realized_volatility'].mean() if not outflow_data.empty else 0.0,
                'inflow_count': len(inflow_data),
                'outflow_count': len(outflow_data),
                'asymmetry_ratio': 0.0
            }

            # 计算不对称性
            if directional_impact['outflow_avg_volatility'] > 0:
                directional_impact['asymmetry_ratio'] = (
                    directional_impact['inflow_avg_volatility'] /
                    directional_impact['outflow_avg_volatility']
                )

            return directional_impact

        except Exception as e:
            logger.warning(f"方向性影响分析失败: {e}")
            return {
                'inflow_avg_volatility': 0.0,
                'outflow_avg_volatility': 0.0,
                'inflow_count': 0,
                'outflow_count': 0,
                'asymmetry_ratio': 1.0
            }

    def _classify_volatility_regime(self, volatility: float) -> str:
        """分类波动率制度"""
        if volatility <= self.volatility_regimes['very_low']:
            return 'very_low'
        elif volatility <= self.volatility_regimes['low']:
            return 'low'
        elif volatility <= self.volatility_regimes['normal']:
            return 'normal'
        elif volatility <= self.volatility_regimes['high']:
            return 'high'
        else:
            return 'very_high'

    def _calculate_regime_statistics(self, regime_history: List[Dict]) -> Dict:
        """计算制度统计"""
        try:
            if not regime_history:
                return {}

            # 统计各制度出现频率
            regime_counts = {}
            for record in regime_history:
                regime = record['regime']
                regime_counts[regime] = regime_counts.get(regime, 0) + 1

            total_periods = len(regime_history)
            regime_frequencies = {
                regime: count / total_periods
                for regime, count in regime_counts.items()
            }

            # 计算平均持续期
            regime_durations = self._calculate_regime_durations(regime_history)

            return {
                'regime_frequencies': regime_frequencies,
                'regime_durations': regime_durations,
                'total_periods': total_periods
            }

        except Exception as e:
            logger.warning(f"制度统计计算失败: {e}")
            return {}

    def _calculate_regime_durations(self, regime_history: List[Dict]) -> Dict:
        """计算制度持续期"""
        try:
            durations = {}
            current_regime = None
            current_duration = 0

            for record in regime_history:
                regime = record['regime']

                if regime == current_regime:
                    current_duration += 1
                else:
                    if current_regime is not None:
                        if current_regime not in durations:
                            durations[current_regime] = []
                        durations[current_regime].append(current_duration)

                    current_regime = regime
                    current_duration = 1

            # 添加最后一个制度的持续期
            if current_regime is not None:
                if current_regime not in durations:
                    durations[current_regime] = []
                durations[current_regime].append(current_duration)

            # 计算平均持续期
            avg_durations = {}
            for regime, duration_list in durations.items():
                avg_durations[regime] = np.mean(duration_list)

            return avg_durations

        except Exception as e:
            logger.warning(f"制度持续期计算失败: {e}")
            return {}

    def _estimate_regime_transition_probability(self, regime_history: List[Dict]) -> Dict:
        """估计制度转换概率"""
        try:
            if len(regime_history) < 2:
                return {}

            # 构建转换矩阵
            transitions = {}
            for i in range(len(regime_history) - 1):
                current_regime = regime_history[i]['regime']
                next_regime = regime_history[i + 1]['regime']

                if current_regime not in transitions:
                    transitions[current_regime] = {}

                if next_regime not in transitions[current_regime]:
                    transitions[current_regime][next_regime] = 0

                transitions[current_regime][next_regime] += 1

            # 计算转换概率
            transition_probabilities = {}
            for current_regime, next_regimes in transitions.items():
                total_transitions = sum(next_regimes.values())
                transition_probabilities[current_regime] = {
                    next_regime: count / total_transitions
                    for next_regime, count in next_regimes.items()
                }

            return transition_probabilities

        except Exception as e:
            logger.warning(f"制度转换概率估计失败: {e}")
            return {}

    def _calculate_volatility_percentile(self, volatility_series: pd.Series) -> float:
        """计算当前波动率的历史分位数"""
        try:
            if volatility_series.empty:
                return 0.5

            current_vol = volatility_series.iloc[-1]
            percentile = stats.percentileofscore(volatility_series.dropna(), current_vol) / 100

            return percentile

        except Exception as e:
            logger.warning(f"波动率分位数计算失败: {e}")
            return 0.5

    def _calculate_elasticity(self, x: pd.Series, y: pd.Series) -> float:
        """计算弹性系数"""
        try:
            if len(x) != len(y) or len(x) < 2:
                return 0.0

            # 计算对数变化率
            x_clean = x.replace([np.inf, -np.inf], np.nan).dropna()
            y_clean = y.replace([np.inf, -np.inf], np.nan).dropna()

            if len(x_clean) < 2 or len(y_clean) < 2:
                return 0.0

            # 简化的弹性计算
            x_change = x_clean.pct_change().dropna()
            y_change = y_clean.pct_change().dropna()

            if len(x_change) == 0 or len(y_change) == 0:
                return 0.0

            # 计算相关性作为弹性的代理
            correlation = x_change.corr(y_change)
            return correlation if not np.isnan(correlation) else 0.0

        except Exception as e:
            logger.warning(f"弹性计算失败: {e}")
            return 0.0

    def _build_volatility_features(self, price_data: pd.DataFrame,
                                 fund_flow_data: pd.DataFrame,
                                 sentiment_data: Dict) -> pd.DataFrame:
        """构建波动率预测特征"""
        try:
            if price_data.empty:
                return pd.DataFrame()

            # 基础价格特征
            price_data = price_data.sort_values('date')
            price_data['log_return'] = np.log(price_data['close'] / price_data['close'].shift(1))
            price_data['realized_vol'] = price_data['log_return'].rolling(window=5).std() * np.sqrt(252)
            price_data['return_lag1'] = price_data['log_return'].shift(1)
            price_data['return_lag2'] = price_data['log_return'].shift(2)
            price_data['vol_lag1'] = price_data['realized_vol'].shift(1)

            # 合并资金流特征
            if not fund_flow_data.empty:
                fund_flow_data = fund_flow_data.sort_values('date')
                if 'net_flow' in fund_flow_data.columns:
                    fund_flow_data['flow_normalized'] = (
                        fund_flow_data['net_flow'] / fund_flow_data['net_flow'].abs().max()
                    )

                    price_data = pd.merge(
                        price_data,
                        fund_flow_data[['date', 'flow_normalized']],
                        on='date',
                        how='left'
                    )
                    price_data['flow_normalized'] = price_data['flow_normalized'].fillna(0)
            else:
                price_data['flow_normalized'] = 0

            # 情绪特征
            if sentiment_data and 'sentiment_score' in sentiment_data:
                price_data['sentiment_score'] = sentiment_data['sentiment_score']
            else:
                price_data['sentiment_score'] = 0

            # 选择特征列
            feature_columns = [
                'realized_vol', 'return_lag1', 'return_lag2', 'vol_lag1',
                'flow_normalized', 'sentiment_score'
            ]

            features_df = price_data[['date'] + feature_columns].dropna()

            return features_df

        except Exception as e:
            logger.warning(f"特征构建失败: {e}")
            return pd.DataFrame()

    def _prepare_training_data(self, features_df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """准备训练数据"""
        try:
            if features_df.empty or len(features_df) < 2:
                return np.array([]), np.array([])

            # 特征矩阵（除了日期和目标变量）
            feature_cols = [col for col in features_df.columns if col not in ['date', 'realized_vol']]
            X = features_df[feature_cols].values

            # 目标变量（下一期波动率）
            y = features_df['realized_vol'].shift(-1).dropna().values

            # 确保X和y长度一致
            min_len = min(len(X), len(y))
            X = X[:min_len]
            y = y[:min_len]

            # 标准化特征
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            return X_scaled, y

        except Exception as e:
            logger.warning(f"训练数据准备失败: {e}")
            return np.array([]), np.array([])

    def _get_empty_garch_result(self) -> Dict:
        """获取空的GARCH结果"""
        return {
            'volatility_forecast': [0.0],
            'annualized_volatility': [0.0],
            'mean_forecast': [0.0],
            'optimal_parameters': {'p': 1, 'q': 1, 'distribution': 'normal'},
            'model_fit_quality': {'aic': np.inf, 'bic': np.inf, 'log_likelihood': -np.inf},
            'model_diagnostics': {},
            'forecast_horizon': 1,
            'current_volatility': 0.0
        }

    def _get_empty_policy_volatility_result(self) -> Dict:
        """获取空的政策波动率结果"""
        return {
            'policy_volatility_premium': 0.0,
            'policy_volatility_ratio': 1.0,
            'policy_period_volatility': 0.0,
            'normal_period_volatility': 0.0,
            'policy_type_impact': {},
            'policy_events_analyzed': 0,
            'policy_impact_periods': 0
        }

    def _get_empty_coupling_result(self) -> Dict:
        """获取空的耦合结果"""
        return {
            'contemporaneous_correlation': 0.0,
            'lag_correlations': {},
            'coupling_strength': 0.0,
            'coupling_regime': 'weak',
            'flow_impact_analysis': {},
            'directional_impact': {},
            'data_points': 0,
            'analysis_period': {'start': '', 'end': ''}
        }

    def _get_empty_regime_result(self) -> Dict:
        """获取空的制度结果"""
        return {
            'current_volatility': 0.0,
            'current_regime': 'normal',
            'regime_history': [],
            'regime_statistics': {},
            'regime_transition_probability': {},
            'volatility_percentile': 0.5
        }

    def _get_empty_forecast_result(self) -> Dict:
        """获取空的预测结果"""
        return {
            'volatility_forecast': [0.0],
            'confidence_intervals': [{'lower': 0.0, 'upper': 0.0}],
            'feature_importance': {},
            'model_score': 0.0,
            'forecast_horizon': 1,
            'training_samples': 0,
            'current_volatility': 0.0
        }
