"""
Tiered Fund Flow Analyzer module.
Provides detailed analysis of fund flows across five tiers.
"""

import pandas as pd
import akshare as ak
from datetime import datetime

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import retry
from utils.cache_utils import cached

class TieredFundFlowAnalyzer:
    """
    Class for analyzing fund flows across five tiers.
    """

    def __init__(self, config=None):
        """
        Initialize the TieredFundFlowAnalyzer.

        Args:
            config: Configuration object or None to use default.
        """
        self.config = config if config else config_loader

        # Load settings from configuration
        self.lookback_days = self.config.get('engines.tiered_fund_flow.lookback_days', 30)

        # Initialize northbound alternative
        self.northbound_alternative = NorthboundAlternative(self.config)

        logger.info("TieredFundFlowAnalyzer initialized")

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_margin_flow(self, stock_code, start_date, end_date):
        """
        Get margin trading flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing margin flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Format stock code
            if stock_code.startswith('6'):
                exchange = 'sse'  # Shanghai Stock Exchange
            else:
                exchange = 'szse'  # Shenzhen Stock Exchange

            # Get margin data using real APIs
            margin_df = pd.DataFrame()

            if exchange == 'sse':
                try:
                    # Convert date to string format if needed
                    if isinstance(start_date, str):
                        date_str = start_date.replace('-', '')
                    else:
                        date_str = start_date.strftime('%Y%m%d')

                    # Get SSE margin detail data
                    margin_df = ak.stock_margin_detail_sse(date=date_str)

                    if not margin_df.empty:
                        # Filter for the specific stock
                        if '标的证券代码' in margin_df.columns:
                            margin_df = margin_df[margin_df['标的证券代码'] == stock_code]
                        elif '股票代码' in margin_df.columns:
                            margin_df = margin_df[margin_df['股票代码'] == stock_code]

                        logger.debug(f"Retrieved SSE margin data for {stock_code}: {len(margin_df)} records")
                    else:
                        logger.debug(f"No SSE margin data available for {stock_code}")

                except Exception as e:
                    logger.debug(f"SSE margin API error for {stock_code}: {str(e)}")
                    margin_df = pd.DataFrame()
            else:
                try:
                    # Convert date to string format if needed
                    if isinstance(start_date, str):
                        date_str = start_date.replace('-', '')
                    else:
                        date_str = start_date.strftime('%Y%m%d')

                    # Get SZSE margin detail data
                    margin_df = ak.stock_margin_detail_szse(date=date_str)

                    if not margin_df.empty:
                        # Filter for the specific stock
                        if '证券代码' in margin_df.columns:
                            margin_df = margin_df[margin_df['证券代码'] == stock_code]
                        elif '股票代码' in margin_df.columns:
                            margin_df = margin_df[margin_df['股票代码'] == stock_code]

                        logger.debug(f"Retrieved SZSE margin data for {stock_code}: {len(margin_df)} records")
                    else:
                        logger.debug(f"No SZSE margin data available for {stock_code}")

                except Exception as e:
                    logger.debug(f"SZSE margin API error for {stock_code}: {str(e)}")
                    margin_df = pd.DataFrame()

            # Process margin data if available
            if not margin_df.empty:
                # Extract relevant data from margin_df
                data = []

                # Process each row in the margin data
                for _, row in margin_df.iterrows():
                    # Extract actual margin data based on column names
                    if '信用交易日期' in row:
                        date_str = str(row['信用交易日期'])
                        try:
                            date = pd.to_datetime(date_str, format='%Y%m%d').date()
                        except:
                            date = pd.Timestamp.now().date()
                    else:
                        date = pd.Timestamp.now().date()

                    # Extract margin data
                    margin_balance = row.get('融资余额', 0)
                    margin_buy = row.get('融资买入额', 0)
                    margin_repay = row.get('融资偿还额', 0)
                    short_balance = row.get('融券余量', 0)
                    short_sell = row.get('融券卖出量', 0)
                    short_repay = row.get('融券偿还量', 0)

                    # Calculate net flows
                    margin_net_buy = margin_buy - margin_repay
                    short_net_sell = short_sell - short_repay

                    data.append({
                        'date': date,
                        'stock_code': stock_code,
                        'margin_balance': margin_balance,
                        'margin_net_buy': margin_net_buy,
                        'short_balance': short_balance,
                        'short_net_sell': short_net_sell
                    })

                result_df = pd.DataFrame(data)
                logger.debug(f"Processed {len(result_df)} margin records for {stock_code}")
            else:
                logger.warning(f"No margin data available for {stock_code}")
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting margin flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'margin_balance', 'margin_net_buy', 'short_balance', 'short_net_sell'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_institutional_flow(self, stock_code, start_date, end_date):
        """
        Get institutional flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing institutional flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Try to get fund portfolio data
            try:
                # This is a placeholder, in a real system you would use actual fund portfolio data
                fund_portfolio_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting fund portfolio data: {str(e)}")
                fund_portfolio_df = pd.DataFrame()

            # Try to get LHB institutional data
            try:
                # This is a placeholder, in a real system you would use actual LHB data
                lhb_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting LHB data: {str(e)}")
                lhb_df = pd.DataFrame()

            # Process real data if available
            if not fund_portfolio_df.empty or not lhb_df.empty:
                # Process fund portfolio data
                if not fund_portfolio_df.empty:
                    # Extract institutional flow from fund portfolio data
                    # This would need to be implemented based on actual data structure
                    logger.debug(f"Processing fund portfolio data for {stock_code}")

                # Process LHB data
                if not lhb_df.empty:
                    # Extract institutional net buy from LHB data
                    # This would need to be implemented based on actual data structure
                    logger.debug(f"Processing LHB data for {stock_code}")

                result_df = pd.DataFrame()  # Placeholder for processed real data
            else:
                logger.warning(f"No institutional flow data available for {stock_code}")
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting institutional flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'mutual_fund_flow_est', 'lhb_institutional_net_buy'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_hot_money_flow(self, stock_code, start_date, end_date):
        """
        Get hot money flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing hot money flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Try to get LHB data for hot money
            try:
                # This is a placeholder, in a real system you would use actual LHB data
                lhb_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting LHB data for hot money: {str(e)}")
                lhb_df = pd.DataFrame()

            # Process real LHB data if available
            if not lhb_df.empty:
                # Extract hot money activity from LHB data
                # This would need to be implemented based on actual LHB data structure
                logger.debug(f"Processing LHB data for hot money analysis for {stock_code}")
                result_df = pd.DataFrame()  # Placeholder for processed real data
            else:
                logger.warning(f"No LHB data available for hot money analysis for {stock_code}")
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting hot money flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'hot_money_active_score', 'hot_money_net_buy'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_retail_flow(self, stock_code, start_date, end_date):
        """
        Get retail flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing retail flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Try to get shareholder data
            try:
                # This is a placeholder, in a real system you would use actual shareholder data
                shareholder_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting shareholder data: {str(e)}")
                shareholder_df = pd.DataFrame()

            # Try to get small order flow data
            try:
                # This is a placeholder, in a real system you would use actual small order flow data
                small_order_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting small order flow data: {str(e)}")
                small_order_df = pd.DataFrame()

            # Process real data if available
            if not shareholder_df.empty or not small_order_df.empty:
                # Process shareholder data
                if not shareholder_df.empty:
                    logger.debug(f"Processing shareholder data for {stock_code}")

                # Process small order flow data
                if not small_order_df.empty:
                    logger.debug(f"Processing small order flow data for {stock_code}")

                result_df = pd.DataFrame()  # Placeholder for processed real data
            else:
                logger.warning(f"No retail flow data available for {stock_code}")
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting retail flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'shareholder_change_rate', 'avg_holding_value', 'small_order_net_flow'])

    def fetch_all_tiered_flows_for_stock(self, stock_code, start_date, end_date):
        """
        Fetch all tiered flows for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            dict: Dictionary containing all tiered flows.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get real northbound flow
            northbound_df = self.northbound_alternative.get_northbound_flow(start_date, end_date)

            # Get margin flow
            margin_df = self.get_margin_flow(stock_code, start_date, end_date)

            # Get institutional flow
            institutional_df = self.get_institutional_flow(stock_code, start_date, end_date)

            # Get hot money flow
            hot_money_df = self.get_hot_money_flow(stock_code, start_date, end_date)

            # Get retail flow
            retail_df = self.get_retail_flow(stock_code, start_date, end_date)

            # Return all flows in a dictionary
            return {
                'northbound': northbound_df,
                'margin': margin_df,
                'institutional': institutional_df,
                'hot_money': hot_money_df,
                'retail': retail_df
            }
        except Exception as e:
            logger.error(f"Error fetching all tiered flows for {stock_code}: {str(e)}")
            return {}

    def calculate_stock_tiered_flow_score(self, stock_code, tiered_flow_data_dict, stock_info):
        """
        Calculate tiered flow score for a stock.

        Args:
            stock_code (str): Stock code.
            tiered_flow_data_dict (dict): Dictionary containing tiered flow data.
            stock_info (dict): Dictionary containing stock information.

        Returns:
            float: Tiered flow score.
        """
        try:
            # Check if tiered flow data is available
            if not tiered_flow_data_dict:
                logger.warning(f"No tiered flow data available for {stock_code}")
                return 0.0

            # Get market cap category
            market_cap = stock_info.get('market_cap', 0)
            if market_cap > 1e11:  # > 100 billion
                market_cap_category = 'large_cap'
            elif market_cap > 1e10:  # > 10 billion
                market_cap_category = 'mid_cap'
            else:
                market_cap_category = 'small_cap'

            # Get flow weights based on market cap category
            flow_weights = self.config.get(f'engines.tiered_fund_flow.flow_weights.{market_cap_category}', {})

            # Default weights if not found in config
            if not flow_weights:
                flow_weights = {
                    'northbound': 0.2,
                    'institutional': 0.2,
                    'margin': 0.2,
                    'hot_money': 0.2,
                    'retail': 0.2
                }

            # Calculate scores for each tier
            scores = {}

            # Northbound score
            if 'northbound' in tiered_flow_data_dict and not tiered_flow_data_dict['northbound'].empty:
                northbound_df = tiered_flow_data_dict['northbound']
                # Calculate score based on recent flow and trend
                if 'northbound_flow' in northbound_df.columns:
                    recent_flow = northbound_df['northbound_flow'].iloc[-5:].mean()
                    flow_std = northbound_df['northbound_flow'].std()
                    if flow_std == 0:
                        northbound_score = 0.5  # Neutral if no variation
                    else:
                        northbound_score = 0.5 + 0.5 * (recent_flow / (flow_std * 3))  # Normalize to [0, 1]
                        northbound_score = max(0, min(1, northbound_score))  # Clip to [0, 1]
                    scores['northbound'] = northbound_score
                else:
                    scores['northbound'] = 0.5  # Neutral if no flow data
            else:
                scores['northbound'] = 0.5  # Neutral if no data

            # Margin score
            if 'margin' in tiered_flow_data_dict and not tiered_flow_data_dict['margin'].empty:
                margin_df = tiered_flow_data_dict['margin']
                # Calculate score based on recent net buy and trend
                recent_net_buy = margin_df['margin_net_buy'].iloc[-5:].mean()
                net_buy_std = margin_df['margin_net_buy'].std()
                if net_buy_std == 0:
                    margin_score = 0.5  # Neutral if no variation
                else:
                    margin_score = 0.5 + 0.5 * (recent_net_buy / (net_buy_std * 3))  # Normalize to [0, 1]
                    margin_score = max(0, min(1, margin_score))  # Clip to [0, 1]
                scores['margin'] = margin_score
            else:
                scores['margin'] = 0.5  # Neutral if no data

            # Institutional score
            if 'institutional' in tiered_flow_data_dict and not tiered_flow_data_dict['institutional'].empty:
                institutional_df = tiered_flow_data_dict['institutional']
                # Calculate score based on recent flows
                recent_mutual_fund_flow = institutional_df['mutual_fund_flow_est'].iloc[-5:].mean()
                recent_lhb_flow = institutional_df['lhb_institutional_net_buy'].iloc[-5:].mean()

                # Combine flows
                combined_flow = recent_mutual_fund_flow + recent_lhb_flow
                flow_std = institutional_df['mutual_fund_flow_est'].std() + institutional_df['lhb_institutional_net_buy'].std()

                if flow_std == 0:
                    institutional_score = 0.5  # Neutral if no variation
                else:
                    institutional_score = 0.5 + 0.5 * (combined_flow / (flow_std * 3))  # Normalize to [0, 1]
                    institutional_score = max(0, min(1, institutional_score))  # Clip to [0, 1]
                scores['institutional'] = institutional_score
            else:
                scores['institutional'] = 0.5  # Neutral if no data

            # Hot money score
            if 'hot_money' in tiered_flow_data_dict and not tiered_flow_data_dict['hot_money'].empty:
                hot_money_df = tiered_flow_data_dict['hot_money']
                # Calculate score based on recent net buy and activity
                recent_net_buy = hot_money_df['hot_money_net_buy'].iloc[-5:].mean()
                recent_activity = hot_money_df['hot_money_active_score'].iloc[-5:].mean()

                # Combine metrics
                net_buy_std = hot_money_df['hot_money_net_buy'].std()
                if net_buy_std == 0:
                    net_buy_score = 0.5  # Neutral if no variation
                else:
                    net_buy_score = 0.5 + 0.5 * (recent_net_buy / (net_buy_std * 3))  # Normalize to [0, 1]
                    net_buy_score = max(0, min(1, net_buy_score))  # Clip to [0, 1]

                # Combine scores
                hot_money_score = 0.7 * net_buy_score + 0.3 * recent_activity
                scores['hot_money'] = hot_money_score
            else:
                scores['hot_money'] = 0.5  # Neutral if no data

            # Retail score
            if 'retail' in tiered_flow_data_dict and not tiered_flow_data_dict['retail'].empty:
                retail_df = tiered_flow_data_dict['retail']
                # Calculate score based on shareholder changes and small order flow
                recent_shareholder_change = retail_df['shareholder_change_rate'].iloc[-5:].mean()
                recent_small_order_flow = retail_df['small_order_net_flow'].iloc[-5:].mean()

                # Combine metrics
                shareholder_change_std = retail_df['shareholder_change_rate'].std()
                small_order_flow_std = retail_df['small_order_net_flow'].std()

                if shareholder_change_std == 0:
                    shareholder_score = 0.5  # Neutral if no variation
                else:
                    shareholder_score = 0.5 + 0.5 * (recent_shareholder_change / (shareholder_change_std * 3))  # Normalize to [0, 1]
                    shareholder_score = max(0, min(1, shareholder_score))  # Clip to [0, 1]

                if small_order_flow_std == 0:
                    small_order_score = 0.5  # Neutral if no variation
                else:
                    small_order_score = 0.5 + 0.5 * (recent_small_order_flow / (small_order_flow_std * 3))  # Normalize to [0, 1]
                    small_order_score = max(0, min(1, small_order_score))  # Clip to [0, 1]

                # Combine scores
                retail_score = 0.5 * shareholder_score + 0.5 * small_order_score
                scores['retail'] = retail_score
            else:
                scores['retail'] = 0.5  # Neutral if no data

            # Calculate final score
            final_score = 0.0
            total_weight = 0.0

            for tier, score in scores.items():
                weight = flow_weights.get(tier, 0.2)  # Default weight is 0.2
                final_score += weight * score
                total_weight += weight

            if total_weight > 0:
                final_score /= total_weight
            else:
                final_score = 0.5  # Neutral if no weights

            return final_score
        except Exception as e:
            logger.error(f"Error calculating tiered flow score for {stock_code}: {str(e)}")
            return 0.5  # Neutral score on error

class NorthboundAlternative:
    """
    Class for getting real northbound fund flow data.
    """

    def __init__(self, config=None):
        """
        Initialize the NorthboundAlternative.

        Args:
            config: Configuration object or None to use default.
        """
        self.config = config if config else config_loader
        logger.info("NorthboundAlternative initialized with real API")

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_northbound_flow(self, start_date, end_date):
        """
        Get real northbound fund flow data.

        Args:
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing northbound flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get real northbound fund flow data using AKShare
            try:
                # Get northbound fund flow summary
                flow_df = ak.stock_hsgt_fund_flow_summary_em()

                if not flow_df.empty:
                    # Filter for northbound data
                    northbound_data = flow_df[flow_df['资金方向'] == '北向']

                    if not northbound_data.empty:
                        # Extract flow data
                        net_flow = northbound_data['资金净流入'].sum()

                        # Create result dataframe
                        result_data = [{
                            'date': pd.Timestamp.now().date(),
                            'northbound_flow': net_flow,
                            'confidence_level': 0.9  # High confidence for real data
                        }]

                        df = pd.DataFrame(result_data)
                        logger.debug(f"Retrieved real northbound flow: {net_flow}")
                        return df

            except Exception as e:
                logger.warning(f"Error getting northbound flow from AKShare: {str(e)}")

            # Return empty dataframe if no data available
            return pd.DataFrame(columns=['date', 'northbound_flow', 'confidence_level'])

        except Exception as e:
            logger.error(f"Error getting northbound flow: {str(e)}")
            return pd.DataFrame(columns=['date', 'northbound_flow', 'confidence_level'])


