#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统健康度检查脚本
快速检查系统各组件的运行状态
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_status(component, status, details=""):
    """打印组件状态"""
    status_icon = "✅" if status == "OK" else "⚠️" if status == "WARNING" else "❌"
    print(f"{status_icon} {component:<25} [{status}] {details}")

def check_imports():
    """检查关键模块导入"""
    print_header("模块导入检查")
    
    modules = [
        ("pandas", "数据处理"),
        ("numpy", "数值计算"),
        ("akshare", "数据源API"),
        ("transformers", "NLP模型"),
        ("yaml", "配置文件"),
        ("jieba", "中文分词")
    ]
    
    import_status = {}
    
    for module, desc in modules:
        try:
            __import__(module)
            print_status(f"{module} ({desc})", "OK")
            import_status[module] = True
        except ImportError as e:
            print_status(f"{module} ({desc})", "ERROR", str(e))
            import_status[module] = False
    
    return import_status

def check_config():
    """检查配置文件"""
    print_header("配置文件检查")
    
    try:
        from utils.config_loader import config_loader
        config = config_loader.get_config()
        print_status("配置文件加载", "OK", "config/config.yaml")
        
        # 检查关键配置项
        key_configs = [
            ("engines.sentiment.model_path", "情绪分析模型路径"),
            ("paths.data_dir", "数据目录"),
            ("paths.logs_dir", "日志目录"),
            ("decision_engine.top_n", "推荐股票数量")
        ]
        
        for key, desc in key_configs:
            value = config_loader.get(key)
            if value is not None:
                print_status(f"{desc}", "OK", f"{key}={value}")
            else:
                print_status(f"{desc}", "WARNING", f"{key} 未配置")
                
        return True
    except Exception as e:
        print_status("配置文件加载", "ERROR", str(e))
        return False

def check_models():
    """检查模型文件"""
    print_header("模型文件检查")
    
    model_paths = [
        ("models/finbert", "FinBERT情绪分析模型"),
        ("data/sentiment_dict.json", "情绪词典"),
        ("data/policy_industry_mapping.json", "政策行业映射")
    ]
    
    model_status = {}
    
    for path, desc in model_paths:
        if os.path.exists(path):
            if os.path.isdir(path):
                files = os.listdir(path)
                print_status(desc, "OK", f"{len(files)} 个文件")
            else:
                size = os.path.getsize(path)
                print_status(desc, "OK", f"{size} bytes")
            model_status[path] = True
        else:
            print_status(desc, "WARNING", f"路径不存在: {path}")
            model_status[path] = False
    
    return model_status

def check_engines():
    """检查分析引擎"""
    print_header("分析引擎检查")
    
    engines = [
        ("engines.sentiment.analyzer", "SentimentAnalyzer", "情绪分析引擎"),
        ("engines.volatility.analyzer", "VolatilityAnalyzer", "波动率分析引擎"),
        ("engines.tiered_fund_flow.analyzer", "TieredFundFlowAnalyzer", "资金流分析引擎"),
        ("engines.news_policy.analyzer", "PolicyAnalyzer", "政策分析引擎")
    ]
    
    engine_status = {}
    
    for module_path, class_name, desc in engines:
        try:
            module = __import__(module_path, fromlist=[class_name])
            engine_class = getattr(module, class_name)
            
            # 尝试初始化
            start_time = time.time()
            engine = engine_class()
            init_time = time.time() - start_time
            
            print_status(desc, "OK", f"初始化耗时 {init_time:.2f}s")
            engine_status[class_name] = True
            
        except Exception as e:
            print_status(desc, "ERROR", str(e))
            engine_status[class_name] = False
    
    return engine_status

def check_data_sources():
    """检查数据源连接"""
    print_header("数据源连接检查")
    
    try:
        import akshare as ak
        
        # 测试基本API调用
        tests = [
            ("股票列表", lambda: ak.stock_info_a_code_name()),
            ("行业分类", lambda: ak.stock_sector_spot()),
            ("市场指数", lambda: ak.stock_zh_index_spot())
        ]
        
        for test_name, test_func in tests:
            try:
                start_time = time.time()
                result = test_func()
                response_time = time.time() - start_time
                
                if hasattr(result, '__len__') and len(result) > 0:
                    print_status(f"AKShare {test_name}", "OK", 
                               f"{len(result)}条记录, {response_time:.2f}s")
                else:
                    print_status(f"AKShare {test_name}", "WARNING", "返回数据为空")
                    
            except Exception as e:
                print_status(f"AKShare {test_name}", "ERROR", str(e))
                
    except ImportError:
        print_status("AKShare API", "ERROR", "模块未安装")

def check_directories():
    """检查目录结构"""
    print_header("目录结构检查")
    
    required_dirs = [
        ("data", "数据目录"),
        ("logs", "日志目录"),
        ("models", "模型目录"),
        ("config", "配置目录"),
        ("engines", "引擎目录"),
        ("utils", "工具目录")
    ]
    
    for dir_path, desc in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            files_count = len(os.listdir(dir_path))
            print_status(desc, "OK", f"{files_count} 个文件/目录")
        else:
            print_status(desc, "WARNING", f"目录不存在: {dir_path}")

def generate_summary(import_status, config_status, model_status, engine_status):
    """生成健康度总结"""
    print_header("系统健康度总结")
    
    # 计算各项得分
    import_score = sum(import_status.values()) / len(import_status) * 100
    config_score = 100 if config_status else 0
    model_score = sum(model_status.values()) / len(model_status) * 100
    engine_score = sum(engine_status.values()) / len(engine_status) * 100
    
    # 总体得分
    overall_score = (import_score + config_score + model_score + engine_score) / 4
    
    print(f"📊 模块导入得分: {import_score:.1f}%")
    print(f"⚙️ 配置文件得分: {config_score:.1f}%")
    print(f"🤖 模型文件得分: {model_score:.1f}%")
    print(f"🔧 分析引擎得分: {engine_score:.1f}%")
    print(f"\n🎯 总体健康度: {overall_score:.1f}%")
    
    # 健康度评级
    if overall_score >= 90:
        grade = "优秀 ✅"
        recommendation = "系统运行状态良好，可以正常使用。"
    elif overall_score >= 70:
        grade = "良好 ⚠️"
        recommendation = "系统基本可用，建议修复部分问题以提升稳定性。"
    elif overall_score >= 50:
        grade = "一般 ⚠️"
        recommendation = "系统存在较多问题，建议进行系统性修复。"
    else:
        grade = "较差 ❌"
        recommendation = "系统存在严重问题，需要立即修复才能正常使用。"
    
    print(f"\n📈 健康度评级: {grade}")
    print(f"💡 建议: {recommendation}")

def main():
    """主函数"""
    print("🏥 政策-流动性-波动率套利系统健康检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项检查
    import_status = check_imports()
    config_status = check_config()
    model_status = check_models()
    engine_status = check_engines()
    
    # 检查数据源（可选，因为可能较慢）
    print("\n是否检查数据源连接？(可能需要较长时间) [y/N]: ", end="")
    if input().lower().startswith('y'):
        check_data_sources()
    
    # 检查目录结构
    check_directories()
    
    # 生成总结
    generate_summary(import_status, config_status, model_status, engine_status)
    
    print("\n" + "="*60)
    print("✅ 健康检查完成！")
    print("💡 如需详细诊断，请运行: python test_fixes.py")
    print("📖 查看修复指南: docs/quick_fix_guide.md")

if __name__ == "__main__":
    main()
