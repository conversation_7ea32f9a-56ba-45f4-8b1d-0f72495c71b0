"""
简化版PolicyDataSource测试
"""

import pandas as pd
import akshare as ak

class SimplePolicyDataSource:
    """简化版政策数据源类"""
    
    def __init__(self):
        """初始化政策数据源"""
        self.default_lookback_days = 7
        print("SimplePolicyDataSource initialized")
    
    def get_financial_breakfast(self):
        """获取财经早餐-东方财富"""
        try:
            df = ak.stock_news_em()
            if not df.empty:
                df = df.rename(columns={
                    '新闻标题': 'title',
                    '新闻内容': 'content',
                    '发布时间': 'publish_date',
                    '新闻链接': 'url'
                })
                df['source'] = '东方财富财经早餐'
                print(f"获取到 {len(df)} 条财经早餐")
            return df
        except Exception as e:
            print(f"获取财经早餐失败: {str(e)}")
            return pd.DataFrame()
    
    def get_global_news_em(self):
        """获取全球财经快讯-东方财富"""
        try:
            df = ak.stock_news_main_cx()
            if not df.empty:
                df = df.rename(columns={
                    'summary': 'title',
                    'tag': 'content',
                    'pub_time': 'publish_date',
                    'url': 'url'
                })
                df['source'] = '东方财富全球快讯'
                print(f"获取到 {len(df)} 条全球财经快讯")
            return df
        except Exception as e:
            print(f"获取全球财经快讯失败: {str(e)}")
            return pd.DataFrame()
    
    def get_stock_news_em(self):
        """获取个股新闻-东方财富"""
        try:
            df = ak.stock_news_em()
            if not df.empty:
                print(f"获取到 {len(df)} 条个股新闻")
            return df
        except Exception as e:
            print(f"获取个股新闻失败: {str(e)}")
            return pd.DataFrame()
    
    def get_news_main_cx(self):
        """获取财经内容精选"""
        try:
            df = ak.stock_news_main_cx()
            if not df.empty:
                print(f"获取到 {len(df)} 条财经内容精选")
            return df
        except Exception as e:
            print(f"获取财经内容精选失败: {str(e)}")
            return pd.DataFrame()

if __name__ == "__main__":
    print("=== 测试简化版PolicyDataSource ===")
    
    policy_data = SimplePolicyDataSource()
    
    # 测试各个方法
    print("\n1. 测试财经早餐...")
    df1 = policy_data.get_financial_breakfast()
    
    print("\n2. 测试全球财经快讯...")
    df2 = policy_data.get_global_news_em()
    
    print("\n3. 测试个股新闻...")
    df3 = policy_data.get_stock_news_em()
    
    print("\n4. 测试财经内容精选...")
    df4 = policy_data.get_news_main_cx()
    
    total = len(df1) + len(df2) + len(df3) + len(df4)
    print(f"\n📊 总计获取新闻: {total} 条")
    print("\n=== 测试完成 ===")
