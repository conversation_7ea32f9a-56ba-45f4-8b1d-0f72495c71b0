"""
情绪共振模型 (Sentiment Resonance Model)
基于新闻政策数据分析市场情绪共振效应
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SentimentResonanceModel:
    """
    情绪共振模型
    
    功能：
    1. 分析新闻政策情绪对市场的影响
    2. 检测情绪共振效应
    3. 预测情绪传导路径
    4. 生成情绪指标
    """
    
    def __init__(self, config=None):
        """
        初始化情绪共振模型
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 模型参数
        self.resonance_threshold = self.config.get('resonance_threshold', 0.6)
        self.time_window = self.config.get('time_window', 24)  # 小时
        self.decay_factor = self.config.get('decay_factor', 0.8)
        
        # 情绪权重配置
        self.sentiment_weights = {
            'policy': 1.5,      # 政策情绪权重更高
            'news': 1.0,        # 新闻情绪基础权重
            'market': 0.8       # 市场情绪权重
        }
        
        # 共振类型定义
        self.resonance_types = {
            'positive_amplification': '正向放大',
            'negative_amplification': '负向放大', 
            'sentiment_reversal': '情绪反转',
            'neutral_dampening': '中性抑制'
        }
        
        logger.info("情绪共振模型初始化完成")
    
    def analyze_sentiment_resonance(self, news_data: pd.DataFrame, 
                                  policy_data: pd.DataFrame,
                                  market_data: Optional[pd.DataFrame] = None) -> Dict:
        """
        分析情绪共振效应
        
        Args:
            news_data: 新闻数据
            policy_data: 政策数据
            market_data: 市场数据（可选）
            
        Returns:
            Dict: 共振分析结果
        """
        logger.info("开始分析情绪共振效应...")
        
        try:
            # 1. 预处理数据
            processed_news = self._preprocess_sentiment_data(news_data, 'news')
            processed_policy = self._preprocess_sentiment_data(policy_data, 'policy')
            
            # 2. 计算基础情绪指标
            news_sentiment = self._calculate_sentiment_metrics(processed_news, 'news')
            policy_sentiment = self._calculate_sentiment_metrics(processed_policy, 'policy')
            
            # 3. 检测共振效应
            resonance_effects = self._detect_resonance_effects(news_sentiment, policy_sentiment)
            
            # 4. 分析传导路径
            transmission_paths = self._analyze_transmission_paths(news_sentiment, policy_sentiment)
            
            # 5. 生成综合指标
            composite_indicators = self._generate_composite_indicators(
                news_sentiment, policy_sentiment, resonance_effects
            )
            
            # 6. 预测情绪趋势
            sentiment_forecast = self._forecast_sentiment_trend(
                news_sentiment, policy_sentiment, resonance_effects
            )
            
            result = {
                'timestamp': datetime.now().isoformat(),
                'news_sentiment': news_sentiment,
                'policy_sentiment': policy_sentiment,
                'resonance_effects': resonance_effects,
                'transmission_paths': transmission_paths,
                'composite_indicators': composite_indicators,
                'sentiment_forecast': sentiment_forecast,
                'model_confidence': self._calculate_model_confidence(resonance_effects)
            }
            
            logger.info("情绪共振分析完成")
            return result
            
        except Exception as e:
            logger.error(f"情绪共振分析失败: {e}")
            return self._get_default_result()
    
    def _preprocess_sentiment_data(self, data: pd.DataFrame, data_type: str) -> pd.DataFrame:
        """预处理情绪数据"""
        if data.empty:
            return pd.DataFrame()
        
        df = data.copy()
        
        # 确保必要的列存在
        required_columns = ['title', 'publish_date']
        for col in required_columns:
            if col not in df.columns:
                logger.warning(f"{data_type}数据缺少{col}列")
                return pd.DataFrame()
        
        # 时间处理
        if 'publish_date' in df.columns:
            df['publish_date'] = pd.to_datetime(df['publish_date'], errors='coerce')
            df = df.dropna(subset=['publish_date'])
        
        # 添加数据类型标识
        df['data_type'] = data_type
        
        # 计算文本长度（用于重要性权重）
        df['text_length'] = df['title'].str.len()
        if 'content' in df.columns:
            df['text_length'] += df['content'].fillna('').str.len()
        
        return df
    
    def _calculate_sentiment_metrics(self, data: pd.DataFrame, data_type: str) -> Dict:
        """计算情绪指标"""
        if data.empty:
            return self._get_empty_sentiment_metrics()
        
        # 使用现有的情绪分析器
        try:
            from engines.sentiment.analyzer import SentimentAnalyzer
            analyzer = SentimentAnalyzer()
            
            # 分析情绪
            sentiment_results = []
            for _, row in data.iterrows():
                text = row.get('title', '') + ' ' + row.get('content', '')
                sentiment_score = analyzer.analyze_text_sentiment(text)
                sentiment_results.append(sentiment_score)
            
            data['sentiment_score'] = sentiment_results
            
        except Exception as e:
            logger.warning(f"使用情绪分析器失败: {e}，使用简化方法")
            # 简化的情绪分析
            data['sentiment_score'] = self._simple_sentiment_analysis(data)
        
        # 计算加权情绪分数
        weight = self.sentiment_weights.get(data_type, 1.0)
        data['weighted_sentiment'] = data['sentiment_score'] * weight
        
        # 计算时间衰减权重
        now = datetime.now()
        data['time_decay'] = data['publish_date'].apply(
            lambda x: self.decay_factor ** ((now - x).total_seconds() / 3600)
        )
        
        # 计算最终加权分数
        data['final_sentiment'] = data['weighted_sentiment'] * data['time_decay']
        
        # 生成指标
        metrics = {
            'data_type': data_type,
            'total_count': len(data),
            'avg_sentiment': data['sentiment_score'].mean(),
            'weighted_avg_sentiment': data['weighted_sentiment'].mean(),
            'final_avg_sentiment': data['final_sentiment'].mean(),
            'sentiment_std': data['sentiment_score'].std(),
            'positive_ratio': (data['sentiment_score'] > 0.2).mean(),
            'negative_ratio': (data['sentiment_score'] < -0.2).mean(),
            'neutral_ratio': ((data['sentiment_score'] >= -0.2) & 
                            (data['sentiment_score'] <= 0.2)).mean(),
            'sentiment_intensity': abs(data['sentiment_score']).mean(),
            'recent_sentiment': data.nlargest(10, 'publish_date')['sentiment_score'].mean(),
            'sentiment_trend': self._calculate_sentiment_trend(data),
            'data': data
        }
        
        return metrics
    
    def _simple_sentiment_analysis(self, data: pd.DataFrame) -> List[float]:
        """简化的情绪分析"""
        positive_words = ['利好', '上涨', '增长', '提高', '支持', '优化', '机遇', '突破']
        negative_words = ['利空', '下跌', '减少', '限制', '风险', '亏损', '崩盘', '危机']
        
        sentiment_scores = []
        for _, row in data.iterrows():
            text = str(row.get('title', '')) + ' ' + str(row.get('content', ''))
            
            pos_count = sum(1 for word in positive_words if word in text)
            neg_count = sum(1 for word in negative_words if word in text)
            
            if pos_count + neg_count == 0:
                score = 0.0
            else:
                score = (pos_count - neg_count) / (pos_count + neg_count)
            
            sentiment_scores.append(score)
        
        return sentiment_scores
    
    def _calculate_sentiment_trend(self, data: pd.DataFrame) -> str:
        """计算情绪趋势"""
        if len(data) < 5:
            return 'insufficient_data'
        
        # 按时间排序
        sorted_data = data.sort_values('publish_date')
        
        # 计算移动平均
        window_size = min(5, len(sorted_data) // 2)
        rolling_sentiment = sorted_data['sentiment_score'].rolling(window=window_size).mean()
        
        # 比较首尾
        first_avg = rolling_sentiment.dropna().iloc[0]
        last_avg = rolling_sentiment.dropna().iloc[-1]
        
        if last_avg > first_avg + 0.1:
            return 'improving'
        elif last_avg < first_avg - 0.1:
            return 'deteriorating'
        else:
            return 'stable'
    
    def _detect_resonance_effects(self, news_sentiment: Dict, policy_sentiment: Dict) -> Dict:
        """检测共振效应"""
        resonance_effects = {
            'has_resonance': False,
            'resonance_type': None,
            'resonance_strength': 0.0,
            'resonance_direction': 'neutral',
            'confidence': 0.0
        }
        
        # 检查数据有效性
        if (news_sentiment['total_count'] == 0 or 
            policy_sentiment['total_count'] == 0):
            return resonance_effects
        
        # 计算情绪相关性
        news_avg = news_sentiment['final_avg_sentiment']
        policy_avg = policy_sentiment['final_avg_sentiment']
        
        # 计算共振强度
        sentiment_alignment = abs(news_avg * policy_avg)  # 同向程度
        sentiment_magnitude = (abs(news_avg) + abs(policy_avg)) / 2  # 情绪强度
        
        resonance_strength = sentiment_alignment * sentiment_magnitude
        
        # 判断是否存在共振
        if resonance_strength > self.resonance_threshold:
            resonance_effects['has_resonance'] = True
            resonance_effects['resonance_strength'] = resonance_strength
            
            # 确定共振类型
            if news_avg > 0 and policy_avg > 0:
                resonance_effects['resonance_type'] = 'positive_amplification'
                resonance_effects['resonance_direction'] = 'positive'
            elif news_avg < 0 and policy_avg < 0:
                resonance_effects['resonance_type'] = 'negative_amplification'
                resonance_effects['resonance_direction'] = 'negative'
            elif (news_avg > 0 and policy_avg < 0) or (news_avg < 0 and policy_avg > 0):
                resonance_effects['resonance_type'] = 'sentiment_reversal'
                resonance_effects['resonance_direction'] = 'conflicting'
            else:
                resonance_effects['resonance_type'] = 'neutral_dampening'
                resonance_effects['resonance_direction'] = 'neutral'
            
            # 计算置信度
            resonance_effects['confidence'] = min(0.95, resonance_strength / 1.5)
        
        return resonance_effects
    
    def _analyze_transmission_paths(self, news_sentiment: Dict, policy_sentiment: Dict) -> Dict:
        """分析传导路径"""
        return {
            'primary_driver': 'policy' if policy_sentiment['sentiment_intensity'] > 
                            news_sentiment['sentiment_intensity'] else 'news',
            'transmission_speed': 'fast',  # 简化实现
            'amplification_factor': 1.2,   # 简化实现
            'decay_rate': self.decay_factor
        }
    
    def _generate_composite_indicators(self, news_sentiment: Dict, 
                                     policy_sentiment: Dict, 
                                     resonance_effects: Dict) -> Dict:
        """生成综合指标"""
        # 综合情绪指数
        composite_sentiment = (
            news_sentiment['final_avg_sentiment'] * 0.4 +
            policy_sentiment['final_avg_sentiment'] * 0.6
        )
        
        # 市场情绪压力指数
        pressure_index = abs(composite_sentiment) * (
            1 + resonance_effects['resonance_strength']
        )
        
        return {
            'composite_sentiment_index': composite_sentiment,
            'market_pressure_index': pressure_index,
            'sentiment_volatility': (news_sentiment['sentiment_std'] + 
                                   policy_sentiment['sentiment_std']) / 2,
            'information_density': news_sentiment['total_count'] + 
                                 policy_sentiment['total_count']
        }
    
    def _forecast_sentiment_trend(self, news_sentiment: Dict, 
                                policy_sentiment: Dict, 
                                resonance_effects: Dict) -> Dict:
        """预测情绪趋势"""
        # 简化的趋势预测
        current_sentiment = (news_sentiment['final_avg_sentiment'] + 
                           policy_sentiment['final_avg_sentiment']) / 2
        
        # 基于共振效应调整预测
        if resonance_effects['has_resonance']:
            if resonance_effects['resonance_direction'] == 'positive':
                predicted_sentiment = current_sentiment * 1.2
            elif resonance_effects['resonance_direction'] == 'negative':
                predicted_sentiment = current_sentiment * 1.2
            else:
                predicted_sentiment = current_sentiment * 0.8
        else:
            predicted_sentiment = current_sentiment * 0.9  # 自然衰减
        
        return {
            'predicted_sentiment': predicted_sentiment,
            'prediction_horizon': '24h',
            'confidence': 0.7,
            'trend_direction': 'up' if predicted_sentiment > current_sentiment else 'down'
        }
    
    def _calculate_model_confidence(self, resonance_effects: Dict) -> float:
        """计算模型置信度"""
        base_confidence = 0.6
        
        if resonance_effects['has_resonance']:
            confidence_boost = resonance_effects['confidence'] * 0.3
            return min(0.95, base_confidence + confidence_boost)
        
        return base_confidence
    
    def _get_empty_sentiment_metrics(self) -> Dict:
        """获取空的情绪指标"""
        return {
            'total_count': 0,
            'avg_sentiment': 0.0,
            'weighted_avg_sentiment': 0.0,
            'final_avg_sentiment': 0.0,
            'sentiment_std': 0.0,
            'positive_ratio': 0.0,
            'negative_ratio': 0.0,
            'neutral_ratio': 1.0,
            'sentiment_intensity': 0.0,
            'recent_sentiment': 0.0,
            'sentiment_trend': 'neutral',
            'data': pd.DataFrame()
        }
    
    def _get_default_result(self) -> Dict:
        """获取默认结果"""
        return {
            'timestamp': datetime.now().isoformat(),
            'news_sentiment': self._get_empty_sentiment_metrics(),
            'policy_sentiment': self._get_empty_sentiment_metrics(),
            'resonance_effects': {
                'has_resonance': False,
                'resonance_type': None,
                'resonance_strength': 0.0,
                'resonance_direction': 'neutral',
                'confidence': 0.0
            },
            'transmission_paths': {},
            'composite_indicators': {
                'composite_sentiment_index': 0.0,
                'market_pressure_index': 0.0,
                'sentiment_volatility': 0.0,
                'information_density': 0
            },
            'sentiment_forecast': {
                'predicted_sentiment': 0.0,
                'prediction_horizon': '24h',
                'confidence': 0.0,
                'trend_direction': 'neutral'
            },
            'model_confidence': 0.0
        }
