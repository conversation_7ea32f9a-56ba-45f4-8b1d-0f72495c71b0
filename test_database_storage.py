"""
测试数据库存储流程
验证新闻政策数据的完整存储流程
"""

import pandas as pd
import json
import os
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database_storage_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_database_config():
    """测试数据库配置"""
    logger.info("=" * 60)
    logger.info("测试数据库配置")
    logger.info("=" * 60)
    
    try:
        config_path = "database/config/database_config.json"
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                db_config = json.load(f)
            logger.info("✅ 数据库配置文件存在")
            
            # 显示配置信息
            for db_type, config in db_config.items():
                enabled = config.get('enabled', False)
                fallback = (config.get('use_sqlite_fallback', False) or 
                          config.get('use_csv_fallback', False) or 
                          config.get('use_json_fallback', False) or 
                          config.get('use_memory_fallback', False))
                status = "启用" if enabled else "禁用"
                fallback_info = " (有备用方案)" if fallback else ""
                logger.info(f"  {db_type}: {status}{fallback_info}")
            
            return True, db_config
        else:
            logger.warning("⚠️ 数据库配置文件不存在")
            return False, {}
            
    except Exception as e:
        logger.error(f"❌ 数据库配置测试失败: {e}")
        return False, {}

def test_file_storage():
    """测试文件存储功能"""
    logger.info("\n" + "=" * 60)
    logger.info("测试文件存储功能")
    logger.info("=" * 60)
    
    try:
        from database.file_storage import FileStorage
        
        # 初始化文件存储
        storage = FileStorage()
        logger.info("✅ FileStorage 初始化成功")
        
        # 测试新闻内容存储
        news_content = "这是一条测试新闻的详细内容，包含了重要的财经信息..."
        news_path = storage.save_news_content(
            news_id="test_news_001",
            content=news_content,
            source="测试来源",
            publish_date="2025-01-15"
        )
        logger.info(f"✅ 新闻内容存储成功: {news_path}")
        
        # 验证文件是否存在
        if os.path.exists(news_path):
            logger.info("✅ 新闻文件存储验证成功")
            # 读取文件内容验证
            with open(news_path, 'r', encoding='utf-8') as f:
                saved_content = f.read()
            if news_content in saved_content:
                logger.info("✅ 文件内容验证成功")
            else:
                logger.warning("⚠️ 文件内容验证失败")
        else:
            logger.warning("⚠️ 新闻文件未找到")
        
        # 测试政策内容存储
        policy_content = "这是一条测试政策的详细内容，包含了重要的政策信息..."
        policy_path = storage.save_policy_content(
            policy_id="test_policy_001",
            content=policy_content,
            source="国务院",
            publish_date="2025-01-10"
        )
        logger.info(f"✅ 政策内容存储成功: {policy_path}")
        
        # 验证文件是否存在
        if os.path.exists(policy_path):
            logger.info("✅ 政策文件存储验证成功")
        else:
            logger.warning("⚠️ 政策文件未找到")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 文件存储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unified_data_access():
    """测试统一数据访问接口"""
    logger.info("\n" + "=" * 60)
    logger.info("测试统一数据访问接口")
    logger.info("=" * 60)
    
    try:
        from database.unified_data_access import UnifiedDataAccess
        
        # 初始化统一数据访问
        data_access = UnifiedDataAccess()
        logger.info("✅ UnifiedDataAccess 初始化成功")
        
        # 测试连接
        if data_access.connect():
            logger.info("✅ 数据库连接成功")
            
            # 测试保存新闻数据
            test_news_data = {
                'id': 'test_news_001',
                'title': '测试新闻标题',
                'content': '这是一条测试新闻内容',
                'source': '测试来源',
                'publish_date': datetime.now().strftime('%Y-%m-%d'),
                'created_at': datetime.now().isoformat(),
                'importance_score': 0.8
            }
            
            try:
                result = data_access.save_data('news_metadata', test_news_data)
                logger.info("✅ 新闻数据保存成功")
                
                # 测试查询数据
                query_result = data_access.query_data('news_metadata', {'id': 'test_news_001'})
                if query_result:
                    logger.info("✅ 新闻数据查询成功")
                    logger.info(f"   查询结果: {len(query_result)} 条记录")
                else:
                    logger.warning("⚠️ 新闻数据查询为空")
                
            except Exception as e:
                logger.warning(f"⚠️ 新闻数据操作失败: {e}")
            
            # 测试保存政策数据
            test_policy_data = {
                'id': 'test_policy_001',
                'title': '测试政策标题',
                'content': '这是一条测试政策内容',
                'source': '国务院',
                'publish_date': datetime.now().strftime('%Y-%m-%d'),
                'created_at': datetime.now().isoformat(),
                'importance_score': 0.9
            }
            
            try:
                result = data_access.save_data('policy_metadata', test_policy_data)
                logger.info("✅ 政策数据保存成功")
                
                # 测试查询数据
                query_result = data_access.query_data('policy_metadata', {'id': 'test_policy_001'})
                if query_result:
                    logger.info("✅ 政策数据查询成功")
                    logger.info(f"   查询结果: {len(query_result)} 条记录")
                else:
                    logger.warning("⚠️ 政策数据查询为空")
                
            except Exception as e:
                logger.warning(f"⚠️ 政策数据操作失败: {e}")
            
            data_access.disconnect()
            return True
        else:
            logger.warning("⚠️ 数据库连接失败，可能使用备用存储")
            return True  # 备用存储也算成功
            
    except Exception as e:
        logger.error(f"❌ 统一数据访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_content_processor():
    """测试内容处理器"""
    logger.info("\n" + "=" * 60)
    logger.info("测试内容处理器")
    logger.info("=" * 60)
    
    try:
        from database.content_processor import ContentProcessor
        
        # 初始化内容处理器
        processor = ContentProcessor()
        logger.info("✅ ContentProcessor 初始化成功")
        
        # 测试新闻数据处理
        news_data = {
            'title': '央行降准释放流动性支持实体经济',
            'content': '中国人民银行决定于2025年1月15日下调存款准备金率0.5个百分点，释放长期资金约1万亿元。',
            'source': '中国人民银行',
            'publish_date': '2025-01-15',
            'url': 'https://example.com/news/001'
        }
        
        try:
            result = processor.process_news(news_data, save_content=True)
            logger.info("✅ 新闻数据处理成功")
            logger.info(f"   生成ID: {result['metadata']['id']}")
            logger.info(f"   重要性评分: {result['metadata']['importance_score']}")
            
            if 'content_storage_path' in result['metadata']:
                logger.info(f"   内容存储路径: {result['metadata']['content_storage_path']}")
            
        except Exception as e:
            logger.warning(f"⚠️ 新闻数据处理失败: {e}")
        
        # 测试政策数据处理
        policy_data = {
            'title': '关于完善中国特色现代企业制度的意见',
            'content': '为深入贯彻党的二十大精神，完善中国特色现代企业制度...',
            'source': '国务院',
            'publish_date': '2025-01-10',
            'url': 'https://example.com/policy/001'
        }
        
        try:
            result = processor.process_policy(policy_data, save_content=True)
            logger.info("✅ 政策数据处理成功")
            logger.info(f"   生成ID: {result['metadata']['id']}")
            logger.info(f"   重要性评分: {result['metadata']['importance_score']}")
            
        except Exception as e:
            logger.warning(f"⚠️ 政策数据处理失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 内容处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info("开始数据库存储流程测试")
    logger.info(f"测试时间: {datetime.now()}")
    
    # 创建必要的目录
    os.makedirs('data', exist_ok=True)
    os.makedirs('data/news', exist_ok=True)
    os.makedirs('data/policy', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    results = {}
    
    # 执行各项测试
    config_success, db_config = test_database_config()
    results['数据库配置'] = config_success
    
    results['文件存储'] = test_file_storage()
    results['统一数据访问'] = test_unified_data_access()
    results['内容处理器'] = test_content_processor()
    
    # 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("数据库存储流程测试结果")
    logger.info("=" * 80)
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count >= 3:  # 至少3项测试通过
        logger.info("🎉 数据库存储流程基本正常！")
        logger.info("📊 数据流程: 数据获取 → 内容处理 → 数据库存储 → 文件存储")
        logger.info("🚀 可以继续开发情绪共振模型")
    else:
        logger.warning("⚠️ 数据库存储流程存在问题，需要进一步调试")
    
    logger.info(f"\n测试完成时间: {datetime.now()}")
    logger.info("详细日志已保存到: database_storage_test.log")
    
    return success_count >= 3

if __name__ == "__main__":
    main()
