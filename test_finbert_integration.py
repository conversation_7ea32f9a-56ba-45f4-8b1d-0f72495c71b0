"""
测试FinBERT集成
验证FinBERT模型的情绪分析功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('finbert_integration_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_finbert_analyzer():
    """测试FinBERT分析器"""
    
    logger.info("=" * 80)
    logger.info("测试FinBERT分析器")
    logger.info("=" * 80)
    
    try:
        from engines.sentiment.finbert_analyzer import FinBERTAnalyzer
        
        # 初始化分析器
        analyzer = FinBERTAnalyzer()
        logger.info("✅ FinBERTAnalyzer 初始化成功")
        
        # 获取模型信息
        model_info = analyzer.get_model_info()
        logger.info(f"📊 模型信息:")
        logger.info(f"   模型名称: {model_info['model_name']}")
        logger.info(f"   模型可用: {model_info['model_available']}")
        logger.info(f"   使用镜像: {model_info['use_hf_mirror']}")
        logger.info(f"   支持语言: {model_info['supported_languages']}")
        logger.info(f"   最大长度: {model_info['max_length']}")
        
        # 测试单个文本分析
        logger.info("\n" + "=" * 60)
        logger.info("测试单个文本情绪分析")
        logger.info("=" * 60)
        
        test_texts = [
            "央行降准释放流动性，股市迎来重大利好消息",
            "通胀压力持续加大，市场担忧货币政策收紧",
            "科技股表现平稳，投资者保持观望态度",
            "The Federal Reserve cut interest rates, boosting market sentiment",
            "Inflation concerns weigh on investor confidence",
            "Markets remain stable amid mixed economic signals"
        ]
        
        single_results = []
        for i, text in enumerate(test_texts, 1):
            result = analyzer.analyze_sentiment(text)
            single_results.append(result)
            
            logger.info(f"{i}. 文本: {text[:50]}...")
            logger.info(f"   情绪标签: {result['label']}")
            logger.info(f"   情绪分数: {result['score']:.3f}")
            logger.info(f"   置信度: {result['confidence']:.3f}")
            logger.info(f"   分析方法: {result['method']}")
            logger.info(f"   概率分布: {result['probabilities']}")
            logger.info("")
        
        # 测试批量分析
        logger.info("=" * 60)
        logger.info("测试批量文本情绪分析")
        logger.info("=" * 60)
        
        batch_results = analyzer.analyze_batch(test_texts)
        logger.info(f"✅ 批量分析完成，处理了 {len(batch_results)} 个文本")
        
        # 统计分析结果
        positive_count = sum(1 for r in batch_results if r['label'] == 'positive')
        negative_count = sum(1 for r in batch_results if r['label'] == 'negative')
        neutral_count = sum(1 for r in batch_results if r['label'] == 'neutral')
        
        logger.info(f"📊 情绪分布:")
        logger.info(f"   正面: {positive_count} ({positive_count/len(batch_results)*100:.1f}%)")
        logger.info(f"   负面: {negative_count} ({negative_count/len(batch_results)*100:.1f}%)")
        logger.info(f"   中性: {neutral_count} ({neutral_count/len(batch_results)*100:.1f}%)")
        
        # 测试DataFrame分析
        logger.info("\n" + "=" * 60)
        logger.info("测试DataFrame情绪分析")
        logger.info("=" * 60)
        
        # 创建测试数据框
        test_df = pd.DataFrame({
            'title': [
                '央行降准释放流动性',
                '通胀压力持续上升',
                '科技股表现平稳',
                'Fed cuts rates'
            ],
            'content': [
                '中国人民银行宣布下调存款准备金率，向市场释放长期资金',
                '最新数据显示通胀压力持续，市场担忧政策收紧',
                '科技股今日表现平稳，投资者保持观望态度',
                'The Federal Reserve announced a rate cut to support economic growth'
            ],
            'source': ['央行', '统计局', '证券报', 'Reuters'],
            'publish_date': [
                datetime.now() - timedelta(hours=1),
                datetime.now() - timedelta(hours=2),
                datetime.now() - timedelta(hours=3),
                datetime.now() - timedelta(hours=4)
            ]
        })
        
        # 执行DataFrame分析
        analyzed_df = analyzer.analyze_dataframe(test_df)
        
        logger.info(f"✅ DataFrame分析完成，处理了 {len(analyzed_df)} 行数据")
        logger.info("📊 分析结果:")
        
        for i, row in analyzed_df.iterrows():
            logger.info(f"  {i+1}. {row['title'][:30]}...")
            logger.info(f"     情绪: {row['sentiment_label']} (分数: {row['sentiment_score']:.3f})")
            logger.info(f"     置信度: {row['sentiment_confidence']:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FinBERT分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sentiment_resonance_with_finbert():
    """测试情绪共振模型与FinBERT的集成"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试情绪共振模型与FinBERT集成")
    logger.info("=" * 80)
    
    try:
        from engines.sentiment.resonance_model import SentimentResonanceModel
        from engines.sentiment.finbert_analyzer import FinBERTAnalyzer
        
        # 初始化模型
        resonance_model = SentimentResonanceModel()
        finbert_analyzer = FinBERTAnalyzer()
        
        logger.info("✅ 模型初始化成功")
        
        # 创建测试数据
        news_data = pd.DataFrame([
            {
                'title': '央行降准释放流动性支持实体经济',
                'content': '中国人民银行决定下调存款准备金率0.5个百分点，释放长期资金约1万亿元。',
                'source': '央行',
                'publish_date': datetime.now() - timedelta(hours=1)
            },
            {
                'title': '科技股集体上涨创新高',
                'content': '受政策利好影响，科技股今日集体上涨，人工智能板块涨幅居前。',
                'source': '证券报',
                'publish_date': datetime.now() - timedelta(hours=2)
            }
        ])
        
        policy_data = pd.DataFrame([
            {
                'title': '国务院发布数字经济发展指导意见',
                'content': '为推动数字经济高质量发展，国务院发布指导意见，提出多项支持措施。',
                'source': '国务院',
                'publish_date': datetime.now() - timedelta(hours=1)
            }
        ])
        
        # 使用FinBERT增强情绪分析
        logger.info("🔍 使用FinBERT分析新闻情绪...")
        news_with_sentiment = finbert_analyzer.analyze_dataframe(news_data)
        
        logger.info("🔍 使用FinBERT分析政策情绪...")
        policy_with_sentiment = finbert_analyzer.analyze_dataframe(policy_data)
        
        # 显示FinBERT分析结果
        logger.info("\n📰 新闻FinBERT分析结果:")
        for i, row in news_with_sentiment.iterrows():
            logger.info(f"  {i+1}. {row['title'][:40]}...")
            logger.info(f"     FinBERT情绪: {row['sentiment_label']} ({row['sentiment_score']:.3f})")
        
        logger.info("\n📋 政策FinBERT分析结果:")
        for i, row in policy_with_sentiment.iterrows():
            logger.info(f"  {i+1}. {row['title'][:40]}...")
            logger.info(f"     FinBERT情绪: {row['sentiment_label']} ({row['sentiment_score']:.3f})")
        
        # 执行共振分析
        logger.info("\n🔄 执行情绪共振分析...")
        resonance_result = resonance_model.analyze_sentiment_resonance(
            news_with_sentiment, policy_with_sentiment
        )
        
        # 显示共振分析结果
        logger.info("📊 共振分析结果:")
        logger.info(f"   存在共振: {resonance_result['resonance_effects']['has_resonance']}")
        logger.info(f"   共振强度: {resonance_result['resonance_effects']['resonance_strength']:.3f}")
        logger.info(f"   综合情绪指数: {resonance_result['composite_indicators']['composite_sentiment_index']:.3f}")
        logger.info(f"   模型置信度: {resonance_result['model_confidence']:.3f}")
        
        logger.info("✅ FinBERT与情绪共振模型集成测试成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """测试性能对比"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试FinBERT vs 关键词方法性能对比")
    logger.info("=" * 80)
    
    try:
        from engines.sentiment.finbert_analyzer import FinBERTAnalyzer
        from engines.sentiment.analyzer import SentimentAnalyzer
        import time
        
        # 初始化分析器
        finbert_analyzer = FinBERTAnalyzer()
        traditional_analyzer = SentimentAnalyzer()
        
        # 测试文本
        test_texts = [
            "央行降准释放流动性，股市迎来重大利好",
            "通胀压力加大，市场担忧货币政策收紧",
            "科技股表现平稳，投资者保持观望态度",
            "外资持续流入，看好中国经济前景",
            "债券收益率上升，市场风险偏好下降"
        ] * 10  # 重复10次以测试性能
        
        # 测试FinBERT性能
        logger.info("🚀 测试FinBERT分析性能...")
        start_time = time.time()
        finbert_results = finbert_analyzer.analyze_batch(test_texts)
        finbert_time = time.time() - start_time
        
        # 测试传统方法性能
        logger.info("🚀 测试传统方法分析性能...")
        start_time = time.time()
        traditional_results = []
        for text in test_texts:
            score = traditional_analyzer.analyze_text_sentiment(text)
            traditional_results.append({'score': score})
        traditional_time = time.time() - start_time
        
        # 性能对比
        logger.info("\n📊 性能对比结果:")
        logger.info(f"   FinBERT方法:")
        logger.info(f"     处理时间: {finbert_time:.2f} 秒")
        logger.info(f"     平均每条: {finbert_time/len(test_texts)*1000:.1f} 毫秒")
        logger.info(f"   传统方法:")
        logger.info(f"     处理时间: {traditional_time:.2f} 秒")
        logger.info(f"     平均每条: {traditional_time/len(test_texts)*1000:.1f} 毫秒")
        logger.info(f"   速度比: {finbert_time/traditional_time:.1f}x")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能对比测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info(f"开始FinBERT集成测试 - {datetime.now()}")
    
    results = {}
    
    # 测试FinBERT分析器
    results['FinBERT分析器'] = test_finbert_analyzer()
    
    # 测试与情绪共振模型集成
    results['情绪共振集成'] = test_sentiment_resonance_with_finbert()
    
    # 测试性能对比
    results['性能对比'] = test_performance_comparison()
    
    # 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("FinBERT集成测试结果")
    logger.info("=" * 80)
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count >= 2:
        logger.info("🎉 FinBERT集成基本成功！")
        logger.info("📊 情绪共振模型已具备高级情绪分析能力")
        logger.info("🚀 可以进行下一阶段的系统集成")
    else:
        logger.warning("⚠️ FinBERT集成存在问题，需要进一步调试")
    
    logger.info(f"\n测试完成时间: {datetime.now()}")
    logger.info("详细日志已保存到: finbert_integration_test.log")
    
    return success_count >= 2

if __name__ == "__main__":
    main()
