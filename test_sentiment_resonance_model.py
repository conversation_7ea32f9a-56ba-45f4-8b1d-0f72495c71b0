"""
测试情绪共振模型
验证情绪共振分析功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sentiment_resonance_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_mock_news_data():
    """创建模拟新闻数据"""
    news_data = [
        {
            'title': '央行降准释放流动性 股市迎来利好',
            'content': '中国人民银行宣布下调存款准备金率0.5个百分点，向市场释放长期资金约1万亿元，支持实体经济发展。',
            'source': '财经新闻',
            'publish_date': datetime.now() - timedelta(hours=2),
            'url': 'https://example.com/news/001'
        },
        {
            'title': '科技股集体上涨 创新板块表现强势',
            'content': '受政策利好影响，科技股今日集体上涨，人工智能、新能源等板块涨幅居前。',
            'source': '证券日报',
            'publish_date': datetime.now() - timedelta(hours=4),
            'url': 'https://example.com/news/002'
        },
        {
            'title': '市场担忧通胀压力 债券收益率上升',
            'content': '通胀数据超预期，市场担忧货币政策收紧，债券收益率出现上升。',
            'source': '金融时报',
            'publish_date': datetime.now() - timedelta(hours=6),
            'url': 'https://example.com/news/003'
        },
        {
            'title': '外资持续流入A股 看好中国经济前景',
            'content': '北向资金连续净流入，外资机构表示看好中国经济长期发展前景。',
            'source': '投资快报',
            'publish_date': datetime.now() - timedelta(hours=8),
            'url': 'https://example.com/news/004'
        }
    ]
    
    return pd.DataFrame(news_data)

def create_mock_policy_data():
    """创建模拟政策数据"""
    policy_data = [
        {
            'title': '国务院发布促进数字经济发展的指导意见',
            'content': '为推动数字经济高质量发展，国务院发布指导意见，提出加强数字基础设施建设，培育数字经济新业态。',
            'source': '国务院',
            'publish_date': datetime.now() - timedelta(hours=1),
            'url': 'https://example.com/policy/001'
        },
        {
            'title': '发改委出台支持新能源汽车产业发展政策',
            'content': '国家发展改革委发布新能源汽车产业发展支持政策，包括财税支持、基础设施建设等措施。',
            'source': '发改委',
            'publish_date': datetime.now() - timedelta(hours=3),
            'url': 'https://example.com/policy/002'
        },
        {
            'title': '央行发布金融支持实体经济的通知',
            'content': '中国人民银行发布通知，要求金融机构加大对实体经济的支持力度，降低融资成本。',
            'source': '央行',
            'publish_date': datetime.now() - timedelta(hours=5),
            'url': 'https://example.com/policy/003'
        }
    ]
    
    return pd.DataFrame(policy_data)

def test_sentiment_resonance_model():
    """测试情绪共振模型"""
    
    logger.info("=" * 80)
    logger.info("测试情绪共振模型")
    logger.info("=" * 80)
    
    try:
        # 导入情绪共振模型
        from engines.sentiment.resonance_model import SentimentResonanceModel
        
        # 初始化模型
        config = {
            'resonance_threshold': 0.5,
            'time_window': 24,
            'decay_factor': 0.8
        }
        
        model = SentimentResonanceModel(config=config)
        logger.info("✅ SentimentResonanceModel 初始化成功")
        
        # 创建测试数据
        news_data = create_mock_news_data()
        policy_data = create_mock_policy_data()
        
        logger.info(f"📊 测试数据准备完成:")
        logger.info(f"   新闻数据: {len(news_data)} 条")
        logger.info(f"   政策数据: {len(policy_data)} 条")
        
        # 执行情绪共振分析
        logger.info("\n开始执行情绪共振分析...")
        result = model.analyze_sentiment_resonance(news_data, policy_data)
        
        # 验证结果结构
        logger.info("\n" + "=" * 60)
        logger.info("分析结果验证")
        logger.info("=" * 60)
        
        required_keys = [
            'timestamp', 'news_sentiment', 'policy_sentiment', 
            'resonance_effects', 'transmission_paths', 
            'composite_indicators', 'sentiment_forecast', 'model_confidence'
        ]
        
        missing_keys = [key for key in required_keys if key not in result]
        if missing_keys:
            logger.error(f"❌ 缺少必要的结果键: {missing_keys}")
            return False
        else:
            logger.info("✅ 结果结构完整")
        
        # 显示分析结果
        logger.info("\n" + "=" * 60)
        logger.info("情绪共振分析结果")
        logger.info("=" * 60)
        
        # 新闻情绪分析结果
        news_sentiment = result['news_sentiment']
        logger.info(f"📰 新闻情绪分析:")
        logger.info(f"   总数量: {news_sentiment['total_count']}")
        logger.info(f"   平均情绪: {news_sentiment['avg_sentiment']:.3f}")
        logger.info(f"   加权情绪: {news_sentiment['weighted_avg_sentiment']:.3f}")
        logger.info(f"   最终情绪: {news_sentiment['final_avg_sentiment']:.3f}")
        logger.info(f"   正面比例: {news_sentiment['positive_ratio']:.3f}")
        logger.info(f"   负面比例: {news_sentiment['negative_ratio']:.3f}")
        logger.info(f"   情绪趋势: {news_sentiment['sentiment_trend']}")
        
        # 政策情绪分析结果
        policy_sentiment = result['policy_sentiment']
        logger.info(f"\n📋 政策情绪分析:")
        logger.info(f"   总数量: {policy_sentiment['total_count']}")
        logger.info(f"   平均情绪: {policy_sentiment['avg_sentiment']:.3f}")
        logger.info(f"   加权情绪: {policy_sentiment['weighted_avg_sentiment']:.3f}")
        logger.info(f"   最终情绪: {policy_sentiment['final_avg_sentiment']:.3f}")
        logger.info(f"   正面比例: {policy_sentiment['positive_ratio']:.3f}")
        logger.info(f"   负面比例: {policy_sentiment['negative_ratio']:.3f}")
        logger.info(f"   情绪趋势: {policy_sentiment['sentiment_trend']}")
        
        # 共振效应分析结果
        resonance_effects = result['resonance_effects']
        logger.info(f"\n🔄 共振效应分析:")
        logger.info(f"   存在共振: {resonance_effects['has_resonance']}")
        logger.info(f"   共振类型: {resonance_effects['resonance_type']}")
        logger.info(f"   共振强度: {resonance_effects['resonance_strength']:.3f}")
        logger.info(f"   共振方向: {resonance_effects['resonance_direction']}")
        logger.info(f"   置信度: {resonance_effects['confidence']:.3f}")
        
        # 综合指标
        composite_indicators = result['composite_indicators']
        logger.info(f"\n📈 综合指标:")
        logger.info(f"   综合情绪指数: {composite_indicators['composite_sentiment_index']:.3f}")
        logger.info(f"   市场压力指数: {composite_indicators['market_pressure_index']:.3f}")
        logger.info(f"   情绪波动率: {composite_indicators['sentiment_volatility']:.3f}")
        logger.info(f"   信息密度: {composite_indicators['information_density']}")
        
        # 情绪预测
        sentiment_forecast = result['sentiment_forecast']
        logger.info(f"\n🔮 情绪预测:")
        logger.info(f"   预测情绪: {sentiment_forecast['predicted_sentiment']:.3f}")
        logger.info(f"   预测时间: {sentiment_forecast['prediction_horizon']}")
        logger.info(f"   预测置信度: {sentiment_forecast['confidence']:.3f}")
        logger.info(f"   趋势方向: {sentiment_forecast['trend_direction']}")
        
        # 模型置信度
        logger.info(f"\n🎯 模型置信度: {result['model_confidence']:.3f}")
        
        # 测试不同场景
        logger.info("\n" + "=" * 60)
        logger.info("测试边界情况")
        logger.info("=" * 60)
        
        # 测试空数据
        empty_result = model.analyze_sentiment_resonance(pd.DataFrame(), pd.DataFrame())
        if empty_result['model_confidence'] == 0.0:
            logger.info("✅ 空数据处理正确")
        else:
            logger.warning("⚠️ 空数据处理可能有问题")
        
        # 测试单一数据源
        single_news_result = model.analyze_sentiment_resonance(news_data, pd.DataFrame())
        if single_news_result['resonance_effects']['has_resonance'] == False:
            logger.info("✅ 单一数据源处理正确")
        else:
            logger.warning("⚠️ 单一数据源处理可能有问题")
        
        logger.info("\n🎉 情绪共振模型测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 情绪共振模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_finbert_integration():
    """测试FinBERT模型集成"""
    
    logger.info("\n" + "=" * 60)
    logger.info("测试FinBERT模型集成")
    logger.info("=" * 60)
    
    try:
        # 尝试导入和使用FinBERT
        from engines.sentiment.analyzer import SentimentAnalyzer
        
        analyzer = SentimentAnalyzer()
        logger.info("✅ SentimentAnalyzer 初始化成功")
        
        # 测试文本
        test_texts = [
            "央行降准释放流动性，股市迎来重大利好",
            "通胀压力加大，市场担忧货币政策收紧",
            "科技股表现平稳，投资者保持观望态度"
        ]
        
        logger.info("测试情绪分析:")
        for i, text in enumerate(test_texts, 1):
            sentiment_score = analyzer.analyze_text_sentiment(text)
            logger.info(f"  {i}. {text[:30]}... → 情绪分数: {sentiment_score:.3f}")
        
        logger.info("✅ FinBERT集成测试成功")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️ FinBERT集成测试失败: {e}")
        logger.info("💡 这可能是因为模型文件未下载或路径配置问题")
        return False

def main():
    """主测试函数"""
    logger.info(f"开始情绪共振模型测试 - {datetime.now()}")
    
    results = {}
    
    # 测试情绪共振模型
    results['情绪共振模型'] = test_sentiment_resonance_model()
    
    # 测试FinBERT集成
    results['FinBERT集成'] = test_finbert_integration()
    
    # 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("情绪共振模型测试结果")
    logger.info("=" * 80)
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count >= 1:
        logger.info("🎉 情绪共振模型基本功能正常！")
        logger.info("📊 可以进行下一步的系统集成测试")
    else:
        logger.warning("⚠️ 情绪共振模型存在问题，需要进一步调试")
    
    logger.info(f"\n测试完成时间: {datetime.now()}")
    logger.info("详细日志已保存到: sentiment_resonance_test.log")
    
    return success_count >= 1

if __name__ == "__main__":
    main()
