"""
基于真实AKShare API的资金流分析器
使用龙虎榜、基金持仓、股东户数、大单资金等真实API
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealAPIFundFlowAnalyzer:
    """
    基于真实API的资金流分析器

    功能：
    1. 使用真实龙虎榜API分析游资和机构资金流
    2. 使用基金持仓API分析机构资金变化
    3. 使用股东户数API分析散户行为
    4. 使用大单资金API分析主力资金流向
    """

    def __init__(self, config=None):
        """
        初始化真实API资金流分析器

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # 分析参数
        self.lookback_days = self.config.get('lookback_days', 30)
        self.hot_money_threshold = self.config.get('hot_money_threshold', 50000000)  # 5000万

        # 游资席位关键词
        self.hot_money_keywords = [
            '温州', '宁波', '杭州', '深圳', '上海', '北京', '广州',
            '中信证券', '华泰证券', '国泰君安', '招商证券', '中金公司'
        ]

        # 机构席位关键词
        self.institution_keywords = [
            '机构专用', '基金专用', '保险专用', '信托专用', '券商专用',
            '社保基金', '养老金', 'QFII', 'RQFII'
        ]

        logger.info("真实API资金流分析器初始化完成")

    def get_lhb_fund_flow(self, symbol: str, start_date: str, end_date: str) -> Dict:
        """
        获取龙虎榜资金流数据

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict: 龙虎榜资金流分析结果
        """
        try:
            logger.info(f"获取 {symbol} 龙虎榜数据...")

            # 获取龙虎榜详细数据
            lhb_detail = ak.stock_lhb_detail_em(symbol=symbol, start_date=start_date, end_date=end_date)

            if lhb_detail.empty:
                logger.warning(f"{symbol} 在指定期间无龙虎榜数据")
                return self._get_empty_lhb_result()

            logger.info(f"获取到 {len(lhb_detail)} 条龙虎榜记录")

            # 分析数据
            analysis_result = {
                'raw_data': lhb_detail,
                'hot_money_analysis': self._analyze_hot_money_from_lhb(lhb_detail),
                'institution_analysis': self._analyze_institution_from_lhb(lhb_detail),
                'seat_analysis': self._analyze_seat_activity(lhb_detail),
                'flow_summary': self._summarize_lhb_flows(lhb_detail)
            }

            logger.info(f"{symbol} 龙虎榜分析完成")
            return analysis_result

        except Exception as e:
            logger.error(f"获取龙虎榜数据失败: {e}")
            return self._get_empty_lhb_result()

    def get_fund_holding_flow(self, symbol: str) -> Dict:
        """
        获取基金持仓资金流数据

        Args:
            symbol: 股票代码

        Returns:
            Dict: 基金持仓分析结果
        """
        try:
            logger.info(f"获取 {symbol} 基金持仓数据...")

            # 获取基金持仓明细
            fund_holdings = ak.fund_portfolio_hold_em(symbol=symbol)

            if fund_holdings.empty:
                logger.warning(f"{symbol} 无基金持仓数据")
                return self._get_empty_fund_result()

            logger.info(f"获取到 {len(fund_holdings)} 条基金持仓记录")

            # 分析基金持仓变化
            analysis_result = {
                'raw_data': fund_holdings,
                'holding_analysis': self._analyze_fund_holdings(fund_holdings),
                'fund_type_analysis': self._analyze_fund_types(fund_holdings),
                'concentration_analysis': self._analyze_holding_concentration(fund_holdings)
            }

            logger.info(f"{symbol} 基金持仓分析完成")
            return analysis_result

        except Exception as e:
            logger.error(f"获取基金持仓数据失败: {e}")
            return self._get_empty_fund_result()

    def get_shareholder_flow(self, symbol: str) -> Dict:
        """
        获取股东户数变化数据

        Args:
            symbol: 股票代码

        Returns:
            Dict: 股东户数分析结果
        """
        try:
            logger.info(f"获取 {symbol} 股东户数数据...")

            # 获取股东户数变化
            shareholder_data = ak.stock_zh_a_gdhs(symbol=symbol)

            if shareholder_data.empty:
                logger.warning(f"{symbol} 无股东户数数据")
                return self._get_empty_shareholder_result()

            logger.info(f"获取到 {len(shareholder_data)} 条股东户数记录")

            # 分析股东户数变化
            analysis_result = {
                'raw_data': shareholder_data,
                'trend_analysis': self._analyze_shareholder_trend(shareholder_data),
                'concentration_analysis': self._analyze_shareholder_concentration(shareholder_data),
                'retail_sentiment': self._calculate_retail_sentiment_from_shareholders(shareholder_data)
            }

            logger.info(f"{symbol} 股东户数分析完成")
            return analysis_result

        except Exception as e:
            logger.error(f"获取股东户数数据失败: {e}")
            return self._get_empty_shareholder_result()

    def get_large_order_flow(self, symbol: str) -> Dict:
        """
        获取大单资金流数据

        Args:
            symbol: 股票代码

        Returns:
            Dict: 大单资金流分析结果
        """
        try:
            logger.info(f"获取 {symbol} 大单资金流数据...")

            # 获取个股资金流详情
            individual_flow = ak.stock_individual_detail_em(symbol=symbol)

            if individual_flow.empty:
                logger.warning(f"{symbol} 无个股资金流数据")
                return self._get_empty_large_order_result()

            logger.info(f"获取到个股资金流数据")

            # 分析大单资金流
            analysis_result = {
                'raw_data': individual_flow,
                'large_order_analysis': self._analyze_large_orders(individual_flow),
                'main_force_analysis': self._analyze_main_force_flow(individual_flow),
                'flow_intensity': self._calculate_flow_intensity(individual_flow)
            }

            logger.info(f"{symbol} 大单资金流分析完成")
            return analysis_result

        except Exception as e:
            logger.error(f"获取大单资金流数据失败: {e}")
            return self._get_empty_large_order_result()

    def comprehensive_fund_flow_analysis(self, symbol: str, start_date: str, end_date: str) -> Dict:
        """
        综合资金流分析

        Args:
            symbol: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict: 综合分析结果
        """
        try:
            logger.info(f"开始 {symbol} 综合资金流分析...")

            # 获取各类数据
            lhb_result = self.get_lhb_fund_flow(symbol, start_date, end_date)
            fund_result = self.get_fund_holding_flow(symbol)
            shareholder_result = self.get_shareholder_flow(symbol)
            large_order_result = self.get_large_order_flow(symbol)

            # 综合分析
            comprehensive_result = {
                'symbol': symbol,
                'analysis_period': f"{start_date} to {end_date}",
                'timestamp': datetime.now().isoformat(),

                # 各模块结果
                'lhb_analysis': lhb_result,
                'fund_analysis': fund_result,
                'shareholder_analysis': shareholder_result,
                'large_order_analysis': large_order_result,

                # 综合评估
                'comprehensive_score': self._calculate_comprehensive_score(
                    lhb_result, fund_result, shareholder_result, large_order_result
                ),
                'risk_assessment': self._assess_comprehensive_risk(
                    lhb_result, fund_result, shareholder_result, large_order_result
                ),
                'investment_signals': self._generate_investment_signals(
                    lhb_result, fund_result, shareholder_result, large_order_result
                )
            }

            logger.info(f"{symbol} 综合资金流分析完成")
            return comprehensive_result

        except Exception as e:
            logger.error(f"综合资金流分析失败: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _analyze_hot_money_from_lhb(self, lhb_data: pd.DataFrame) -> Dict:
        """从龙虎榜数据分析游资活动"""
        hot_money_analysis = {
            'total_hot_money_buy': 0.0,
            'total_hot_money_sell': 0.0,
            'net_hot_money_flow': 0.0,
            'active_hot_money_seats': 0,
            'hot_money_activity_score': 0.0,
            'top_hot_money_seats': []
        }

        try:
            # 筛选游资席位
            hot_money_seats = lhb_data[
                ~lhb_data['营业部名称'].str.contains('|'.join(self.institution_keywords), na=False)
            ]

            if not hot_money_seats.empty:
                # 计算游资买卖金额
                hot_money_analysis['total_hot_money_buy'] = hot_money_seats['买入金额'].sum()
                hot_money_analysis['total_hot_money_sell'] = hot_money_seats['卖出金额'].sum()
                hot_money_analysis['net_hot_money_flow'] = (
                    hot_money_analysis['total_hot_money_buy'] -
                    hot_money_analysis['total_hot_money_sell']
                )

                # 活跃席位数
                hot_money_analysis['active_hot_money_seats'] = len(hot_money_seats['营业部名称'].unique())

                # 活跃度评分
                total_amount = hot_money_analysis['total_hot_money_buy'] + hot_money_analysis['total_hot_money_sell']
                hot_money_analysis['hot_money_activity_score'] = min(1.0, total_amount / (self.hot_money_threshold * 10))

                # 前5大游资席位
                seat_summary = hot_money_seats.groupby('营业部名称').agg({
                    '买入金额': 'sum',
                    '卖出金额': 'sum'
                }).reset_index()
                seat_summary['净买入'] = seat_summary['买入金额'] - seat_summary['卖出金额']
                top_seats = seat_summary.nlargest(5, '净买入')

                hot_money_analysis['top_hot_money_seats'] = top_seats.to_dict('records')

        except Exception as e:
            logger.warning(f"游资分析失败: {e}")

        return hot_money_analysis

    def _analyze_institution_from_lhb(self, lhb_data: pd.DataFrame) -> Dict:
        """从龙虎榜数据分析机构活动"""
        institution_analysis = {
            'total_institution_buy': 0.0,
            'total_institution_sell': 0.0,
            'net_institution_flow': 0.0,
            'active_institution_seats': 0,
            'institution_types': {},
            'top_institution_seats': []
        }

        try:
            # 筛选机构席位
            institution_seats = lhb_data[
                lhb_data['营业部名称'].str.contains('|'.join(self.institution_keywords), na=False)
            ]

            if not institution_seats.empty:
                # 计算机构买卖金额
                institution_analysis['total_institution_buy'] = institution_seats['买入金额'].sum()
                institution_analysis['total_institution_sell'] = institution_seats['卖出金额'].sum()
                institution_analysis['net_institution_flow'] = (
                    institution_analysis['total_institution_buy'] -
                    institution_analysis['total_institution_sell']
                )

                # 活跃席位数
                institution_analysis['active_institution_seats'] = len(institution_seats['营业部名称'].unique())

                # 机构类型分析
                for keyword in self.institution_keywords:
                    matching_seats = institution_seats[
                        institution_seats['营业部名称'].str.contains(keyword, na=False)
                    ]
                    if not matching_seats.empty:
                        institution_analysis['institution_types'][keyword] = {
                            'count': len(matching_seats),
                            'net_flow': matching_seats['买入金额'].sum() - matching_seats['卖出金额'].sum()
                        }

                # 前5大机构席位
                seat_summary = institution_seats.groupby('营业部名称').agg({
                    '买入金额': 'sum',
                    '卖出金额': 'sum'
                }).reset_index()
                seat_summary['净买入'] = seat_summary['买入金额'] - seat_summary['卖出金额']
                top_seats = seat_summary.nlargest(5, '净买入')

                institution_analysis['top_institution_seats'] = top_seats.to_dict('records')

        except Exception as e:
            logger.warning(f"机构分析失败: {e}")

        return institution_analysis

    def _analyze_seat_activity(self, lhb_data: pd.DataFrame) -> Dict:
        """分析席位活跃度"""
        seat_analysis = {
            'total_seats': 0,
            'buy_seats': 0,
            'sell_seats': 0,
            'net_buy_seats': 0,
            'seat_concentration': 0.0,
            'most_active_seats': []
        }

        try:
            if not lhb_data.empty:
                # 按席位汇总
                seat_summary = lhb_data.groupby('营业部名称').agg({
                    '买入金额': 'sum',
                    '卖出金额': 'sum'
                }).reset_index()

                seat_summary['净买入'] = seat_summary['买入金额'] - seat_summary['卖出金额']
                seat_summary['总金额'] = seat_summary['买入金额'] + seat_summary['卖出金额']

                seat_analysis['total_seats'] = len(seat_summary)
                seat_analysis['buy_seats'] = len(seat_summary[seat_summary['净买入'] > 0])
                seat_analysis['sell_seats'] = len(seat_summary[seat_summary['净买入'] < 0])
                seat_analysis['net_buy_seats'] = seat_analysis['buy_seats'] - seat_analysis['sell_seats']

                # 席位集中度（前5大席位占比）
                if len(seat_summary) > 0:
                    top5_amount = seat_summary.nlargest(5, '总金额')['总金额'].sum()
                    total_amount = seat_summary['总金额'].sum()
                    seat_analysis['seat_concentration'] = top5_amount / total_amount if total_amount > 0 else 0

                # 最活跃席位
                seat_analysis['most_active_seats'] = seat_summary.nlargest(10, '总金额').to_dict('records')

        except Exception as e:
            logger.warning(f"席位活跃度分析失败: {e}")

        return seat_analysis

    def _summarize_lhb_flows(self, lhb_data: pd.DataFrame) -> Dict:
        """汇总龙虎榜资金流"""
        flow_summary = {
            'total_buy_amount': 0.0,
            'total_sell_amount': 0.0,
            'net_flow': 0.0,
            'flow_ratio': 0.0,
            'trading_days': 0,
            'avg_daily_flow': 0.0
        }

        try:
            if not lhb_data.empty:
                flow_summary['total_buy_amount'] = lhb_data['买入金额'].sum()
                flow_summary['total_sell_amount'] = lhb_data['卖出金额'].sum()
                flow_summary['net_flow'] = flow_summary['total_buy_amount'] - flow_summary['total_sell_amount']

                total_amount = flow_summary['total_buy_amount'] + flow_summary['total_sell_amount']
                flow_summary['flow_ratio'] = flow_summary['net_flow'] / total_amount if total_amount > 0 else 0

                # 交易天数
                flow_summary['trading_days'] = len(lhb_data['交易日期'].unique())
                flow_summary['avg_daily_flow'] = flow_summary['net_flow'] / flow_summary['trading_days'] if flow_summary['trading_days'] > 0 else 0

        except Exception as e:
            logger.warning(f"资金流汇总失败: {e}")

        return flow_summary

    def _analyze_fund_holdings(self, fund_data: pd.DataFrame) -> Dict:
        """分析基金持仓"""
        holding_analysis = {
            'total_funds': 0,
            'total_holding_value': 0.0,
            'avg_holding_ratio': 0.0,
            'holding_change_trend': 'stable',
            'top_funds': []
        }

        try:
            if not fund_data.empty:
                holding_analysis['total_funds'] = len(fund_data)

                # 持仓价值和比例
                if '持仓市值' in fund_data.columns:
                    holding_analysis['total_holding_value'] = fund_data['持仓市值'].sum()

                if '持仓比例' in fund_data.columns:
                    holding_analysis['avg_holding_ratio'] = fund_data['持仓比例'].mean()

                # 前10大基金
                if '基金名称' in fund_data.columns and '持仓市值' in fund_data.columns:
                    top_funds = fund_data.nlargest(10, '持仓市值')[['基金名称', '持仓市值', '持仓比例']]
                    holding_analysis['top_funds'] = top_funds.to_dict('records')

        except Exception as e:
            logger.warning(f"基金持仓分析失败: {e}")

        return holding_analysis

    def _analyze_fund_types(self, fund_data: pd.DataFrame) -> Dict:
        """分析基金类型分布"""
        type_analysis = {
            'fund_types': {},
            'type_concentration': 0.0,
            'dominant_type': None
        }

        try:
            if not fund_data.empty and '基金类型' in fund_data.columns:
                type_counts = fund_data['基金类型'].value_counts()
                type_analysis['fund_types'] = type_counts.to_dict()

                # 类型集中度
                if len(type_counts) > 0:
                    type_analysis['type_concentration'] = type_counts.iloc[0] / len(fund_data)
                    type_analysis['dominant_type'] = type_counts.index[0]

        except Exception as e:
            logger.warning(f"基金类型分析失败: {e}")

        return type_analysis

    def _analyze_holding_concentration(self, fund_data: pd.DataFrame) -> Dict:
        """分析持仓集中度"""
        concentration_analysis = {
            'top5_concentration': 0.0,
            'top10_concentration': 0.0,
            'herfindahl_index': 0.0
        }

        try:
            if not fund_data.empty and '持仓比例' in fund_data.columns:
                holding_ratios = fund_data['持仓比例'].sort_values(ascending=False)

                # 前5和前10集中度
                if len(holding_ratios) >= 5:
                    concentration_analysis['top5_concentration'] = holding_ratios.head(5).sum()
                if len(holding_ratios) >= 10:
                    concentration_analysis['top10_concentration'] = holding_ratios.head(10).sum()

                # 赫芬达尔指数
                concentration_analysis['herfindahl_index'] = (holding_ratios ** 2).sum()

        except Exception as e:
            logger.warning(f"持仓集中度分析失败: {e}")

        return concentration_analysis

    def _analyze_shareholder_trend(self, shareholder_data: pd.DataFrame) -> Dict:
        """分析股东户数趋势"""
        trend_analysis = {
            'latest_shareholder_count': 0,
            'shareholder_change_rate': 0.0,
            'trend_direction': 'stable',
            'trend_strength': 0.0,
            'historical_data': []
        }

        try:
            if not shareholder_data.empty:
                # 最新股东户数
                if '股东户数' in shareholder_data.columns:
                    trend_analysis['latest_shareholder_count'] = int(shareholder_data['股东户数'].iloc[-1])

                    # 计算变化率
                    if len(shareholder_data) > 1:
                        current_count = shareholder_data['股东户数'].iloc[-1]
                        previous_count = shareholder_data['股东户数'].iloc[-2]
                        trend_analysis['shareholder_change_rate'] = (current_count - previous_count) / previous_count

                        # 趋势方向
                        if trend_analysis['shareholder_change_rate'] > 0.05:
                            trend_analysis['trend_direction'] = 'increasing'
                        elif trend_analysis['shareholder_change_rate'] < -0.05:
                            trend_analysis['trend_direction'] = 'decreasing'
                        else:
                            trend_analysis['trend_direction'] = 'stable'

                        # 趋势强度
                        trend_analysis['trend_strength'] = abs(trend_analysis['shareholder_change_rate'])

                # 历史数据
                if len(shareholder_data) > 0:
                    historical = shareholder_data[['截止日期', '股东户数', '人均持股金额']].tail(10)
                    trend_analysis['historical_data'] = historical.to_dict('records')

        except Exception as e:
            logger.warning(f"股东趋势分析失败: {e}")

        return trend_analysis

    def _analyze_shareholder_concentration(self, shareholder_data: pd.DataFrame) -> Dict:
        """分析股东集中度"""
        concentration_analysis = {
            'concentration_trend': 'stable',
            'avg_holding_per_person': 0.0,
            'concentration_score': 0.5
        }

        try:
            if not shareholder_data.empty and '人均持股金额' in shareholder_data.columns:
                # 人均持股金额
                concentration_analysis['avg_holding_per_person'] = float(shareholder_data['人均持股金额'].iloc[-1])

                # 集中度趋势
                if len(shareholder_data) > 1:
                    current_avg = shareholder_data['人均持股金额'].iloc[-1]
                    previous_avg = shareholder_data['人均持股金额'].iloc[-2]
                    change_rate = (current_avg - previous_avg) / previous_avg

                    if change_rate > 0.1:
                        concentration_analysis['concentration_trend'] = 'increasing'
                    elif change_rate < -0.1:
                        concentration_analysis['concentration_trend'] = 'decreasing'
                    else:
                        concentration_analysis['concentration_trend'] = 'stable'

                # 集中度评分（基于人均持股金额）
                if concentration_analysis['avg_holding_per_person'] > 100000:  # 10万以上
                    concentration_analysis['concentration_score'] = 0.8
                elif concentration_analysis['avg_holding_per_person'] > 50000:  # 5万以上
                    concentration_analysis['concentration_score'] = 0.6
                else:
                    concentration_analysis['concentration_score'] = 0.4

        except Exception as e:
            logger.warning(f"股东集中度分析失败: {e}")

        return concentration_analysis

    def _calculate_retail_sentiment_from_shareholders(self, shareholder_data: pd.DataFrame) -> Dict:
        """基于股东户数计算散户情绪"""
        sentiment_analysis = {
            'sentiment_score': 0.5,
            'sentiment_trend': 'neutral',
            'confidence': 0.5
        }

        try:
            if not shareholder_data.empty and len(shareholder_data) > 1:
                # 基于股东户数变化计算情绪
                current_count = shareholder_data['股东户数'].iloc[-1]
                previous_count = shareholder_data['股东户数'].iloc[-2]
                change_rate = (current_count - previous_count) / previous_count

                # 股东户数减少通常表示散户情绪乐观（筹码集中）
                sentiment_score = 0.5 - change_rate * 2  # 反向关系
                sentiment_analysis['sentiment_score'] = max(0, min(1, sentiment_score))

                # 情绪趋势
                if sentiment_analysis['sentiment_score'] > 0.6:
                    sentiment_analysis['sentiment_trend'] = 'optimistic'
                elif sentiment_analysis['sentiment_score'] < 0.4:
                    sentiment_analysis['sentiment_trend'] = 'pessimistic'
                else:
                    sentiment_analysis['sentiment_trend'] = 'neutral'

                # 置信度基于变化幅度
                sentiment_analysis['confidence'] = min(1.0, abs(change_rate) * 10)

        except Exception as e:
            logger.warning(f"散户情绪计算失败: {e}")

        return sentiment_analysis

    def _analyze_large_orders(self, flow_data: pd.DataFrame) -> Dict:
        """分析大单资金流"""
        large_order_analysis = {
            'large_order_net_inflow': 0.0,
            'large_order_ratio': 0.0,
            'main_force_activity': 'low',
            'flow_intensity': 0.0
        }

        try:
            if not flow_data.empty:
                # 大单净流入
                if '大单净流入' in flow_data.columns:
                    large_order_analysis['large_order_net_inflow'] = float(flow_data['大单净流入'].iloc[0])

                # 大单净流入占比
                if '大单净流入占比' in flow_data.columns:
                    large_order_analysis['large_order_ratio'] = float(flow_data['大单净流入占比'].iloc[0])

                # 主力活跃度
                if abs(large_order_analysis['large_order_ratio']) > 0.1:
                    large_order_analysis['main_force_activity'] = 'high'
                elif abs(large_order_analysis['large_order_ratio']) > 0.05:
                    large_order_analysis['main_force_activity'] = 'medium'
                else:
                    large_order_analysis['main_force_activity'] = 'low'

                # 资金流强度
                large_order_analysis['flow_intensity'] = abs(large_order_analysis['large_order_net_inflow']) / 100000000  # 以亿为单位

        except Exception as e:
            logger.warning(f"大单分析失败: {e}")

        return large_order_analysis

    def _analyze_main_force_flow(self, flow_data: pd.DataFrame) -> Dict:
        """分析主力资金流"""
        main_force_analysis = {
            'main_net_inflow': 0.0,
            'main_inflow_ratio': 0.0,
            'super_large_net_inflow': 0.0,
            'flow_direction': 'neutral'
        }

        try:
            if not flow_data.empty:
                # 主力净流入
                if '主力净流入' in flow_data.columns:
                    main_force_analysis['main_net_inflow'] = float(flow_data['主力净流入'].iloc[0])

                # 主力净流入占比
                if '主力净流入占比' in flow_data.columns:
                    main_force_analysis['main_inflow_ratio'] = float(flow_data['主力净流入占比'].iloc[0])

                # 超大单净流入
                if '超大单净流入' in flow_data.columns:
                    main_force_analysis['super_large_net_inflow'] = float(flow_data['超大单净流入'].iloc[0])

                # 资金流方向
                if main_force_analysis['main_net_inflow'] > 0:
                    main_force_analysis['flow_direction'] = 'inflow'
                elif main_force_analysis['main_net_inflow'] < 0:
                    main_force_analysis['flow_direction'] = 'outflow'
                else:
                    main_force_analysis['flow_direction'] = 'neutral'

        except Exception as e:
            logger.warning(f"主力资金流分析失败: {e}")

        return main_force_analysis

    def _calculate_flow_intensity(self, flow_data: pd.DataFrame) -> Dict:
        """计算资金流强度"""
        intensity_analysis = {
            'overall_intensity': 0.0,
            'inflow_intensity': 0.0,
            'outflow_intensity': 0.0,
            'intensity_level': 'low'
        }

        try:
            if not flow_data.empty:
                # 计算各类资金流强度
                main_inflow = flow_data.get('主力净流入', pd.Series([0])).iloc[0]
                large_inflow = flow_data.get('大单净流入', pd.Series([0])).iloc[0]

                # 综合强度
                total_inflow = abs(main_inflow) + abs(large_inflow)
                intensity_analysis['overall_intensity'] = total_inflow / 100000000  # 以亿为单位

                # 流入流出强度
                if main_inflow > 0:
                    intensity_analysis['inflow_intensity'] = main_inflow / 100000000
                else:
                    intensity_analysis['outflow_intensity'] = abs(main_inflow) / 100000000

                # 强度等级
                if intensity_analysis['overall_intensity'] > 5:
                    intensity_analysis['intensity_level'] = 'very_high'
                elif intensity_analysis['overall_intensity'] > 2:
                    intensity_analysis['intensity_level'] = 'high'
                elif intensity_analysis['overall_intensity'] > 0.5:
                    intensity_analysis['intensity_level'] = 'medium'
                else:
                    intensity_analysis['intensity_level'] = 'low'

        except Exception as e:
            logger.warning(f"资金流强度计算失败: {e}")

        return intensity_analysis

    def _calculate_comprehensive_score(self, lhb_result: Dict, fund_result: Dict,
                                     shareholder_result: Dict, large_order_result: Dict) -> float:
        """计算综合评分"""
        try:
            scores = []

            # 龙虎榜评分
            if lhb_result.get('flow_summary', {}).get('net_flow', 0) > 0:
                scores.append(0.7)
            else:
                scores.append(0.3)

            # 基金持仓评分
            if fund_result.get('holding_analysis', {}).get('total_funds', 0) > 10:
                scores.append(0.8)
            else:
                scores.append(0.5)

            # 股东户数评分
            shareholder_trend = shareholder_result.get('trend_analysis', {}).get('trend_direction', 'stable')
            if shareholder_trend == 'decreasing':  # 股东户数减少是好事
                scores.append(0.8)
            elif shareholder_trend == 'increasing':
                scores.append(0.3)
            else:
                scores.append(0.5)

            # 大单资金评分
            large_order_flow = large_order_result.get('large_order_analysis', {}).get('large_order_net_inflow', 0)
            if large_order_flow > 0:
                scores.append(0.7)
            else:
                scores.append(0.3)

            return np.mean(scores) if scores else 0.5

        except Exception as e:
            logger.warning(f"综合评分计算失败: {e}")
            return 0.5

    def _assess_comprehensive_risk(self, lhb_result: Dict, fund_result: Dict,
                                 shareholder_result: Dict, large_order_result: Dict) -> str:
        """评估综合风险"""
        try:
            risk_factors = 0

            # 龙虎榜风险
            if lhb_result.get('flow_summary', {}).get('net_flow', 0) < 0:
                risk_factors += 1

            # 基金持仓风险
            if fund_result.get('holding_analysis', {}).get('total_funds', 0) < 5:
                risk_factors += 1

            # 股东户数风险
            shareholder_trend = shareholder_result.get('trend_analysis', {}).get('trend_direction', 'stable')
            if shareholder_trend == 'increasing':
                risk_factors += 1

            # 大单资金风险
            large_order_flow = large_order_result.get('large_order_analysis', {}).get('large_order_net_inflow', 0)
            if large_order_flow < 0:
                risk_factors += 1

            if risk_factors >= 3:
                return 'high'
            elif risk_factors >= 2:
                return 'medium'
            else:
                return 'low'

        except Exception as e:
            logger.warning(f"风险评估失败: {e}")
            return 'medium'

    def _generate_investment_signals(self, lhb_result: Dict, fund_result: Dict,
                                   shareholder_result: Dict, large_order_result: Dict) -> List[str]:
        """生成投资信号"""
        signals = []

        try:
            # 龙虎榜信号
            lhb_flow = lhb_result.get('flow_summary', {}).get('net_flow', 0)
            if lhb_flow > 50000000:  # 5000万以上净流入
                signals.append('strong_institutional_buying')
            elif lhb_flow < -50000000:
                signals.append('institutional_selling_pressure')

            # 游资信号
            hot_money_flow = lhb_result.get('hot_money_analysis', {}).get('net_hot_money_flow', 0)
            if hot_money_flow > 100000000:  # 1亿以上游资净流入
                signals.append('hot_money_accumulation')

            # 基金持仓信号
            fund_count = fund_result.get('holding_analysis', {}).get('total_funds', 0)
            if fund_count > 20:
                signals.append('strong_institutional_support')

            # 股东户数信号
            shareholder_trend = shareholder_result.get('trend_analysis', {}).get('trend_direction', 'stable')
            if shareholder_trend == 'decreasing':
                signals.append('chip_concentration')

            # 大单资金信号
            main_force_flow = large_order_result.get('main_force_analysis', {}).get('main_net_inflow', 0)
            if main_force_flow > 100000000:
                signals.append('main_force_accumulation')
            elif main_force_flow < -100000000:
                signals.append('main_force_distribution')

        except Exception as e:
            logger.warning(f"投资信号生成失败: {e}")

        return signals

    def _get_empty_lhb_result(self) -> Dict:
        """获取空的龙虎榜结果"""
        return {
            'raw_data': pd.DataFrame(),
            'hot_money_analysis': {
                'total_hot_money_buy': 0.0,
                'total_hot_money_sell': 0.0,
                'net_hot_money_flow': 0.0,
                'active_hot_money_seats': 0,
                'hot_money_activity_score': 0.0,
                'top_hot_money_seats': []
            },
            'institution_analysis': {
                'total_institution_buy': 0.0,
                'total_institution_sell': 0.0,
                'net_institution_flow': 0.0,
                'active_institution_seats': 0,
                'institution_types': {},
                'top_institution_seats': []
            },
            'seat_analysis': {
                'total_seats': 0,
                'buy_seats': 0,
                'sell_seats': 0,
                'net_buy_seats': 0,
                'seat_concentration': 0.0,
                'most_active_seats': []
            },
            'flow_summary': {
                'total_buy_amount': 0.0,
                'total_sell_amount': 0.0,
                'net_flow': 0.0,
                'flow_ratio': 0.0,
                'trading_days': 0,
                'avg_daily_flow': 0.0
            }
        }

    def _get_empty_fund_result(self) -> Dict:
        """获取空的基金结果"""
        return {
            'raw_data': pd.DataFrame(),
            'holding_analysis': {
                'total_funds': 0,
                'total_holding_value': 0.0,
                'avg_holding_ratio': 0.0,
                'holding_change_trend': 'stable',
                'top_funds': []
            },
            'fund_type_analysis': {
                'fund_types': {},
                'type_concentration': 0.0,
                'dominant_type': None
            },
            'concentration_analysis': {
                'top5_concentration': 0.0,
                'top10_concentration': 0.0,
                'herfindahl_index': 0.0
            }
        }

    def _get_empty_shareholder_result(self) -> Dict:
        """获取空的股东结果"""
        return {
            'raw_data': pd.DataFrame(),
            'trend_analysis': {
                'latest_shareholder_count': 0,
                'shareholder_change_rate': 0.0,
                'trend_direction': 'stable',
                'trend_strength': 0.0,
                'historical_data': []
            },
            'concentration_analysis': {
                'concentration_trend': 'stable',
                'avg_holding_per_person': 0.0,
                'concentration_score': 0.5
            },
            'retail_sentiment': {
                'sentiment_score': 0.5,
                'sentiment_trend': 'neutral',
                'confidence': 0.5
            }
        }

    def _get_empty_large_order_result(self) -> Dict:
        """获取空的大单结果"""
        return {
            'raw_data': pd.DataFrame(),
            'large_order_analysis': {
                'large_order_net_inflow': 0.0,
                'large_order_ratio': 0.0,
                'main_force_activity': 'low',
                'flow_intensity': 0.0
            },
            'main_force_analysis': {
                'main_net_inflow': 0.0,
                'main_inflow_ratio': 0.0,
                'super_large_net_inflow': 0.0,
                'flow_direction': 'neutral'
            },
            'flow_intensity': {
                'overall_intensity': 0.0,
                'inflow_intensity': 0.0,
                'outflow_intensity': 0.0,
                'intensity_level': 'low'
            }
        }
