{"data_collection_20250525_234957": {"task_id": "data_collection_20250525_234957", "task_type": "data_collection", "module": "monitor_system", "function": "run_data_collection", "params": {}, "priority": "MEDIUM", "schedule_time": "2025-05-25T23:49:57.171141", "timeout": 300, "status": "COMPLETED", "created_at": "2025-05-25T23:49:57.171141", "started_at": "2025-05-25T23:49:57.171141", "completed_at": "2025-05-25T23:50:01.221178", "result": null, "error": null, "retry_count": 0, "max_retries": 3}, "anomaly_detection_20250525_234957": {"task_id": "anomaly_detection_20250525_234957", "task_type": "anomaly_detection", "module": "monitor_system", "function": "run_anomaly_detection", "params": {}, "priority": "HIGH", "schedule_time": "2025-05-25T23:49:57.171141", "timeout": 300, "status": "COMPLETED", "created_at": "2025-05-25T23:49:57.171141", "started_at": "2025-05-25T23:49:57.171141", "completed_at": "2025-05-25T23:49:57.171141", "result": null, "error": null, "retry_count": 0, "max_retries": 3}}