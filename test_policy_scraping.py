"""
测试修复后的政策抓取功能
"""

import pandas as pd
import time
from datetime import datetime
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('policy_scraping_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_policy_scraping():
    """测试政策抓取功能"""
    
    logger.info("=" * 80)
    logger.info("测试修复后的政策抓取功能")
    logger.info("=" * 80)
    
    try:
        from data_sources.policy_data import PolicyDataSource
        policy_data = PolicyDataSource()
        logger.info("✅ PolicyDataSource 初始化成功")
    except Exception as e:
        logger.error(f"❌ PolicyDataSource 初始化失败: {e}")
        return False
    
    # 测试结果统计
    results = {}
    
    # 1. 测试国家政策抓取（限制为50条进行测试）
    logger.info("\n" + "=" * 50)
    logger.info("测试国家政策抓取")
    logger.info("=" * 50)
    
    start_time = time.time()
    try:
        gov_policies = policy_data.get_gov_policy(page=1, limit=50)
        elapsed = time.time() - start_time
        results['国家政策'] = {
            'count': len(gov_policies),
            'time': elapsed,
            'status': 'success' if not gov_policies.empty else 'empty'
        }
        
        logger.info(f"✅ 国家政策: {len(gov_policies)} 条, 耗时: {elapsed:.2f}秒")
        if not gov_policies.empty:
            logger.info(f"   列名: {list(gov_policies.columns)}")
            logger.info(f"   示例标题: {gov_policies['title'].iloc[0][:50]}...")
            logger.info(f"   示例来源: {gov_policies['source'].iloc[0]}")
            
            # 显示前5条政策
            logger.info("\n前5条政策:")
            for i, row in gov_policies.head(5).iterrows():
                logger.info(f"  {i+1}. {row['title'][:60]}... ({row['source']})")
        else:
            logger.warning("⚠️ 国家政策抓取返回空数据")
            
    except Exception as e:
        results['国家政策'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 国家政策抓取失败: {e}")
    
    # 2. 测试发改委政策抓取（限制为50条进行测试）
    logger.info("\n" + "=" * 50)
    logger.info("测试发改委政策抓取")
    logger.info("=" * 50)
    
    start_time = time.time()
    try:
        ndrc_policies = policy_data.get_ndrc_policy(page=1, limit=50)
        elapsed = time.time() - start_time
        results['发改委政策'] = {
            'count': len(ndrc_policies),
            'time': elapsed,
            'status': 'success' if not ndrc_policies.empty else 'empty'
        }
        
        logger.info(f"✅ 发改委政策: {len(ndrc_policies)} 条, 耗时: {elapsed:.2f}秒")
        if not ndrc_policies.empty:
            logger.info(f"   列名: {list(ndrc_policies.columns)}")
            logger.info(f"   示例标题: {ndrc_policies['title'].iloc[0][:50]}...")
            logger.info(f"   示例来源: {ndrc_policies['source'].iloc[0]}")
            
            # 显示分类分布
            if 'category' in ndrc_policies.columns:
                category_counts = ndrc_policies['category'].value_counts()
                logger.info("   分类分布:")
                for category, count in category_counts.items():
                    logger.info(f"     {category}: {count} 条")
            
            # 显示前5条政策
            logger.info("\n前5条政策:")
            for i, row in ndrc_policies.head(5).iterrows():
                logger.info(f"  {i+1}. {row['title'][:60]}... ({row['source']})")
        else:
            logger.warning("⚠️ 发改委政策抓取返回空数据")
            
    except Exception as e:
        results['发改委政策'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 发改委政策抓取失败: {e}")
    
    # 3. 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("政策抓取测试结果")
    logger.info("=" * 80)
    
    success_count = sum(1 for r in results.values() if r['status'] == 'success')
    empty_count = sum(1 for r in results.values() if r['status'] == 'empty')
    error_count = sum(1 for r in results.values() if r['status'] == 'error')
    total_policies = sum(r['count'] for r in results.values())
    total_time = sum(r['time'] for r in results.values())
    
    logger.info(f"📊 政策源总数: {len(results)}")
    logger.info(f"✅ 成功获取数据: {success_count}")
    logger.info(f"⚠️ 返回空数据: {empty_count}")
    logger.info(f"❌ 抓取失败: {error_count}")
    logger.info(f"📈 总政策数量: {total_policies}")
    logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
    
    # 详细结果
    logger.info("\n详细结果:")
    for source_name, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "⚠️" if result['status'] == 'empty' else "❌"
        logger.info(f"{status_icon} {source_name}: {result['count']} 条, {result['time']:.2f}秒")
        if result['status'] == 'error':
            logger.info(f"   错误: {result.get('error', '未知错误')}")
    
    # 判断测试是否成功
    success = total_policies > 0
    
    if success:
        logger.info("\n🎉 政策抓取测试成功！")
        logger.info(f"成功获取 {total_policies} 条政策数据")
    else:
        logger.warning("\n⚠️ 政策抓取测试失败，所有数据源都返回空数据")
    
    return success, total_policies

if __name__ == "__main__":
    logger.info(f"开始政策抓取测试 - {datetime.now()}")
    
    success, total_policies = test_policy_scraping()
    
    logger.info("\n" + "=" * 80)
    logger.info("测试完成")
    logger.info("=" * 80)
    logger.info(f"测试结束时间: {datetime.now()}")
    logger.info("详细日志已保存到: policy_scraping_test.log")
    
    if success:
        logger.info(f"🎯 下一步: 可以将限制提高到1000条进行大规模抓取")
    else:
        logger.info("🔧 需要进一步调试政策抓取逻辑")
