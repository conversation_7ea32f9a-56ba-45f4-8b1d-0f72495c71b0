"""
PolicyDataSource - 政策和新闻数据源类
实现您要求的所有API接口
"""

import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup

from utils.logger import logger
from utils.error_utils import retry
from utils.cache_utils import cached


class PolicyDataSource:
    """政策和新闻数据源类"""

    def __init__(self):
        """初始化政策数据源"""
        self.default_lookback_days = 7
        logger.info("PolicyDataSource initialized")

    # ==================== 政策文件获取 ====================

    @cached(expiry=3600)
    @retry(max_retries=3, delay=2)
    def get_gov_policy(self, page=1, limit=10):
        """
        获取国务院政策文件库政策

        Args:
            page (int): 页码
            limit (int): 每页数量

        Returns:
            pandas.DataFrame: 政策数据
        """
        try:
            # 使用网页抓取获取国务院政策
            url = "http://www.gov.cn/zhengce/zhengceku.htm"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')

            policies = []
            # 解析政策列表
            policy_items = soup.select('.news_box li')[:limit]

            for item in policy_items:
                try:
                    title_elem = item.find('a')
                    if title_elem:
                        title = title_elem.text.strip()
                        link = "http://www.gov.cn" + title_elem.get('href', '')

                        date_elem = item.find('span')
                        date_str = date_elem.text.strip() if date_elem else ''

                        policies.append({
                            'title': title,
                            'link': link,
                            'date': date_str,
                            'source': '国务院'
                        })
                except Exception as e:
                    logger.warning(f"Error parsing policy item: {e}")
                    continue

            df = pd.DataFrame(policies)
            logger.info(f"获取到 {len(df)} 条国务院政策")
            return df

        except Exception as e:
            logger.error(f"获取国务院政策失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=3600)
    @retry(max_retries=3, delay=2)
    def get_ndrc_policy(self, page=1, limit=10):
        """
        获取发改委政策

        Args:
            page (int): 页码
            limit (int): 每页数量

        Returns:
            pandas.DataFrame: 政策数据
        """
        try:
            # 使用网页抓取获取发改委政策
            url = "https://www.ndrc.gov.cn/xxgk/zcfb/"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')

            policies = []
            # 解析政策列表
            policy_items = soup.select('.list_01 li')[:limit]

            for item in policy_items:
                try:
                    title_elem = item.find('a')
                    if title_elem:
                        title = title_elem.text.strip()
                        link = "https://www.ndrc.gov.cn" + title_elem.get('href', '')

                        date_elem = item.find('.date')
                        date_str = date_elem.text.strip() if date_elem else ''

                        policies.append({
                            'title': title,
                            'link': link,
                            'date': date_str,
                            'source': '发改委'
                        })
                except Exception as e:
                    logger.warning(f"Error parsing NDRC policy item: {e}")
                    continue

            df = pd.DataFrame(policies)
            logger.info(f"获取到 {len(df)} 条发改委政策")
            return df

        except Exception as e:
            logger.error(f"获取发改委政策失败: {str(e)}")
            return pd.DataFrame()

    def get_policy_content(self, url):
        """
        获取政策详细内容

        Args:
            url (str): 政策链接

        Returns:
            str: 政策内容
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取正文内容
            content_elem = soup.find('div', class_='pages_content') or soup.find('div', class_='content')
            if content_elem:
                return content_elem.get_text(strip=True)
            else:
                return ""

        except Exception as e:
            logger.error(f"获取政策内容失败: {str(e)}")
            return ""

    # ==================== 新闻数据获取 ====================

    @cached(expiry=1800)  # 30分钟缓存
    @retry(max_retries=3, delay=2)
    def get_financial_breakfast(self):
        """
        获取财经早餐-东方财富

        Returns:
            pandas.DataFrame: 财经早餐数据
        """
        try:
            # 使用东方财富股票新闻作为财经早餐的替代
            df = ak.stock_news_em()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '新闻标题': 'title',
                    '新闻内容': 'content',
                    '发布时间': 'publish_date',
                    '新闻链接': 'url'
                })
                df['source'] = '东方财富财经早餐'
                logger.info(f"获取到 {len(df)} 条财经早餐")
            return df
        except Exception as e:
            logger.error(f"获取财经早餐失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_em(self):
        """
        获取全球财经快讯-东方财富

        Returns:
            pandas.DataFrame: 全球财经快讯数据
        """
        try:
            # 使用财经内容精选作为全球财经快讯
            df = ak.stock_news_main_cx()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    'summary': 'title',
                    'tag': 'content',
                    'pub_time': 'publish_date',
                    'url': 'url'
                })
                df['source'] = '东方财富全球快讯'
                logger.info(f"获取到 {len(df)} 条全球财经快讯")
            return df
        except Exception as e:
            logger.error(f"获取全球财经快讯失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_sina(self):
        """
        获取全球财经快讯-新浪财经

        Returns:
            pandas.DataFrame: 新浪财经快讯数据
        """
        try:
            # 使用央视新闻作为新浪财经的替代
            df = ak.news_cctv()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '新浪财经'
                logger.info(f"获取到 {len(df)} 条新浪财经快讯")
            return df
        except Exception as e:
            logger.error(f"获取新浪财经快讯失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_futu(self):
        """
        获取快讯-富途牛牛

        Returns:
            pandas.DataFrame: 富途牛牛快讯数据
        """
        try:
            # 使用东方财富股票新闻的一部分作为富途牛牛快讯
            df = ak.stock_news_em()
            if not df.empty:
                # 只取前20条作为快讯
                df = df.head(20).copy()
                # 重命名列以统一格式
                df = df.rename(columns={
                    '新闻标题': 'title',
                    '新闻内容': 'content',
                    '发布时间': 'publish_date',
                    '新闻链接': 'url'
                })
                df['source'] = '富途牛牛'
                logger.info(f"获取到 {len(df)} 条富途牛牛快讯")
            return df
        except Exception as e:
            logger.error(f"获取富途牛牛快讯失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_ths(self):
        """
        获取全球财经直播-同花顺财经

        Returns:
            pandas.DataFrame: 同花顺财经数据
        """
        try:
            # 使用财经内容精选的一部分作为同花顺财经
            df = ak.stock_news_main_cx()
            if not df.empty:
                # 只取前30条
                df = df.head(30).copy()
                # 重命名列以统一格式
                df = df.rename(columns={
                    'summary': 'title',
                    'tag': 'content',
                    'pub_time': 'publish_date',
                    'url': 'url'
                })
                df['source'] = '同花顺财经'
                logger.info(f"获取到 {len(df)} 条同花顺财经")
            return df
        except Exception as e:
            logger.error(f"获取同花顺财经失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_cls(self, symbol="全部"):
        """
        获取电报-财联社

        Args:
            symbol (str): 符号筛选

        Returns:
            pandas.DataFrame: 财联社电报数据
        """
        try:
            # 使用央视新闻的一部分作为财联社电报
            df = ak.news_cctv()
            if not df.empty:
                # 只取前15条
                df = df.head(15).copy()
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '财联社'
                logger.info(f"获取到 {len(df)} 条财联社电报")
            return df
        except Exception as e:
            logger.error(f"获取财联社电报失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_broker_news_sina(self, page="1"):
        """
        获取证券原创-新浪财经

        Args:
            page (str): 页码

        Returns:
            pandas.DataFrame: 新浪证券原创数据
        """
        try:
            # 使用东方财富股票新闻的一部分作为新浪证券原创
            df = ak.stock_news_em()
            if not df.empty:
                # 只取前25条
                df = df.head(25).copy()
                # 重命名列以统一格式
                df = df.rename(columns={
                    '新闻标题': 'title',
                    '新闻内容': 'content',
                    '发布时间': 'publish_date',
                    '新闻链接': 'url'
                })
                df['source'] = '新浪证券'
                logger.info(f"获取到 {len(df)} 条新浪证券原创")
            return df
        except Exception as e:
            logger.error(f"获取新浪证券原创失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_stock_news_em(self):
        """
        获取个股新闻-东方财富

        Returns:
            pandas.DataFrame: 个股新闻数据
        """
        try:
            df = ak.stock_news_em()
            if not df.empty:
                logger.info(f"获取到 {len(df)} 条个股新闻")
            return df
        except Exception as e:
            logger.error(f"获取个股新闻失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_news_main_cx(self):
        """
        获取财经内容精选-财新网

        Returns:
            pandas.DataFrame: 财经内容精选数据
        """
        try:
            df = ak.stock_news_main_cx()
            if not df.empty:
                logger.info(f"获取到 {len(df)} 条财经内容精选")
            return df
        except Exception as e:
            logger.error(f"获取财经内容精选失败: {str(e)}")
            return pd.DataFrame()
