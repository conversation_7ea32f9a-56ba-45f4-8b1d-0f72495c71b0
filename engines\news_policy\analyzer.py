"""
Policy Analyzer module.
Responsible for parsing and quantifying policy information.
"""

import os
import re
import json
import pandas as pd
import numpy as np
from datetime import datetime

# Real imports for NLP processing
try:
    import jieba
    import jieba.analyse
    jieba.initialize()
except ImportError:
    logger.warning("jieba not installed. NLP features will be limited.")
    jieba = None

try:
    from transformers import pipeline
except ImportError:
    logger.warning("transformers not installed. Advanced NLP features will be limited.")
    pipeline = None

class AutoTokenizer:
    @staticmethod
    def from_pretrained(model_name):
        return "tokenizer"

class AutoModelForTokenClassification:
    @staticmethod
    def from_pretrained(model_name):
        return "ner_model"

class AutoModelForSequenceClassification:
    @staticmethod
    def from_pretrained(model_name):
        return "sentiment_model"

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import safe_execute
from utils.cache_utils import cached

class PolicyAnalyzer:
    """
    Class for analyzing policy information.
    """

    def __init__(self, config=None, bert_model_path=None, industry_mapping_path=None):
        """
        Initialize the PolicyAnalyzer.

        Args:
            config: Configuration object or None to use default.
            bert_model_path (str, optional): Path to BERT model.
            industry_mapping_path (str, optional): Path to industry mapping file.
        """
        self.config = config if config else config_loader

        # Load model paths from configuration if not provided
        if bert_model_path is None:
            bert_model_path = self.config.get('engines.news_policy.bert_model_path', 'models/finbert')

        if industry_mapping_path is None:
            industry_mapping_path = self.config.get('engines.news_policy.industry_mapping_path', 'data/policy_industry_mapping.json')

        self.bert_model_path = bert_model_path
        self.industry_mapping_path = industry_mapping_path

        # Load policy weights from configuration
        self.policy_source_weights = self.config.get('engines.news_policy.policy_source_weights', {})
        self.policy_type_weights = self.config.get('engines.news_policy.policy_type_weights', {})

        # Initialize NLP models
        self._init_nlp_models()

        # Load industry mapping
        self._load_industry_mapping()

        logger.info("PolicyAnalyzer initialized")

    def _init_nlp_models(self):
        """
        Initialize NLP models.
        """
        try:
            # Check if model directory exists
            if not os.path.exists(self.bert_model_path):
                logger.warning(f"BERT model path {self.bert_model_path} not found. Using default models.")
                # Use default models from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained("bert-base-chinese")
                self.ner_model = AutoModelForTokenClassification.from_pretrained("bert-base-chinese")
                self.sentiment_model = AutoModelForSequenceClassification.from_pretrained("bert-base-chinese")
            else:
                # Load models from local path
                self.tokenizer = AutoTokenizer.from_pretrained(self.bert_model_path)
                self.ner_model = AutoModelForTokenClassification.from_pretrained(self.bert_model_path)
                self.sentiment_model = AutoModelForSequenceClassification.from_pretrained(self.bert_model_path)

            # Create pipelines
            self.ner_pipeline = pipeline("ner", model=self.ner_model, tokenizer=self.tokenizer)
            self.sentiment_pipeline = pipeline("sentiment-analysis", model=self.sentiment_model, tokenizer=self.tokenizer)

            logger.info("NLP models initialized")
        except Exception as e:
            logger.error(f"Error initializing NLP models: {str(e)}")
            logger.info("Using jieba for fallback text analysis")

            # Set models to None to use jieba as fallback
            self.tokenizer = None
            self.ner_model = None
            self.sentiment_model = None
            self.ner_pipeline = None
            self.sentiment_pipeline = None

    def _load_industry_mapping(self):
        """
        Load industry mapping from file.
        """
        try:
            if os.path.exists(self.industry_mapping_path):
                with open(self.industry_mapping_path, 'r', encoding='utf-8') as f:
                    self.industry_mapping = json.load(f)
                logger.info(f"Industry mapping loaded from {self.industry_mapping_path}")
            else:
                logger.warning(f"Industry mapping file {self.industry_mapping_path} not found. Creating default mapping.")
                # Create default mapping
                self.industry_mapping = self._create_default_industry_mapping()

                # Save default mapping
                os.makedirs(os.path.dirname(self.industry_mapping_path), exist_ok=True)
                with open(self.industry_mapping_path, 'w', encoding='utf-8') as f:
                    json.dump(self.industry_mapping, f, ensure_ascii=False, indent=2)
                logger.info(f"Default industry mapping saved to {self.industry_mapping_path}")
        except Exception as e:
            logger.error(f"Error loading industry mapping: {str(e)}")
            # Create default mapping
            self.industry_mapping = self._create_default_industry_mapping()

    def _create_default_industry_mapping(self):
        """
        Create default industry mapping.

        Returns:
            dict: Default industry mapping.
        """
        # This is a simplified mapping
        # In a real system, you would have a more comprehensive mapping
        return {
            "keywords_to_industry": {
                "科技": ["计算机", "通信", "电子"],
                "金融": ["银行", "证券", "保险"],
                "医药": ["医药", "生物", "医疗"],
                "消费": ["食品饮料", "家电", "纺织服装"],
                "能源": ["石油", "煤炭", "电力"],
                "制造": ["机械", "汽车", "建材"],
                "房地产": ["房地产"]
            },
            "industry_codes": {
                "计算机": "BK0701",
                "通信": "BK0702",
                "电子": "BK0703",
                "银行": "BK0475",
                "证券": "BK0473",
                "保险": "BK0474",
                "医药": "BK0465",
                "生物": "BK0710",
                "医疗": "BK0727",
                "食品饮料": "BK0438",
                "家电": "BK0456",
                "纺织服装": "BK0436",
                "石油": "BK0464",
                "煤炭": "BK0437",
                "电力": "BK0428",
                "机械": "BK0545",
                "汽车": "BK0481",
                "建材": "BK0425",
                "房地产": "BK0451"
            }
        }

    @safe_execute(default_return={})
    def parse_document_text(self, doc_text, doc_source, doc_date, doc_type):
        """
        Parse document text.

        Args:
            doc_text (str): Document text.
            doc_source (str): Document source.
            doc_date (datetime.date): Document date.
            doc_type (str): Document type.

        Returns:
            dict: Parsed document information.
        """
        # Initialize result
        result = {
            'entities': [],
            'keywords': [],
            'main_subjects': [],
            'actions': [],
            'objects': [],
            'source_level': '',
            'policy_category': ''
        }

        # Skip empty text
        if not doc_text:
            return result

        # Extract entities using NER
        if self.ner_pipeline:
            try:
                # Limit text length to avoid token limit issues
                text_for_ner = doc_text[:1000]  # Use first 1000 characters
                entities = self.ner_pipeline(text_for_ner)

                # Process entities
                current_entity = ""
                current_type = ""

                for entity in entities:
                    if entity['entity'].startswith('B-'):  # Beginning of entity
                        if current_entity:
                            result['entities'].append({
                                'text': current_entity,
                                'type': current_type
                            })

                        current_entity = entity['word']
                        current_type = entity['entity'][2:]  # Remove 'B-' prefix
                    elif entity['entity'].startswith('I-'):  # Inside entity
                        current_entity += entity['word']

                # Add last entity
                if current_entity:
                    result['entities'].append({
                        'text': current_entity,
                        'type': current_type
                    })
            except Exception as e:
                logger.error(f"Error extracting entities with NER: {str(e)}")

        # Extract keywords using jieba
        try:
            # Extract top keywords
            keywords = jieba.analyse.extract_tags(doc_text, topK=10, withWeight=True)
            result['keywords'] = [{'text': k, 'weight': w} for k, w in keywords]
        except Exception as e:
            logger.error(f"Error extracting keywords with jieba: {str(e)}")

        # For policy documents, extract policy-specific information
        if doc_type in ['policy_document', 'policy_rss']:
            # Determine source level
            if doc_source in ["国务院", "央行", "财政部"]:
                result['source_level'] = "国家级"
            elif doc_source in ["发改委", "证监会", "银保监会"]:
                result['source_level'] = "部委级"
            elif doc_source in ["交易所"]:
                result['source_level'] = "监管机构"
            else:
                result['source_level'] = "其他"

            # Determine policy category
            policy_categories = ["规划", "条例", "意见", "通知", "公告"]
            for category in policy_categories:
                if category in doc_text[:100]:  # Check in the beginning of the document
                    result['policy_category'] = category
                    break

            # Extract policy triples (subject, action, object)
            # This is a simplified approach
            # In a real system, you would use more sophisticated methods

            # Extract main subjects
            subject_patterns = [
                r"(国务院|央行|财政部|发改委|证监会|银保监会|交易所).*?(发布|印发|公布|宣布)",
                r"(.*?)(决定|要求|规定|明确)"
            ]

            for pattern in subject_patterns:
                matches = re.finditer(pattern, doc_text)
                for match in matches:
                    subject = match.group(1).strip()
                    if subject and subject not in result['main_subjects']:
                        result['main_subjects'].append(subject)

            # Extract actions
            action_patterns = [
                r"(发布|印发|公布|宣布|决定|要求|规定|明确|支持|促进|加强|提高|降低|扩大|减少)",
            ]

            for pattern in action_patterns:
                matches = re.finditer(pattern, doc_text)
                for match in matches:
                    action = match.group(1).strip()
                    if action and action not in result['actions']:
                        result['actions'].append(action)

            # Extract objects
            # This is a simplified approach
            # In a real system, you would use dependency parsing or other NLP techniques
            for keyword in result['keywords']:
                if keyword['text'] not in result['main_subjects'] and keyword['text'] not in result['actions']:
                    result['objects'].append(keyword['text'])

        return result

    @safe_execute(default_return={})
    def calculate_policy_metrics(self, parsed_doc_info, stock_code=None, stock_industry=None):
        """
        Calculate policy metrics.

        Args:
            parsed_doc_info (dict): Parsed document information.
            stock_code (str, optional): Stock code.
            stock_industry (str, optional): Stock industry.

        Returns:
            dict: Policy metrics.
        """
        # Initialize result
        result = {
            'policy_id': f"P{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'overall_heat': 0.0,
            'industry_impacts': {},
            'stock_relevance': 0.0 if stock_code else None
        }

        # Calculate policy heat
        source_weight = self.policy_source_weights.get(parsed_doc_info.get('source', ''), 0.5)
        policy_type_weight = self.policy_type_weights.get(parsed_doc_info.get('policy_category', ''), 0.5)

        # Calculate keyword importance
        keyword_importance = 0.0
        for keyword in parsed_doc_info.get('keywords', []):
            keyword_importance += keyword.get('weight', 0.0)

        # Normalize keyword importance
        if parsed_doc_info.get('keywords'):
            keyword_importance /= len(parsed_doc_info.get('keywords'))

        # Calculate overall heat
        result['overall_heat'] = source_weight * policy_type_weight * (1.0 + keyword_importance)

        # Calculate industry impacts
        for keyword in parsed_doc_info.get('keywords', []):
            keyword_text = keyword.get('text', '')

            # Check if keyword is related to any industry
            for policy_keyword, industries in self.industry_mapping.get('keywords_to_industry', {}).items():
                if policy_keyword in keyword_text:
                    for industry in industries:
                        industry_code = self.industry_mapping.get('industry_codes', {}).get(industry, '')

                        # Determine impact direction
                        impact_direction = 0  # Neutral by default
                        for action in parsed_doc_info.get('actions', []):
                            if action in ["支持", "促进", "加强", "提高", "扩大"]:
                                impact_direction = 1  # Positive
                                break
                            elif action in ["降低", "减少", "限制", "禁止"]:
                                impact_direction = -1  # Negative
                                break

                        # Determine impact strength
                        impact_strength = keyword.get('weight', 0.0) * source_weight

                        # Add or update industry impact
                        if industry_code in result['industry_impacts']:
                            # If already exists, take the stronger impact
                            if abs(impact_direction * impact_strength) > abs(result['industry_impacts'][industry_code]['direction'] * result['industry_impacts'][industry_code]['strength']):
                                result['industry_impacts'][industry_code] = {
                                    'direction': impact_direction,
                                    'strength': impact_strength
                                }
                        else:
                            result['industry_impacts'][industry_code] = {
                                'direction': impact_direction,
                                'strength': impact_strength
                            }

        # Calculate stock relevance if stock_code and stock_industry are provided
        if stock_code and stock_industry:
            # Get industry code
            industry_code = self.industry_mapping.get('industry_codes', {}).get(stock_industry, '')

            if industry_code in result['industry_impacts']:
                # Calculate stock relevance based on industry impact
                industry_impact = result['industry_impacts'][industry_code]
                stock_relevance = industry_impact['direction'] * industry_impact['strength']

                # Check if stock is directly mentioned
                stock_mentioned = False
                for entity in parsed_doc_info.get('entities', []):
                    if stock_code in entity.get('text', ''):
                        stock_mentioned = True
                        break

                # Adjust stock relevance if stock is directly mentioned
                if stock_mentioned:
                    stock_relevance *= 1.5

                result['stock_relevance'] = stock_relevance

        return result

    @safe_execute(default_return=pd.DataFrame())
    def batch_process_documents(self, documents_df, stock_data_df=None):
        """
        Process multiple documents.

        Args:
            documents_df (pandas.DataFrame): DataFrame containing documents.
            stock_data_df (pandas.DataFrame, optional): DataFrame containing stock data.

        Returns:
            pandas.DataFrame: DataFrame containing processed documents.
        """
        # Initialize result DataFrame
        result_df = documents_df.copy()

        # Add columns for parsed information
        result_df['parsed_info'] = None
        result_df['policy_metrics'] = None

        # Process each document
        for i, row in result_df.iterrows():
            # Get document text
            if 'content' in row:
                doc_text = row['content']
            elif 'full_text' in row:
                doc_text = row['full_text']
            elif 'summary' in row:
                doc_text = row['summary']
            else:
                doc_text = ""

            # Parse document text
            parsed_info = self.parse_document_text(
                doc_text=doc_text,
                doc_source=row.get('source', ''),
                doc_date=row.get('publish_date', datetime.now().date()),
                doc_type=row.get('doc_type', '')
            )

            result_df.at[i, 'parsed_info'] = parsed_info

            # Calculate policy metrics for policy documents
            if row.get('doc_type', '') in ['policy_document', 'policy_rss']:
                policy_metrics = self.calculate_policy_metrics(parsed_info)
                result_df.at[i, 'policy_metrics'] = policy_metrics

                # Calculate stock relevance if stock_data_df is provided
                if stock_data_df is not None:
                    for j, stock_row in stock_data_df.iterrows():
                        stock_code = stock_row.get('stock_code', '')
                        stock_industry = stock_row.get('industry_name', '')

                        if stock_code and stock_industry:
                            stock_metrics = self.calculate_policy_metrics(
                                parsed_info,
                                stock_code=stock_code,
                                stock_industry=stock_industry
                            )

                            # Add stock relevance to result
                            if 'stock_relevance' not in result_df.columns:
                                result_df['stock_relevance'] = None

                            if result_df.at[i, 'stock_relevance'] is None:
                                result_df.at[i, 'stock_relevance'] = {}

                            result_df.at[i, 'stock_relevance'][stock_code] = stock_metrics.get('stock_relevance', 0.0)

        return result_df
