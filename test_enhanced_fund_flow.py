"""
测试增强版资金流分析器
验证五级资金流层级联动和游资目标股票预测功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_fund_flow_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_enhanced_fund_flow_analyzer():
    """测试增强版资金流分析器"""
    
    logger.info("=" * 80)
    logger.info("测试增强版资金流分析器")
    logger.info("=" * 80)
    
    try:
        from engines.fund_flow.enhanced_analyzer import EnhancedFundFlowAnalyzer
        
        # 初始化分析器
        config = {
            'lookback_days': 30,
            'hot_money_threshold': 50000000,
            'large_order_threshold': 1000000
        }
        
        analyzer = EnhancedFundFlowAnalyzer(config=config)
        logger.info("✅ EnhancedFundFlowAnalyzer 初始化成功")
        
        # 测试股票代码
        test_stock = '000001'
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        logger.info(f"📊 测试股票: {test_stock}")
        logger.info(f"📅 测试时间范围: {start_date} 到 {end_date}")
        
        # 1. 测试北向资金流分析
        logger.info("\n" + "=" * 60)
        logger.info("测试北向资金流分析")
        logger.info("=" * 60)
        
        try:
            northbound_data = analyzer.get_enhanced_northbound_flow(start_date, end_date)
            logger.info(f"✅ 北向资金流数据获取: {len(northbound_data)} 条记录")
            
            if not northbound_data.empty:
                logger.info(f"   数据列: {list(northbound_data.columns)}")
                logger.info(f"   最新净流入: {northbound_data['net_flow'].iloc[-1]:,.0f}")
                logger.info(f"   平均流强度: {northbound_data['flow_strength'].mean():.3f}")
            
        except Exception as e:
            logger.warning(f"⚠️ 北向资金流测试失败: {e}")
        
        # 2. 测试机构资金流分析
        logger.info("\n" + "=" * 60)
        logger.info("测试机构资金流分析")
        logger.info("=" * 60)
        
        try:
            institutional_data = analyzer.get_enhanced_institutional_flow(test_stock, start_date, end_date)
            logger.info(f"✅ 机构资金流数据获取: {len(institutional_data)} 条记录")
            
            if not institutional_data.empty:
                logger.info(f"   数据列: {list(institutional_data.columns)}")
                if 'institution_type' in institutional_data.columns:
                    inst_types = institutional_data['institution_type'].value_counts()
                    logger.info(f"   机构类型分布: {dict(inst_types)}")
            
        except Exception as e:
            logger.warning(f"⚠️ 机构资金流测试失败: {e}")
        
        # 3. 测试游资资金流分析（核心功能）
        logger.info("\n" + "=" * 60)
        logger.info("测试游资资金流分析（核心功能）")
        logger.info("=" * 60)
        
        try:
            hot_money_data = analyzer.get_enhanced_hot_money_flow(test_stock, start_date, end_date)
            logger.info(f"✅ 游资资金流数据获取: {len(hot_money_data)} 条记录")
            
            if not hot_money_data.empty:
                logger.info(f"   数据列: {list(hot_money_data.columns)}")
                logger.info(f"   平均活跃席位数: {hot_money_data['active_seats_count'].mean():.1f}")
                logger.info(f"   平均游资强度: {hot_money_data['hot_money_strength'].mean():.3f}")
                logger.info(f"   总净流入: {hot_money_data['net_amount'].sum():,.0f}")
            
        except Exception as e:
            logger.warning(f"⚠️ 游资资金流测试失败: {e}")
        
        # 4. 测试散户资金流分析
        logger.info("\n" + "=" * 60)
        logger.info("测试散户资金流分析")
        logger.info("=" * 60)
        
        try:
            retail_data = analyzer.get_enhanced_retail_flow(test_stock, start_date, end_date)
            logger.info(f"✅ 散户资金流数据获取: {len(retail_data)} 条记录")
            
            if not retail_data.empty:
                logger.info(f"   数据列: {list(retail_data.columns)}")
                if 'retail_sentiment' in retail_data.columns:
                    logger.info(f"   平均散户情绪: {retail_data['retail_sentiment'].mean():.3f}")
                if 'shareholder_count' in retail_data.columns:
                    latest_count = retail_data['shareholder_count'].iloc[-1]
                    logger.info(f"   最新股东户数: {latest_count:,}")
            
        except Exception as e:
            logger.warning(f"⚠️ 散户资金流测试失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 增强版资金流分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hot_money_prediction():
    """测试游资目标股票预测功能"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试游资目标股票预测功能")
    logger.info("=" * 80)
    
    try:
        from engines.fund_flow.enhanced_analyzer import EnhancedFundFlowAnalyzer
        
        analyzer = EnhancedFundFlowAnalyzer()
        
        # 模拟市场数据
        market_data = {
            'price_change': 0.05,      # 5%涨幅
            'volume_ratio': 2.5,       # 成交量放大2.5倍
            'market_cap': 3e10,        # 300亿市值
            'sector': 'technology'
        }
        
        # 模拟情绪数据
        sentiment_data = {
            'sentiment_score': 0.3,    # 正面情绪
            'news_count': 5,
            'policy_impact': 0.2
        }
        
        logger.info("🔮 开始游资目标股票预测...")
        predictions = analyzer.predict_hot_money_targets(market_data, sentiment_data)
        
        logger.info(f"✅ 预测完成，识别到 {len(predictions)} 只潜在目标股票")
        
        if predictions:
            logger.info("\n📊 预测结果详情:")
            for i, pred in enumerate(predictions[:5], 1):  # 显示前5只
                logger.info(f"  {i}. 股票代码: {pred['stock_code']}")
                logger.info(f"     预测评分: {pred['prediction_score']:.3f}")
                logger.info(f"     置信度: {pred['confidence']:.3f}")
                logger.info(f"     风险等级: {pred['risk_level']}")
                logger.info(f"     时间周期: {pred['time_horizon']}")
                logger.info(f"     关键因子: {pred['key_factors']}")
                logger.info(f"     入场信号: {pred['entry_signals']}")
                logger.info("")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 游资预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tier_linkage_analysis():
    """测试五级资金流层级联动分析"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试五级资金流层级联动分析")
    logger.info("=" * 80)
    
    try:
        from engines.fund_flow.enhanced_analyzer import EnhancedFundFlowAnalyzer
        
        analyzer = EnhancedFundFlowAnalyzer()
        
        # 创建模拟的五级资金流数据
        dates = pd.date_range(start='2025-01-01', periods=20, freq='D')
        
        # 模拟各层级数据
        all_tier_data = {
            'northbound': pd.DataFrame({
                'date': dates,
                'net_flow': np.random.normal(100000000, 50000000, 20),  # 北向资金
                'flow_strength': np.random.uniform(0.3, 0.8, 20)
            }),
            'institutional': pd.DataFrame({
                'date': dates,
                'net_amount': np.random.normal(50000000, 30000000, 20),  # 机构资金
                'institution_type': ['mutual_fund'] * 20
            }),
            'hot_money': pd.DataFrame({
                'date': dates,
                'net_amount': np.random.normal(80000000, 60000000, 20),  # 游资
                'hot_money_strength': np.random.uniform(0.4, 0.9, 20),
                'active_seats_count': np.random.randint(3, 15, 20)
            }),
            'margin': pd.DataFrame({
                'date': dates,
                'net_amount': np.random.normal(30000000, 20000000, 20),  # 融资
                'margin_balance': np.random.uniform(1e8, 5e8, 20)
            }),
            'retail': pd.DataFrame({
                'date': dates,
                'net_amount': np.random.normal(-20000000, 40000000, 20),  # 散户（通常流出）
                'retail_sentiment': np.random.uniform(0.2, 0.7, 20)
            })
        }
        
        test_stock = '000001'
        
        logger.info(f"🔗 分析 {test_stock} 的五级资金流层级联动...")
        linkage_result = analyzer.analyze_tier_linkage(test_stock, all_tier_data)
        
        logger.info("✅ 层级联动分析完成")
        logger.info("\n📊 联动分析结果:")
        logger.info(f"   联动强度: {linkage_result['linkage_strength']:.3f}")
        logger.info(f"   主导层级: {linkage_result['dominant_tier']}")
        logger.info(f"   资金流向: {linkage_result['flow_direction']}")
        logger.info(f"   同步性评分: {linkage_result['synchronization_score']:.3f}")
        logger.info(f"   领先指标: {linkage_result['leading_indicators']}")
        logger.info(f"   滞后指标: {linkage_result['lagging_indicators']}")
        
        # 异常检测结果
        anomalies = linkage_result['anomaly_detection']
        if anomalies:
            logger.info(f"\n⚠️ 检测到异常:")
            for tier, anomaly_info in anomalies.items():
                logger.info(f"   {tier}: {anomaly_info['anomaly_count']} 个异常点")
        else:
            logger.info("\n✅ 未检测到显著异常")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 层级联动分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_report():
    """测试综合分析报告生成"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试综合分析报告生成")
    logger.info("=" * 80)
    
    try:
        from engines.fund_flow.enhanced_analyzer import EnhancedFundFlowAnalyzer
        
        analyzer = EnhancedFundFlowAnalyzer()
        
        # 模拟分析结果
        analysis_results = {
            'tier_data': {
                'northbound': pd.DataFrame({
                    'net_amount': [100000000, 150000000, 80000000]
                }),
                'hot_money': pd.DataFrame({
                    'net_amount': [200000000, 180000000, 220000000]
                })
            },
            'linkage_analysis': {
                'linkage_strength': 0.75,
                'dominant_tier': 'hot_money',
                'flow_direction': 'inflow',
                'anomaly_detection': {}
            },
            'hot_money_predictions': [
                {
                    'stock_code': '000001',
                    'prediction_score': 0.85,
                    'confidence': 0.78,
                    'risk_level': 'medium'
                }
            ]
        }
        
        test_stock = '000001'
        
        logger.info(f"📋 生成 {test_stock} 综合分析报告...")
        report = analyzer.generate_comprehensive_report(test_stock, analysis_results)
        
        logger.info("✅ 综合报告生成完成")
        logger.info("\n📊 报告摘要:")
        logger.info(f"   股票代码: {report['stock_code']}")
        logger.info(f"   综合评分: {report['overall_score']:.3f}")
        logger.info(f"   风险评估: {report['risk_assessment']}")
        logger.info(f"   投资建议: {report['investment_recommendation']}")
        
        if report['key_insights']:
            logger.info(f"\n💡 关键洞察:")
            for insight in report['key_insights']:
                logger.info(f"   • {insight}")
        
        if report['opportunity_factors']:
            logger.info(f"\n🚀 机会因子:")
            for factor in report['opportunity_factors']:
                logger.info(f"   • {factor}")
        
        if report['risk_factors']:
            logger.info(f"\n⚠️ 风险因子:")
            for factor in report['risk_factors']:
                logger.info(f"   • {factor}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 综合报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info(f"开始增强版资金流分析器测试 - {datetime.now()}")
    
    results = {}
    
    # 执行各项测试
    results['增强版资金流分析器'] = test_enhanced_fund_flow_analyzer()
    results['游资目标预测'] = test_hot_money_prediction()
    results['层级联动分析'] = test_tier_linkage_analysis()
    results['综合报告生成'] = test_comprehensive_report()
    
    # 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("增强版资金流分析器测试结果")
    logger.info("=" * 80)
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        logger.info(f"{status} {test_name}")
    
    logger.info(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count >= 3:
        logger.info("🎉 增强版资金流分析器基本功能正常！")
        logger.info("📊 五级资金流层级联动分析已实现")
        logger.info("🎯 游资目标股票预测算法已完成")
        logger.info("🚀 可以进行波动率分析模块完善")
    else:
        logger.warning("⚠️ 增强版资金流分析器存在问题，需要进一步调试")
    
    logger.info(f"\n测试完成时间: {datetime.now()}")
    logger.info("详细日志已保存到: enhanced_fund_flow_test.log")
    
    return success_count >= 3

if __name__ == "__main__":
    main()
