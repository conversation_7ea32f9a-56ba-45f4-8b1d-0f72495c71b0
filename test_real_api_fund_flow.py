"""
测试基于真实AKShare API的资金流分析器
验证龙虎榜、基金持仓、股东户数、大单资金等真实API功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('real_api_fund_flow_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_real_api_fund_flow_analyzer():
    """测试基于真实API的资金流分析器"""
    
    logger.info("=" * 80)
    logger.info("测试基于真实AKShare API的资金流分析器")
    logger.info("=" * 80)
    
    try:
        from engines.fund_flow.real_api_analyzer import RealAPIFundFlowAnalyzer
        
        # 初始化分析器
        config = {
            'lookback_days': 30,
            'hot_money_threshold': 50000000
        }
        
        analyzer = RealAPIFundFlowAnalyzer(config=config)
        logger.info("✅ RealAPIFundFlowAnalyzer 初始化成功")
        
        # 测试股票代码（选择活跃股票）
        test_stocks = ['000001', '000002', '600519', '000858']
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        end_date = datetime.now().strftime('%Y%m%d')
        
        logger.info(f"📊 测试股票池: {test_stocks}")
        logger.info(f"📅 测试时间范围: {start_date} 到 {end_date}")
        
        test_results = {}
        
        for stock_code in test_stocks:
            logger.info(f"\n{'='*60}")
            logger.info(f"测试股票: {stock_code}")
            logger.info(f"{'='*60}")
            
            stock_results = {}
            
            # 1. 测试龙虎榜数据获取
            logger.info(f"\n🎯 测试 {stock_code} 龙虎榜数据...")
            try:
                lhb_result = analyzer.get_lhb_fund_flow(stock_code, start_date, end_date)
                
                if not lhb_result['raw_data'].empty:
                    logger.info(f"✅ 龙虎榜数据: {len(lhb_result['raw_data'])} 条记录")
                    
                    # 显示游资分析结果
                    hot_money = lhb_result['hot_money_analysis']
                    logger.info(f"   游资净流入: {hot_money['net_hot_money_flow']:,.0f}")
                    logger.info(f"   活跃游资席位: {hot_money['active_hot_money_seats']}")
                    logger.info(f"   游资活跃度: {hot_money['hot_money_activity_score']:.3f}")
                    
                    # 显示机构分析结果
                    institution = lhb_result['institution_analysis']
                    logger.info(f"   机构净流入: {institution['net_institution_flow']:,.0f}")
                    logger.info(f"   活跃机构席位: {institution['active_institution_seats']}")
                    
                    stock_results['lhb_success'] = True
                else:
                    logger.warning(f"⚠️ {stock_code} 无龙虎榜数据")
                    stock_results['lhb_success'] = False
                    
            except Exception as e:
                logger.error(f"❌ 龙虎榜测试失败: {e}")
                stock_results['lhb_success'] = False
            
            # 2. 测试基金持仓数据
            logger.info(f"\n📈 测试 {stock_code} 基金持仓数据...")
            try:
                fund_result = analyzer.get_fund_holding_flow(stock_code)
                
                if not fund_result['raw_data'].empty:
                    logger.info(f"✅ 基金持仓数据: {len(fund_result['raw_data'])} 条记录")
                    
                    holding = fund_result['holding_analysis']
                    logger.info(f"   持仓基金数: {holding['total_funds']}")
                    logger.info(f"   总持仓市值: {holding['total_holding_value']:,.0f}")
                    logger.info(f"   平均持仓比例: {holding['avg_holding_ratio']:.3f}%")
                    
                    stock_results['fund_success'] = True
                else:
                    logger.warning(f"⚠️ {stock_code} 无基金持仓数据")
                    stock_results['fund_success'] = False
                    
            except Exception as e:
                logger.error(f"❌ 基金持仓测试失败: {e}")
                stock_results['fund_success'] = False
            
            # 3. 测试股东户数数据
            logger.info(f"\n👥 测试 {stock_code} 股东户数数据...")
            try:
                shareholder_result = analyzer.get_shareholder_flow(stock_code)
                
                if not shareholder_result['raw_data'].empty:
                    logger.info(f"✅ 股东户数数据: {len(shareholder_result['raw_data'])} 条记录")
                    
                    trend = shareholder_result['trend_analysis']
                    logger.info(f"   最新股东户数: {trend['latest_shareholder_count']:,}")
                    logger.info(f"   户数变化率: {trend['shareholder_change_rate']:.3f}")
                    logger.info(f"   趋势方向: {trend['trend_direction']}")
                    
                    sentiment = shareholder_result['retail_sentiment']
                    logger.info(f"   散户情绪评分: {sentiment['sentiment_score']:.3f}")
                    logger.info(f"   情绪趋势: {sentiment['sentiment_trend']}")
                    
                    stock_results['shareholder_success'] = True
                else:
                    logger.warning(f"⚠️ {stock_code} 无股东户数数据")
                    stock_results['shareholder_success'] = False
                    
            except Exception as e:
                logger.error(f"❌ 股东户数测试失败: {e}")
                stock_results['shareholder_success'] = False
            
            # 4. 测试大单资金流数据
            logger.info(f"\n💰 测试 {stock_code} 大单资金流数据...")
            try:
                large_order_result = analyzer.get_large_order_flow(stock_code)
                
                if not large_order_result['raw_data'].empty:
                    logger.info(f"✅ 大单资金流数据获取成功")
                    
                    large_order = large_order_result['large_order_analysis']
                    logger.info(f"   大单净流入: {large_order['large_order_net_inflow']:,.0f}")
                    logger.info(f"   大单净流入占比: {large_order['large_order_ratio']:.3f}%")
                    logger.info(f"   主力活跃度: {large_order['main_force_activity']}")
                    
                    main_force = large_order_result['main_force_analysis']
                    logger.info(f"   主力净流入: {main_force['main_net_inflow']:,.0f}")
                    logger.info(f"   资金流方向: {main_force['flow_direction']}")
                    
                    stock_results['large_order_success'] = True
                else:
                    logger.warning(f"⚠️ {stock_code} 无大单资金流数据")
                    stock_results['large_order_success'] = False
                    
            except Exception as e:
                logger.error(f"❌ 大单资金流测试失败: {e}")
                stock_results['large_order_success'] = False
            
            test_results[stock_code] = stock_results
            
            # 短暂延时避免API限制
            import time
            time.sleep(1)
        
        return test_results
        
    except Exception as e:
        logger.error(f"❌ 真实API资金流分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_comprehensive_analysis():
    """测试综合资金流分析"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试综合资金流分析")
    logger.info("=" * 80)
    
    try:
        from engines.fund_flow.real_api_analyzer import RealAPIFundFlowAnalyzer
        
        analyzer = RealAPIFundFlowAnalyzer()
        
        # 选择一个活跃股票进行综合分析
        test_stock = '000001'
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        end_date = datetime.now().strftime('%Y%m%d')
        
        logger.info(f"🔍 对 {test_stock} 进行综合资金流分析...")
        
        comprehensive_result = analyzer.comprehensive_fund_flow_analysis(
            test_stock, start_date, end_date
        )
        
        if 'error' not in comprehensive_result:
            logger.info("✅ 综合分析完成")
            
            # 显示综合评估结果
            logger.info(f"\n📊 综合分析结果:")
            logger.info(f"   股票代码: {comprehensive_result['symbol']}")
            logger.info(f"   分析时间: {comprehensive_result['analysis_period']}")
            logger.info(f"   综合评分: {comprehensive_result['comprehensive_score']:.3f}")
            logger.info(f"   风险评估: {comprehensive_result['risk_assessment']}")
            
            # 显示投资信号
            signals = comprehensive_result['investment_signals']
            if signals:
                logger.info(f"\n🚨 投资信号:")
                for signal in signals:
                    logger.info(f"   • {signal}")
            else:
                logger.info(f"\n📊 当前无明显投资信号")
            
            return True
        else:
            logger.error(f"❌ 综合分析失败: {comprehensive_result['error']}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 综合分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_availability():
    """测试API可用性"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试AKShare API可用性")
    logger.info("=" * 80)
    
    api_tests = {
        '龙虎榜API': False,
        '基金持仓API': False,
        '股东户数API': False,
        '大单资金API': False
    }
    
    try:
        import akshare as ak
        
        # 测试龙虎榜API
        try:
            logger.info("🔍 测试龙虎榜API...")
            lhb_data = ak.stock_lhb_detail_em(symbol='000001', start_date='20250101', end_date='20250115')
            api_tests['龙虎榜API'] = True
            logger.info(f"✅ 龙虎榜API可用，返回 {len(lhb_data)} 条数据")
        except Exception as e:
            logger.warning(f"⚠️ 龙虎榜API测试失败: {e}")
        
        # 测试基金持仓API
        try:
            logger.info("🔍 测试基金持仓API...")
            fund_data = ak.fund_portfolio_hold_em(symbol='000001')
            api_tests['基金持仓API'] = True
            logger.info(f"✅ 基金持仓API可用，返回 {len(fund_data)} 条数据")
        except Exception as e:
            logger.warning(f"⚠️ 基金持仓API测试失败: {e}")
        
        # 测试股东户数API
        try:
            logger.info("🔍 测试股东户数API...")
            shareholder_data = ak.stock_zh_a_gdhs(symbol='000001')
            api_tests['股东户数API'] = True
            logger.info(f"✅ 股东户数API可用，返回 {len(shareholder_data)} 条数据")
        except Exception as e:
            logger.warning(f"⚠️ 股东户数API测试失败: {e}")
        
        # 测试大单资金API
        try:
            logger.info("🔍 测试大单资金API...")
            flow_data = ak.stock_individual_detail_em(symbol='000001')
            api_tests['大单资金API'] = True
            logger.info(f"✅ 大单资金API可用，返回数据")
        except Exception as e:
            logger.warning(f"⚠️ 大单资金API测试失败: {e}")
        
    except Exception as e:
        logger.error(f"❌ API可用性测试失败: {e}")
    
    return api_tests

def main():
    """主测试函数"""
    logger.info(f"开始真实API资金流分析器测试 - {datetime.now()}")
    
    # 1. 测试API可用性
    api_availability = test_api_availability()
    
    # 2. 测试分析器功能
    analyzer_results = test_real_api_fund_flow_analyzer()
    
    # 3. 测试综合分析
    comprehensive_success = test_comprehensive_analysis()
    
    # 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("真实API资金流分析器测试结果")
    logger.info("=" * 80)
    
    # API可用性报告
    logger.info("📡 API可用性:")
    available_apis = sum(api_availability.values())
    total_apis = len(api_availability)
    
    for api_name, available in api_availability.items():
        status = "✅ 可用" if available else "❌ 不可用"
        logger.info(f"   {api_name}: {status}")
    
    logger.info(f"\nAPI可用性: {available_apis}/{total_apis}")
    
    # 分析器功能报告
    if analyzer_results:
        logger.info("\n📊 分析器功能测试:")
        for stock_code, results in analyzer_results.items():
            success_count = sum(results.values())
            total_count = len(results)
            logger.info(f"   {stock_code}: {success_count}/{total_count} 项功能正常")
    
    # 综合分析报告
    comprehensive_status = "✅ 成功" if comprehensive_success else "❌ 失败"
    logger.info(f"\n🔍 综合分析: {comprehensive_status}")
    
    # 总体评估
    overall_success = (
        available_apis >= 2 and  # 至少2个API可用
        len(analyzer_results) > 0 and  # 至少测试了一个股票
        comprehensive_success  # 综合分析成功
    )
    
    if overall_success:
        logger.info("\n🎉 真实API资金流分析器基本功能正常！")
        logger.info("📊 可以使用真实数据进行资金流分析")
        logger.info("🚀 资金流分析模块优化完成")
    else:
        logger.warning("\n⚠️ 真实API资金流分析器存在问题")
        logger.info("💡 建议检查网络连接和API访问权限")
    
    logger.info(f"\n测试完成时间: {datetime.now()}")
    logger.info("详细日志已保存到: real_api_fund_flow_test.log")
    
    return overall_success

if __name__ == "__main__":
    main()
