"""
FinBERT情绪分析器
专门用于金融文本的情绪分析，集成FinBERT模型
"""

import pandas as pd
import numpy as np
from datetime import datetime
import logging
from typing import Dict, List, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinBERTAnalyzer:
    """
    FinBERT情绪分析器
    
    功能：
    1. 使用FinBERT模型进行金融文本情绪分析
    2. 支持批量处理
    3. 提供置信度评估
    4. 支持中英文金融文本
    """
    
    def __init__(self, model_name: str = "ProsusAI/finbert", use_hf_mirror: bool = True):
        """
        初始化FinBERT分析器
        
        Args:
            model_name: 模型名称
            use_hf_mirror: 是否使用HF镜像
        """
        self.model_name = model_name
        self.use_hf_mirror = use_hf_mirror
        
        # 初始化模型
        self._init_finbert_model()
        
        # 金融关键词词典
        self._init_financial_keywords()
        
        logger.info("FinBERT分析器初始化完成")
    
    def _init_finbert_model(self):
        """初始化FinBERT模型"""
        try:
            from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
            
            # 设置镜像源
            if self.use_hf_mirror:
                import os
                os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
            
            logger.info(f"正在加载FinBERT模型: {self.model_name}")
            
            # 加载tokenizer和模型
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_name)
            
            # 创建pipeline
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=self.model,
                tokenizer=self.tokenizer,
                return_all_scores=True
            )
            
            logger.info("✅ FinBERT模型加载成功")
            self.model_available = True
            
        except Exception as e:
            logger.warning(f"⚠️ FinBERT模型加载失败: {e}")
            logger.info("💡 将使用备用的简化情绪分析方法")
            self.model_available = False
            self.sentiment_pipeline = None
    
    def _init_financial_keywords(self):
        """初始化金融关键词词典"""
        self.positive_keywords = {
            # 中文正面词汇
            '利好', '上涨', '增长', '提高', '扩大', '促进', '支持', '优化', '改善',
            '机遇', '机会', '利润', '盈利', '收益', '红利', '股息', '分红', '回购',
            '突破', '创新高', '牛市', '强势', '领涨', '爆发', '暴涨', '大涨',
            '降准', '降息', '刺激', '宽松', '流动性', '资金面',
            
            # 英文正面词汇
            'positive', 'growth', 'increase', 'rise', 'gain', 'profit', 'bullish',
            'optimistic', 'strong', 'robust', 'recovery', 'expansion', 'boost'
        }
        
        self.negative_keywords = {
            # 中文负面词汇
            '利空', '下跌', '下滑', '减少', '收缩', '限制', '禁止', '恶化', '下降',
            '风险', '危机', '亏损', '损失', '减持', '抛售', '套现', '减仓',
            '破位', '创新低', '熊市', '弱势', '领跌', '崩盘', '暴跌', '大跌',
            '加息', '紧缩', '收紧', '监管', '调控',
            
            # 英文负面词汇
            'negative', 'decline', 'decrease', 'fall', 'loss', 'bearish',
            'pessimistic', 'weak', 'recession', 'contraction', 'crisis'
        }
        
        self.neutral_keywords = {
            # 中文中性词汇
            '平稳', '稳定', '持平', '观望', '震荡', '整理', '调整',
            
            # 英文中性词汇
            'stable', 'steady', 'neutral', 'unchanged', 'flat', 'sideways'
        }
    
    def analyze_sentiment(self, text: str) -> Dict:
        """
        分析单个文本的情绪
        
        Args:
            text: 待分析文本
            
        Returns:
            Dict: 情绪分析结果
        """
        if not text or not text.strip():
            return self._get_neutral_result()
        
        # 预处理文本
        processed_text = self._preprocess_text(text)
        
        if self.model_available:
            return self._analyze_with_finbert(processed_text)
        else:
            return self._analyze_with_keywords(processed_text)
    
    def analyze_batch(self, texts: List[str]) -> List[Dict]:
        """
        批量分析文本情绪
        
        Args:
            texts: 文本列表
            
        Returns:
            List[Dict]: 情绪分析结果列表
        """
        results = []
        
        for text in texts:
            result = self.analyze_sentiment(text)
            results.append(result)
        
        return results
    
    def analyze_dataframe(self, df: pd.DataFrame, 
                         text_column: str = 'content',
                         title_column: str = 'title') -> pd.DataFrame:
        """
        分析DataFrame中的文本情绪
        
        Args:
            df: 数据框
            text_column: 内容列名
            title_column: 标题列名
            
        Returns:
            pd.DataFrame: 添加情绪分析结果的数据框
        """
        if df.empty:
            return df
        
        df_result = df.copy()
        
        # 合并标题和内容
        texts = []
        for _, row in df.iterrows():
            title = str(row.get(title_column, '')) if title_column in df.columns else ''
            content = str(row.get(text_column, '')) if text_column in df.columns else ''
            combined_text = f"{title} {content}".strip()
            texts.append(combined_text)
        
        # 批量分析
        sentiment_results = self.analyze_batch(texts)
        
        # 添加结果到数据框
        df_result['sentiment_label'] = [r['label'] for r in sentiment_results]
        df_result['sentiment_score'] = [r['score'] for r in sentiment_results]
        df_result['sentiment_confidence'] = [r['confidence'] for r in sentiment_results]
        df_result['sentiment_probabilities'] = [r['probabilities'] for r in sentiment_results]
        
        return df_result
    
    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        # 基本清理
        text = text.strip()
        
        # 限制长度（FinBERT通常有512 token限制）
        if len(text) > 500:
            text = text[:500]
        
        return text
    
    def _analyze_with_finbert(self, text: str) -> Dict:
        """使用FinBERT模型分析情绪"""
        try:
            # 使用pipeline进行预测
            results = self.sentiment_pipeline(text)
            
            # 解析结果
            if results and len(results) > 0:
                # 获取最高置信度的结果
                best_result = max(results[0], key=lambda x: x['score'])
                
                # 标准化标签
                label = self._normalize_label(best_result['label'])
                score = best_result['score']
                
                # 计算情绪分数（-1到1）
                sentiment_score = self._convert_to_sentiment_score(label, score)
                
                # 构建概率分布
                probabilities = {}
                for result in results[0]:
                    norm_label = self._normalize_label(result['label'])
                    probabilities[norm_label] = result['score']
                
                return {
                    'label': label,
                    'score': sentiment_score,
                    'confidence': score,
                    'probabilities': probabilities,
                    'method': 'finbert'
                }
            else:
                return self._get_neutral_result()
                
        except Exception as e:
            logger.warning(f"FinBERT分析失败: {e}，使用关键词方法")
            return self._analyze_with_keywords(text)
    
    def _analyze_with_keywords(self, text: str) -> Dict:
        """使用关键词方法分析情绪"""
        text_lower = text.lower()
        
        # 计算关键词匹配
        positive_count = sum(1 for keyword in self.positive_keywords 
                           if keyword.lower() in text_lower)
        negative_count = sum(1 for keyword in self.negative_keywords 
                           if keyword.lower() in text_lower)
        neutral_count = sum(1 for keyword in self.neutral_keywords 
                          if keyword.lower() in text_lower)
        
        total_count = positive_count + negative_count + neutral_count
        
        if total_count == 0:
            return self._get_neutral_result()
        
        # 计算概率
        pos_prob = positive_count / total_count
        neg_prob = negative_count / total_count
        neu_prob = neutral_count / total_count
        
        # 确定主要情绪
        if pos_prob > neg_prob and pos_prob > neu_prob:
            label = 'positive'
            confidence = pos_prob
            sentiment_score = pos_prob
        elif neg_prob > pos_prob and neg_prob > neu_prob:
            label = 'negative'
            confidence = neg_prob
            sentiment_score = -neg_prob
        else:
            label = 'neutral'
            confidence = neu_prob
            sentiment_score = 0.0
        
        return {
            'label': label,
            'score': sentiment_score,
            'confidence': confidence,
            'probabilities': {
                'positive': pos_prob,
                'negative': neg_prob,
                'neutral': neu_prob
            },
            'method': 'keywords'
        }
    
    def _normalize_label(self, label: str) -> str:
        """标准化标签"""
        label_lower = label.lower()
        
        if label_lower in ['positive', 'pos', 'bullish']:
            return 'positive'
        elif label_lower in ['negative', 'neg', 'bearish']:
            return 'negative'
        else:
            return 'neutral'
    
    def _convert_to_sentiment_score(self, label: str, confidence: float) -> float:
        """将标签和置信度转换为情绪分数"""
        if label == 'positive':
            return confidence
        elif label == 'negative':
            return -confidence
        else:
            return 0.0
    
    def _get_neutral_result(self) -> Dict:
        """获取中性结果"""
        return {
            'label': 'neutral',
            'score': 0.0,
            'confidence': 0.5,
            'probabilities': {
                'positive': 0.33,
                'negative': 0.33,
                'neutral': 0.34
            },
            'method': 'default'
        }
    
    def get_model_info(self) -> Dict:
        """获取模型信息"""
        return {
            'model_name': self.model_name,
            'model_available': self.model_available,
            'use_hf_mirror': self.use_hf_mirror,
            'supported_languages': ['en', 'zh'],
            'max_length': 512
        }
