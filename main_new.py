"""
政策-流动性-波动率套利系统 - 重构版主程序
基于核心决策引擎的完整分析系统
"""

import sys
import os
import logging
from datetime import datetime
import pandas as pd
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入重构后的核心模块
from engines.decision.core_engine import CoreDecisionEngine
from engines.news_policy import PolicyDataSource
from engines.sentiment.finbert_analyzer import FinBERTAnalyzer
from engines.sentiment.resonance_model import SentimentResonanceModel
from engines.fund_flow.real_api_analyzer import RealAPIFundFlowAnalyzer
from engines.volatility.enhanced_analyzer import EnhancedVolatilityAnalyzer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/system_main_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PolicyLiquidityVolatilitySystem:
    """政策-流动性-波动率套利系统 - 重构版"""

    def __init__(self):
        """初始化系统"""
        logger.info("🚀 初始化政策-流动性-波动率套利系统...")

        try:
            # 初始化核心决策引擎
            self.decision_engine = CoreDecisionEngine()

            # 初始化各个分析模块
            self.policy_source = PolicyDataSource()
            self.sentiment_analyzer = FinBERTAnalyzer()
            self.resonance_model = SentimentResonanceModel()
            self.fund_flow_analyzer = RealAPIFundFlowAnalyzer()
            self.volatility_analyzer = EnhancedVolatilityAnalyzer()

            logger.info("✅ 系统初始化完成")

        except Exception as e:
            logger.error(f"❌ 系统初始化失败: {e}")
            raise

    def run_comprehensive_analysis(self, symbols=None):
        """运行完整的综合分析"""
        try:
            logger.info("🚀 开始运行综合分析...")

            # 默认股票池
            if symbols is None:
                symbols = ['000001', '000002', '600519', '000858', '002415']

            # 1. 获取新闻政策数据
            logger.info("📰 获取新闻政策数据...")
            news_data = self.policy_source.get_all_news_with_refresh_control()
            policy_data = self.policy_source.get_gov_policy()

            # 2. 为每个股票生成信号
            all_signals = {
                'policy': {},
                'sentiment': {},
                'fund_flow': {},
                'volatility': {}
            }

            for symbol in symbols:
                logger.info(f"🔍 分析股票: {symbol}")

                # 政策信号
                policy_signals = self._generate_policy_signals(symbol, news_data, policy_data)
                all_signals['policy'][symbol] = policy_signals

                # 情绪信号
                sentiment_signals = self._generate_sentiment_signals(symbol, news_data)
                all_signals['sentiment'][symbol] = sentiment_signals

                # 资金流信号
                fund_flow_signals = self._generate_fund_flow_signals(symbol)
                all_signals['fund_flow'][symbol] = fund_flow_signals

                # 波动率信号
                volatility_signals = self._generate_volatility_signals(symbol)
                all_signals['volatility'][symbol] = volatility_signals

            # 3. 使用核心决策引擎进行批量分析
            logger.info("🧠 执行核心决策分析...")
            decision_results = self.decision_engine.batch_decision_analysis(symbols, all_signals)

            # 4. 生成系统报告
            self._generate_comprehensive_report(decision_results)

            return decision_results

        except Exception as e:
            logger.error(f"❌ 综合分析失败: {e}")
            return {}

    def _generate_policy_signals(self, symbol, news_data, policy_data):
        """生成政策信号"""
        try:
            # 简化的政策信号生成
            policy_impact_score = 0.5  # 默认中性

            if not news_data.empty:
                # 基于新闻数量和重要性评估政策影响
                recent_news_count = len(news_data)
                if recent_news_count > 10:
                    policy_impact_score = 0.6
                elif recent_news_count > 20:
                    policy_impact_score = 0.7

            return {
                'policy_impact_score': policy_impact_score,
                'direction': 'positive' if policy_impact_score > 0.5 else 'neutral',
                'strength': 'medium',
                'news_count': len(news_data) if not news_data.empty else 0
            }

        except Exception as e:
            logger.warning(f"政策信号生成失败: {e}")
            return {'policy_impact_score': 0.5, 'direction': 'neutral', 'strength': 'medium'}

    def _generate_sentiment_signals(self, symbol, news_data):
        """生成情绪信号"""
        try:
            if news_data.empty:
                return {'sentiment_index': 0.0, 'comprehensive_sentiment_score': 0.5}

            # 使用FinBERT分析新闻情绪
            sentiment_scores = []
            for _, news in news_data.head(10).iterrows():  # 分析最近10条新闻
                if 'title' in news and news['title']:
                    sentiment_result = self.sentiment_analyzer.analyze_sentiment(news['title'])
                    if sentiment_result and 'sentiment_score' in sentiment_result:
                        sentiment_scores.append(sentiment_result['sentiment_score'])

            if sentiment_scores:
                avg_sentiment = sum(sentiment_scores) / len(sentiment_scores)
                sentiment_index = (avg_sentiment - 0.5) * 2  # 转换到-1到1区间
            else:
                sentiment_index = 0.0

            return {
                'sentiment_index': sentiment_index,
                'comprehensive_sentiment_score': (sentiment_index + 1) / 2,
                'analyzed_news_count': len(sentiment_scores)
            }

        except Exception as e:
            logger.warning(f"情绪信号生成失败: {e}")
            return {'sentiment_index': 0.0, 'comprehensive_sentiment_score': 0.5}

    def _generate_fund_flow_signals(self, symbol):
        """生成资金流信号"""
        try:
            # 使用真实API分析器
            start_date = (datetime.now() - pd.Timedelta(days=30)).strftime('%Y%m%d')
            end_date = datetime.now().strftime('%Y%m%d')

            # 获取龙虎榜数据
            lhb_result = self.fund_flow_analyzer.get_lhb_fund_flow(symbol, start_date, end_date)

            # 获取股东户数数据
            shareholder_result = self.fund_flow_analyzer.get_shareholder_flow(symbol)

            # 计算综合评分
            comprehensive_score = 0.5  # 默认中性

            if lhb_result and 'hot_money_analysis' in lhb_result:
                hot_money_flow = lhb_result['hot_money_analysis'].get('net_hot_money_flow', 0)
                if hot_money_flow > 0:
                    comprehensive_score += 0.1
                elif hot_money_flow < 0:
                    comprehensive_score -= 0.1

            if shareholder_result and 'trend_analysis' in shareholder_result:
                change_rate = shareholder_result['trend_analysis'].get('shareholder_change_rate', 0)
                if change_rate < 0:  # 股东户数减少，通常是好事
                    comprehensive_score += 0.1
                elif change_rate > 0.1:  # 股东户数大幅增加
                    comprehensive_score -= 0.1

            return {
                'comprehensive_score': max(0.0, min(1.0, comprehensive_score)),
                'main_force_activity': 'medium',
                'hot_money_interest': 'medium'
            }

        except Exception as e:
            logger.warning(f"资金流信号生成失败: {e}")
            return {'comprehensive_score': 0.5, 'main_force_activity': 'medium'}

    def _generate_volatility_signals(self, symbol):
        """生成波动率信号"""
        try:
            # 获取股票价格数据
            import akshare as ak

            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - pd.Timedelta(days=100)).strftime('%Y%m%d')

            price_data = ak.stock_zh_a_hist(symbol=symbol, period='daily',
                                          start_date=start_date, end_date=end_date)

            if price_data.empty:
                return {'current_volatility': 0.25, 'volatility_signal_strength': 0.5}

            # 重命名列
            price_data = price_data.rename(columns={'日期': 'date', '收盘': 'close'})
            price_data['date'] = pd.to_datetime(price_data['date'])
            price_data['close'] = pd.to_numeric(price_data['close'], errors='coerce')

            # 使用增强波动率分析器
            garch_result = self.volatility_analyzer.calculate_enhanced_garch_volatility(price_data)

            current_volatility = garch_result.get('current_volatility', 0.25)

            # 基于波动率水平生成信号强度
            if current_volatility < 0.15:
                signal_strength = 0.7  # 低波动率，正面信号
            elif current_volatility > 0.35:
                signal_strength = 0.3  # 高波动率，负面信号
            else:
                signal_strength = 0.5  # 正常波动率

            return {
                'current_volatility': current_volatility,
                'volatility_signal_strength': signal_strength,
                'current_regime': 'normal'
            }

        except Exception as e:
            logger.warning(f"波动率信号生成失败: {e}")
            return {'current_volatility': 0.25, 'volatility_signal_strength': 0.5}

    def _generate_comprehensive_report(self, decision_results):
        """生成综合分析报告"""
        try:
            logger.info("📊 生成综合分析报告...")

            # 创建报告目录
            os.makedirs('reports', exist_ok=True)

            # 生成报告文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f'reports/comprehensive_analysis_{timestamp}.json'

            # 保存详细结果到JSON
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(decision_results, f, ensure_ascii=False, indent=2, default=str)

            # 生成控制台报告
            print("\n" + "="*80)
            print("🎯 政策-流动性-波动率套利系统 - 综合分析报告")
            print("="*80)
            print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📊 分析股票数: {decision_results.get('total_symbols', 0)}")

            # 决策统计
            stats = decision_results.get('summary_statistics', {})
            print(f"\n📈 决策分布:")
            for action, count in stats.items():
                print(f"   {action}: {count} 只")

            # 投资组合建议
            portfolio = decision_results.get('portfolio_recommendation', {})
            recommended_stocks = portfolio.get('recommended_stocks', [])

            if recommended_stocks:
                print(f"\n💼 投资组合建议 (总仓位: {portfolio.get('total_position', 0):.2%}):")
                for i, stock in enumerate(recommended_stocks[:5], 1):  # 显示前5只
                    print(f"   {i}. {stock['symbol']}: {stock['action']} "
                          f"(仓位: {stock['position']:.2%}, 评分: {stock['score']:.3f})")
            else:
                print(f"\n💼 当前市场环境下暂无推荐股票")

            print("="*80)
            print(f"📄 详细报告已保存到: {report_file}")

            logger.info(f"报告生成完成: {report_file}")

        except Exception as e:
            logger.error(f"报告生成失败: {e}")

def main():
    """主函数"""
    print("🚀 启动政策-流动性-波动率套利系统...")

    try:
        # 创建系统实例
        system = PolicyLiquidityVolatilitySystem()

        # 运行综合分析
        results = system.run_comprehensive_analysis()

        if results and results.get('total_symbols', 0) > 0:
            print("✅ 系统分析完成！")
        else:
            print("⚠️ 分析完成，但结果为空，请检查日志。")

    except Exception as e:
        print(f"❌ 系统运行失败: {e}")
        logger.error(f"系统运行失败: {e}")

if __name__ == "__main__":
    main()
