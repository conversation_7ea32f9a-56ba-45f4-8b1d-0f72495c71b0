"""
Main entry point for policy_liquidity_volatility_arbitrage.
"""

import os
import sys
import argparse
import pandas as pd
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import utilities
from utils.logger import logger, setup_logger
from utils.config_loader import ConfigLoader
from utils.data_utils import get_latest_trading_date
from utils.stock_utils import get_stock_list

# Import engines
from engines.news_policy.fetcher import NewsPolicyFetcher
from engines.news_policy.analyzer import PolicyAnalyzer
from engines.sentiment.analyzer import SentimentAnalyzer
from engines.tiered_fund_flow.analyzer import TieredFundFlowAnalyzer
from engines.volatility.analyzer import VolatilityAnalyzer
from decision_engine.core import DecisionEngine

# Import execution
from execution.qmt_adapter import QMTAdapter

class StockScreener:
    """
    Main stock screener class.
    Orchestrates the analysis and decision-making process.
    """

    def __init__(self, config_path="config/config.yaml"):
        """
        Initialize the StockScreener.

        Args:
            config_path (str): Path to the configuration file.
        """
        # Load configuration
        self.config_loader = ConfigLoader(config_path)
        self.config = self.config_loader.config

        # Set up logger
        log_dir = self.config.get('paths.logs_dir', 'logs')
        log_level = self.config.get('logging.level', 'INFO')
        self.logger = setup_logger(log_dir, log_level)

        # Initialize engines (to be implemented)
        self._init_engines()

        # Initialize execution adapter (to be implemented)
        self._init_execution()

        self.logger.info("StockScreener initialized")

    def _init_engines(self):
        """
        Initialize analysis engines.
        """
        self.logger.info("Initializing engines")

        # Initialize engines (pass config_loader instead of config dict)
        self.news_policy_fetcher = NewsPolicyFetcher(self.config_loader)
        self.policy_analyzer = PolicyAnalyzer(self.config_loader)
        self.sentiment_analyzer = SentimentAnalyzer(self.config_loader)
        self.tiered_fund_flow_analyzer = TieredFundFlowAnalyzer(self.config_loader)
        self.volatility_analyzer = VolatilityAnalyzer(self.config_loader)

        # Initialize decision engine
        self.decision_engine = DecisionEngine(
            config=self.config_loader,
            news_policy_fetcher=self.news_policy_fetcher,
            policy_analyzer=self.policy_analyzer,
            sentiment_analyzer=self.sentiment_analyzer,
            tiered_fund_flow_analyzer=self.tiered_fund_flow_analyzer,
            volatility_analyzer=self.volatility_analyzer
        )

    def _init_execution(self):
        """
        Initialize execution adapter.
        """
        self.logger.info("Initializing execution adapter")

        # Initialize QMT adapter
        self.execution_adapter = QMTAdapter(self.config)

    def get_stock_pool(self, filter_criteria=None):
        """
        Get the stock pool to analyze.

        Args:
            filter_criteria (dict, optional): Criteria to filter stocks.

        Returns:
            pandas.DataFrame: DataFrame containing the stock pool.
        """
        try:
            # Get all A-share stocks
            stock_list = get_stock_list()

            # Apply filters if provided
            if filter_criteria:
                # Example filters (to be implemented)
                if 'min_market_cap' in filter_criteria:
                    # Filter by market cap
                    pass

                if 'exclude_st' in filter_criteria and filter_criteria['exclude_st']:
                    # Exclude ST stocks
                    stock_list = stock_list[~stock_list['stock_name'].str.contains('ST')]

            # For testing, always use a small subset to speed up processing
            self.logger.info("Using test subset for faster processing.")
            # Create a test subset with some common stocks
            test_stocks = [
                {'stock_code': '600000', 'stock_name': '浦发银行'},
                {'stock_code': '600036', 'stock_name': '招商银行'},
                {'stock_code': '601318', 'stock_name': '中国平安'},
                {'stock_code': '600519', 'stock_name': '贵州茅台'},
                {'stock_code': '000001', 'stock_name': '平安银行'}
            ]
            stock_list = pd.DataFrame(test_stocks)

            self.logger.info(f"Stock pool contains {len(stock_list)} stocks")
            return stock_list
        except Exception as e:
            self.logger.error(f"Error getting stock pool: {str(e)}")
            # Return an empty DataFrame with the required columns
            return pd.DataFrame(columns=['stock_code', 'stock_name'])

    def analyze_market(self):
        """
        Analyze the overall market condition.

        Returns:
            dict: Market analysis results.
        """
        self.logger.info("Analyzing market conditions")

        # Use volatility analyzer to get market volatility regime
        market_info = self.volatility_analyzer.analyze_market_volatility_regime()

        # Add market date
        market_info['date'] = get_latest_trading_date()

        # Get market sentiment if available
        if hasattr(self, 'news_policy_fetcher') and hasattr(self, 'sentiment_analyzer'):
            try:
                # Get market news
                market_news_df = self.news_policy_fetcher.get_market_information(
                    days_lookback=7,
                    include_policy_docs=False,
                    include_policy_rss=False
                )

                # Analyze sentiment
                market_sentiment = self.sentiment_analyzer.get_market_sentiment(market_news_df)

                # Update market info
                market_info['market_sentiment'] = market_sentiment.get('sentiment_trend', 'neutral')
                market_info['market_sentiment_score'] = market_sentiment.get('market_sentiment_score', 0)
            except Exception as e:
                self.logger.error(f"Error analyzing market sentiment: {str(e)}")
                market_info['market_sentiment'] = 'neutral'
                market_info['market_sentiment_score'] = 0
        else:
            market_info['market_sentiment'] = 'neutral'
            market_info['market_sentiment_score'] = 0

        self.logger.info(f"Market analysis: vol_regime={market_info.get('market_vol_regime', 'unknown')}, "
                        f"sentiment={market_info.get('market_sentiment', 'unknown')}")

        return market_info

    def generate_recommendations(self, top_n=None):
        """
        Generate stock recommendations.

        Args:
            top_n (int, optional): Number of top stocks to recommend.

        Returns:
            pandas.DataFrame: DataFrame containing recommended stocks.
        """
        if top_n is None:
            top_n = self.config.get('decision_engine.top_n', 30)

        self.logger.info(f"Generating recommendations for top {top_n} stocks")

        # Get stock pool
        stock_pool = self.get_stock_pool({'exclude_st': True})

        # Use decision engine to get recommendations
        recommendations = self.decision_engine.get_recommendations(stock_pool=stock_pool, top_n=top_n)

        # Log results
        if not recommendations.empty:
            self.logger.info(f"Generated {len(recommendations)} recommendations")
            # Log top 5 recommendations
            top_5 = recommendations.head(5)
            for _, row in top_5.iterrows():
                self.logger.info(f"Top pick: {row['stock_code']} ({row['stock_name']}), "
                                f"score: {row['final_score']:.4f}")
        else:
            self.logger.warning("No recommendations generated")

        return recommendations

    def execute_trades(self, recommendations):
        """
        Execute trades based on recommendations.

        Args:
            recommendations (pandas.DataFrame): DataFrame containing recommended stocks.

        Returns:
            bool: True if successful, False otherwise.
        """
        self.logger.info(f"Executing trades for {len(recommendations)} stocks")

        # Check if execution adapter is available
        if not hasattr(self, 'execution_adapter'):
            self.logger.error("Execution adapter not initialized")
            return False

        # Execute trades
        success = self.execution_adapter.execute_recommendations(recommendations)

        if success:
            self.logger.info("Trade execution completed successfully")
        else:
            self.logger.error("Trade execution failed")

        return success

    def run(self, mode='analyze', execute=False):
        """
        Run the stock screener.

        Args:
            mode (str): Operation mode ('analyze', 'backtest').
            execute (bool): Whether to execute trades.

        Returns:
            pandas.DataFrame: DataFrame containing results.
        """
        self.logger.info(f"Running StockScreener in {mode} mode")

        if mode == 'analyze':
            # Generate recommendations
            recommendations = self.generate_recommendations()

            # Execute trades if requested
            if execute:
                self.execute_trades(recommendations)

            return recommendations

        elif mode == 'backtest':
            # To be implemented
            self.logger.info("Backtesting placeholder")
            return pd.DataFrame()

        else:
            self.logger.error(f"Invalid mode: {mode}")
            return pd.DataFrame()

def parse_args():
    """
    Parse command line arguments.

    Returns:
        argparse.Namespace: Parsed arguments.
    """
    parser = argparse.ArgumentParser(description='Policy Liquidity Volatility Arbitrage')
    parser.add_argument('--config', type=str, default='config/config.yaml', help='Path to configuration file')
    parser.add_argument('--mode', type=str, choices=['analyze', 'backtest'], default='analyze', help='Operation mode')
    parser.add_argument('--execute', action='store_true', help='Execute trades')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO', help='Log level')

    return parser.parse_args()

def main():
    """
    Main function.
    """
    # Parse command line arguments
    args = parse_args()

    # Initialize stock screener
    screener = StockScreener(args.config)

    # Run stock screener
    results = screener.run(mode=args.mode, execute=args.execute)

    # Print results
    if not results.empty:
        print("\nRecommended stocks:")
        print(results[['stock_code', 'stock_name', 'final_score', 'news_policy_score', 'tiered_flow_score']].to_string(index=False))

if __name__ == '__main__':
    main()
