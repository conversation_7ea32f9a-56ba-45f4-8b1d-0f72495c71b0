"""
简化的政策抓取测试 - 不使用装饰器
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
from datetime import datetime

def test_people_policy():
    """测试人民网政策库"""
    print("=" * 60)
    print("测试人民网政策库")
    print("=" * 60)
    
    try:
        url = "https://data.people.com.cn/pd/gjzcxx"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        print(f"正在访问: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        response.encoding = 'utf-8'
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找政策链接
            policy_links = soup.find_all('a', href=True)
            policy_count = 0
            
            print("\n找到的政策链接:")
            for link in policy_links[:10]:  # 只显示前10个
                href = link.get('href', '')
                text = link.text.strip()
                
                if 'detail.html' in href and len(text) > 10:
                    policy_count += 1
                    print(f"{policy_count}. {text[:60]}...")
                    print(f"   链接: {href}")
            
            print(f"\n总计找到 {policy_count} 个政策链接")
            return policy_count > 0
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_ndrc_policy():
    """测试发改委政策"""
    print("\n" + "=" * 60)
    print("测试发改委政策")
    print("=" * 60)
    
    try:
        # 测试发改委通知页面
        url = "https://www.ndrc.gov.cn/xxgk/zcfb/tz/"
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        print(f"正在访问: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        response.encoding = 'utf-8'
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找政策链接
            policy_links = soup.find_all('a', href=True)
            policy_count = 0
            
            print("\n找到的政策链接:")
            for link in policy_links[:10]:  # 只显示前10个
                href = link.get('href', '')
                text = link.text.strip()
                
                # 过滤有效的政策链接
                if ('通知' in text or '办法' in text or '规定' in text) and len(text) > 10:
                    policy_count += 1
                    print(f"{policy_count}. {text[:60]}...")
                    print(f"   链接: {href}")
            
            print(f"\n总计找到 {policy_count} 个政策链接")
            return policy_count > 0
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_akshare_news():
    """测试AKShare新闻接口"""
    print("\n" + "=" * 60)
    print("测试AKShare新闻接口")
    print("=" * 60)
    
    try:
        import akshare as ak
        print("✅ AKShare 导入成功")
        
        # 测试财经早餐
        print("\n测试财经早餐接口...")
        try:
            df = ak.stock_info_cjzc_em()
            print(f"✅ 财经早餐: {len(df)} 条")
            if not df.empty:
                print(f"   列名: {list(df.columns)}")
                print(f"   示例: {df.iloc[0].to_dict()}")
        except Exception as e:
            print(f"❌ 财经早餐失败: {e}")
        
        # 测试全球财经快讯
        print("\n测试全球财经快讯...")
        try:
            df = ak.stock_info_global_em()
            print(f"✅ 全球财经快讯: {len(df)} 条")
            if not df.empty:
                print(f"   列名: {list(df.columns)}")
        except Exception as e:
            print(f"❌ 全球财经快讯失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ AKShare 未安装")
        return False
    except Exception as e:
        print(f"❌ AKShare 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始简化政策抓取测试")
    print(f"测试时间: {datetime.now()}")
    
    results = {}
    
    # 测试人民网政策库
    results['人民网政策'] = test_people_policy()
    time.sleep(2)  # 避免请求过快
    
    # 测试发改委政策
    results['发改委政策'] = test_ndrc_policy()
    time.sleep(2)
    
    # 测试AKShare新闻
    results['AKShare新闻'] = test_akshare_news()
    
    # 生成报告
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    
    success_count = sum(results.values())
    total_count = len(results)
    
    for test_name, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count > 0:
        print("🎉 至少有一项测试成功，可以继续开发！")
    else:
        print("⚠️ 所有测试都失败，需要检查网络连接和依赖")
    
    print(f"\n测试完成时间: {datetime.now()}")

if __name__ == "__main__":
    main()
