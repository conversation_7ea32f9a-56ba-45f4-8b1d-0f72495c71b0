"""
PolicyDataSource - 政策和新闻数据源类
完全按照用户要求实现所有API接口，支持定时刷新和去重
"""

import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup
import json
import os
import hashlib

from utils.logger import logger
from utils.error_utils import retry
from utils.cache_utils import cached


class PolicyDataSource:
    """政策和新闻数据源类 - 完全重写版本"""

    def __init__(self):
        """初始化政策数据源"""
        self.default_lookback_days = 7
        self.data_cache_dir = "data/news_cache"
        self.last_fetch_file = "data/last_fetch_times.json"

        # 创建缓存目录
        os.makedirs(self.data_cache_dir, exist_ok=True)
        os.makedirs("data", exist_ok=True)

        # 加载上次获取时间
        self.last_fetch_times = self._load_last_fetch_times()

        logger.info("PolicyDataSource initialized with caching and deduplication")

    def _load_last_fetch_times(self):
        """加载上次获取时间"""
        try:
            if os.path.exists(self.last_fetch_file):
                with open(self.last_fetch_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load last fetch times: {e}")
        return {}

    def _save_last_fetch_times(self):
        """保存上次获取时间"""
        try:
            with open(self.last_fetch_file, 'w', encoding='utf-8') as f:
                json.dump(self.last_fetch_times, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save last fetch times: {e}")

    def _should_refresh(self, api_name, refresh_minutes):
        """检查是否需要刷新数据"""
        if api_name not in self.last_fetch_times:
            return True

        last_time = datetime.fromisoformat(self.last_fetch_times[api_name])
        now = datetime.now()
        return (now - last_time).total_seconds() > refresh_minutes * 60

    def _update_fetch_time(self, api_name):
        """更新获取时间"""
        self.last_fetch_times[api_name] = datetime.now().isoformat()
        self._save_last_fetch_times()

    def _generate_content_hash(self, content):
        """生成内容哈希用于去重"""
        if isinstance(content, dict):
            content_str = json.dumps(content, sort_keys=True, ensure_ascii=False)
        else:
            content_str = str(content)
        return hashlib.md5(content_str.encode('utf-8')).hexdigest()

    def _load_cached_data(self, api_name):
        """加载缓存数据"""
        cache_file = os.path.join(self.data_cache_dir, f"{api_name}.json")
        try:
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return pd.DataFrame(data)
        except Exception as e:
            logger.warning(f"Failed to load cached data for {api_name}: {e}")
        return pd.DataFrame()

    def _save_cached_data(self, api_name, df):
        """保存缓存数据"""
        cache_file = os.path.join(self.data_cache_dir, f"{api_name}.json")
        try:
            df.to_json(cache_file, orient='records', force_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save cached data for {api_name}: {e}")

    def _deduplicate_with_cache(self, api_name, new_df, key_columns=['title']):
        """与缓存数据去重"""
        if new_df.empty:
            return new_df

        # 加载缓存数据
        cached_df = self._load_cached_data(api_name)

        if cached_df.empty:
            # 没有缓存数据，直接返回新数据
            self._save_cached_data(api_name, new_df)
            return new_df

        # 基于关键列去重
        combined_df = pd.concat([cached_df, new_df], ignore_index=True)

        # 去重逻辑
        if all(col in combined_df.columns for col in key_columns):
            deduplicated_df = combined_df.drop_duplicates(subset=key_columns, keep='last')
        else:
            # 如果关键列不存在，使用所有列去重
            deduplicated_df = combined_df.drop_duplicates(keep='last')

        # 只返回新增的数据
        new_count = len(deduplicated_df) - len(cached_df)
        if new_count > 0:
            result_df = deduplicated_df.tail(new_count).copy()
            # 保存更新后的缓存
            self._save_cached_data(api_name, deduplicated_df)
            logger.info(f"{api_name}: 新增 {new_count} 条数据，去重后总计 {len(deduplicated_df)} 条")
            return result_df
        else:
            logger.info(f"{api_name}: 没有新数据")
            return pd.DataFrame()

    # ==================== 新闻数据获取接口（按用户要求重写） ====================

    @retry(max_retries=3, delay=2)
    def get_financial_breakfast(self):
        """
        获取财经早餐-东方财富
        接口：stock_info_cjzc_em
        刷新频率：每次调用都获取最新数据
        """
        api_name = "financial_breakfast"
        try:
            df = ak.stock_info_cjzc_em()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '摘要': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '东方财富财经早餐'
                df['api_name'] = api_name
                logger.info(f"获取到 {len(df)} 条财经早餐")
            return df
        except Exception as e:
            logger.error(f"获取财经早餐失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_global_news_em(self):
        """
        获取全球财经快讯-东方财富
        接口：stock_info_global_em
        刷新频率：每4小时刷新，对照上一次获取去重
        """
        api_name = "global_news_em"

        # 检查是否需要刷新（4小时 = 240分钟）
        if not self._should_refresh(api_name, 240):
            logger.info(f"{api_name}: 距离上次获取不足4小时，跳过")
            return pd.DataFrame()

        try:
            df = ak.stock_info_global_em()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '摘要': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '东方财富全球快讯'
                df['api_name'] = api_name

                # 与缓存数据去重
                result_df = self._deduplicate_with_cache(api_name, df, ['title', 'publish_date'])

                # 更新获取时间
                self._update_fetch_time(api_name)

                logger.info(f"获取到 {len(df)} 条全球财经快讯，去重后新增 {len(result_df)} 条")
                return result_df
            else:
                self._update_fetch_time(api_name)
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取全球财经快讯失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_global_news_sina(self):
        """
        获取全球财经快讯-新浪财经
        接口：stock_info_global_sina
        刷新频率：每15分钟刷新，对照上一次获取去重
        """
        api_name = "global_news_sina"

        # 检查是否需要刷新（15分钟）
        if not self._should_refresh(api_name, 15):
            logger.info(f"{api_name}: 距离上次获取不足15分钟，跳过")
            return pd.DataFrame()

        try:
            df = ak.stock_info_global_sina()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '时间': 'publish_date',
                    '内容': 'title'
                })
                df['content'] = df['title']  # 新浪接口内容和标题相同
                df['url'] = ''  # 新浪接口没有链接
                df['source'] = '新浪财经'
                df['api_name'] = api_name

                # 与缓存数据去重
                result_df = self._deduplicate_with_cache(api_name, df, ['title', 'publish_date'])

                # 更新获取时间
                self._update_fetch_time(api_name)

                logger.info(f"获取到 {len(df)} 条新浪财经快讯，去重后新增 {len(result_df)} 条")
                return result_df
            else:
                self._update_fetch_time(api_name)
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取新浪财经快讯失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_global_news_futu(self):
        """
        获取快讯-富途牛牛
        接口：stock_info_global_futu
        刷新频率：每15分钟刷新，对照上一次获取去重
        """
        api_name = "global_news_futu"

        # 检查是否需要刷新（15分钟）
        if not self._should_refresh(api_name, 15):
            logger.info(f"{api_name}: 距离上次获取不足15分钟，跳过")
            return pd.DataFrame()

        try:
            df = ak.stock_info_global_futu()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '富途牛牛'
                df['api_name'] = api_name

                # 与缓存数据去重
                result_df = self._deduplicate_with_cache(api_name, df, ['title', 'publish_date'])

                # 更新获取时间
                self._update_fetch_time(api_name)

                logger.info(f"获取到 {len(df)} 条富途牛牛快讯，去重后新增 {len(result_df)} 条")
                return result_df
            else:
                self._update_fetch_time(api_name)
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取富途牛牛快讯失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_global_news_ths(self):
        """
        获取全球财经直播-同花顺财经
        接口：stock_info_global_ths
        刷新频率：每15分钟刷新，对照上一次获取去重
        """
        api_name = "global_news_ths"

        # 检查是否需要刷新（15分钟）
        if not self._should_refresh(api_name, 15):
            logger.info(f"{api_name}: 距离上次获取不足15分钟，跳过")
            return pd.DataFrame()

        try:
            df = ak.stock_info_global_ths()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '同花顺财经'
                df['api_name'] = api_name

                # 与缓存数据去重
                result_df = self._deduplicate_with_cache(api_name, df, ['title', 'publish_date'])

                # 更新获取时间
                self._update_fetch_time(api_name)

                logger.info(f"获取到 {len(df)} 条同花顺财经，去重后新增 {len(result_df)} 条")
                return result_df
            else:
                self._update_fetch_time(api_name)
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取同花顺财经失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_global_news_cls(self, symbol="全部"):
        """
        获取电报-财联社
        接口：stock_info_global_cls
        刷新频率：每15分钟刷新，对照上一次获取去重
        """
        api_name = f"global_news_cls_{symbol}"

        # 检查是否需要刷新（15分钟）
        if not self._should_refresh(api_name, 15):
            logger.info(f"{api_name}: 距离上次获取不足15分钟，跳过")
            return pd.DataFrame()

        try:
            df = ak.stock_info_global_cls(symbol=symbol)
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '发布日期': 'publish_date',
                    '发布时间': 'time'
                })
                # 合并日期和时间
                if 'time' in df.columns:
                    df['publish_date'] = df['publish_date'].astype(str) + ' ' + df['time'].astype(str)
                df['url'] = ''  # 财联社接口没有链接
                df['source'] = '财联社'
                df['api_name'] = api_name

                # 与缓存数据去重
                result_df = self._deduplicate_with_cache(api_name, df, ['title', 'publish_date'])

                # 更新获取时间
                self._update_fetch_time(api_name)

                logger.info(f"获取到 {len(df)} 条财联社电报，去重后新增 {len(result_df)} 条")
                return result_df
            else:
                self._update_fetch_time(api_name)
                return pd.DataFrame()
        except Exception as e:
            logger.error(f"获取财联社电报失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_broker_news_sina(self, page="1"):
        """
        获取证券原创-新浪财经
        接口：stock_info_broker_sina
        保持原有接口，但不设置定时刷新
        """
        try:
            df = ak.stock_info_broker_sina(page=page)
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '时间': 'publish_date',
                    '内容': 'title',
                    '链接': 'url'
                })
                df['content'] = df['title']  # 内容和标题相同
                df['source'] = '新浪证券'
                logger.info(f"获取到 {len(df)} 条新浪证券原创")
            return df
        except Exception as e:
            logger.error(f"获取新浪证券原创失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_stock_news_em(self):
        """
        获取个股新闻-东方财富
        保持原有接口
        """
        try:
            df = ak.stock_news_em()
            if not df.empty:
                logger.info(f"获取到 {len(df)} 条个股新闻")
            return df
        except Exception as e:
            logger.error(f"获取个股新闻失败: {str(e)}")
            return pd.DataFrame()

    @retry(max_retries=3, delay=2)
    def get_news_main_cx(self):
        """
        获取财经内容精选-财新网
        保持原有接口
        """
        try:
            df = ak.stock_news_main_cx()
            if not df.empty:
                logger.info(f"获取到 {len(df)} 条财经内容精选")
            return df
        except Exception as e:
            logger.error(f"获取财经内容精选失败: {str(e)}")
            return pd.DataFrame()

    # ==================== 统一新闻获取接口 ====================

    def get_all_news_with_refresh_control(self):
        """
        获取所有新闻，按照刷新频率控制
        """
        all_news = []

        # 财经早餐（每次都获取）
        breakfast = self.get_financial_breakfast()
        if not breakfast.empty:
            all_news.append(breakfast)

        # 4小时刷新的接口
        global_em = self.get_global_news_em()
        if not global_em.empty:
            all_news.append(global_em)

        # 15分钟刷新的接口
        sina = self.get_global_news_sina()
        if not sina.empty:
            all_news.append(sina)

        futu = self.get_global_news_futu()
        if not futu.empty:
            all_news.append(futu)

        ths = self.get_global_news_ths()
        if not ths.empty:
            all_news.append(ths)

        cls = self.get_global_news_cls()
        if not cls.empty:
            all_news.append(cls)

        if all_news:
            combined_df = pd.concat(all_news, ignore_index=True)
            logger.info(f"总计获取到 {len(combined_df)} 条新闻")
            return combined_df
        else:
            logger.info("没有获取到新闻数据")
            return pd.DataFrame()

    # ==================== 政策文件获取 ====================

    @cached(expiry=3600)
    @retry(max_retries=3, delay=2)
    def get_gov_policy(self, page=1, limit=1000):
        """
        获取人民网政策库数据（更可靠的数据源）

        Args:
            page (int): 页码
            limit (int): 每页数量（默认1000条）

        Returns:
            pandas.DataFrame: 政策数据
        """
        try:
            all_policies = []
            base_url = "https://data.people.com.cn/pd/gjzcxx"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # 分页获取政策数据
            current_page = page
            collected_count = 0

            while collected_count < limit and current_page <= 50:  # 限制最大页数
                try:
                    # 构建分页URL
                    if current_page == 1:
                        url = base_url
                    else:
                        url = f"{base_url}/list.html?page={current_page}"

                    logger.info(f"正在获取国家政策第 {current_page} 页...")
                    response = requests.get(url, headers=headers, timeout=30)
                    response.encoding = 'utf-8'
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 解析政策列表 - 基于实际HTML结构
                    policy_items = soup.select('li') or soup.select('.list-item') or soup.select('a[href*="/detail.html"]')

                    if not policy_items:
                        logger.warning(f"第 {current_page} 页没有找到政策数据，停止获取")
                        break

                    page_policies = []
                    for item in policy_items:
                        if collected_count >= limit:
                            break

                        try:
                            # 查找链接和标题
                            link_elem = item.find('a') if item.name != 'a' else item
                            if link_elem and 'detail.html' in link_elem.get('href', ''):
                                title = link_elem.text.strip()
                                link = link_elem.get('href', '')

                                # 补全链接
                                if link and not link.startswith('http'):
                                    if link.startswith('/'):
                                        link = "https://data.people.com.cn" + link
                                    else:
                                        link = "https://data.people.com.cn/pd/gjzcxx/" + link

                                # 查找日期
                                date_str = ""
                                date_elem = item.find(class_='date') or item.find('span')
                                if date_elem:
                                    date_str = date_elem.text.strip()

                                # 查找来源
                                source_elem = item.find('em') or item.find(class_='source')
                                source = source_elem.text.strip() if source_elem else '国家政策'

                                if title and len(title) > 5:  # 过滤太短的标题
                                    page_policies.append({
                                        'title': title,
                                        'link': link,
                                        'date': date_str,
                                        'content': '',  # 暂不获取详细内容以提高速度
                                        'source': source,
                                        'page': current_page
                                    })
                                    collected_count += 1

                        except Exception as e:
                            logger.warning(f"解析政策项目失败: {e}")
                            continue

                    all_policies.extend(page_policies)
                    logger.info(f"第 {current_page} 页获取到 {len(page_policies)} 条政策，累计 {collected_count} 条")

                    if len(page_policies) == 0:
                        logger.warning(f"第 {current_page} 页没有有效数据，停止获取")
                        break

                    # 添加延时避免被封
                    import time
                    time.sleep(3)
                    current_page += 1

                except Exception as e:
                    logger.error(f"获取第 {current_page} 页失败: {e}")
                    current_page += 1
                    continue

            df = pd.DataFrame(all_policies)
            logger.info(f"国家政策获取完成，总计 {len(df)} 条")
            return df

        except Exception as e:
            logger.error(f"获取国家政策失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=3600)
    @retry(max_retries=3, delay=2)
    def get_ndrc_policy(self, page=1, limit=1000):
        """
        获取发改委政策（基于实际网站结构）

        Args:
            page (int): 页码
            limit (int): 每页数量（默认1000条）

        Returns:
            pandas.DataFrame: 政策数据
        """
        try:
            all_policies = []

            # 发改委政策的多个分类URL
            policy_urls = [
                "https://www.ndrc.gov.cn/xxgk/zcfb/fzggwl/",  # 发展改革委令
                "https://www.ndrc.gov.cn/xxgk/zcfb/ghxwj/",   # 规范性文件
                "https://www.ndrc.gov.cn/xxgk/zcfb/tz/",      # 通知
                "https://www.ndrc.gov.cn/xxgk/zcfb/gg/",      # 公告
                "https://www.ndrc.gov.cn/xxgk/zcfb/ghwb/"     # 规划文本
            ]

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            collected_count = 0

            for category_url in policy_urls:
                if collected_count >= limit:
                    break

                category_name = category_url.split('/')[-2]
                logger.info(f"正在获取发改委{category_name}类政策...")

                try:
                    response = requests.get(category_url, headers=headers, timeout=30)
                    response.encoding = 'utf-8'
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 基于实际HTML结构解析
                    policy_items = soup.select('li') or soup.select('.list-item')

                    page_policies = []
                    for item in policy_items:
                        if collected_count >= limit:
                            break

                        try:
                            # 查找链接
                            link_elem = item.find('a')
                            if link_elem and link_elem.get('href'):
                                title = link_elem.text.strip()
                                link = link_elem.get('href', '')

                                # 补全链接
                                if link and not link.startswith('http'):
                                    if link.startswith('./'):
                                        link = category_url + link[2:]
                                    elif link.startswith('/'):
                                        link = "https://www.ndrc.gov.cn" + link
                                    else:
                                        link = category_url + link

                                # 查找日期 - 基于实际HTML结构
                                date_str = ""
                                # 尝试多种日期格式
                                date_patterns = [
                                    r'(\d{4}/\d{2}/\d{2})',
                                    r'(\d{4}-\d{2}-\d{2})',
                                    r'(\d{4}\.\d{2}\.\d{2})'
                                ]

                                item_text = item.get_text()
                                for pattern in date_patterns:
                                    import re
                                    match = re.search(pattern, item_text)
                                    if match:
                                        date_str = match.group(1)
                                        break

                                # 过滤有效的政策标题
                                if title and len(title) > 10 and ('通知' in title or '办法' in title or '规定' in title or '意见' in title or '方案' in title):
                                    page_policies.append({
                                        'title': title,
                                        'link': link,
                                        'date': date_str,
                                        'content': '',  # 暂不获取详细内容以提高速度
                                        'source': f'发改委-{category_name}',
                                        'category': category_name
                                    })
                                    collected_count += 1

                        except Exception as e:
                            logger.warning(f"解析发改委政策项目失败: {e}")
                            continue

                    all_policies.extend(page_policies)
                    logger.info(f"发改委{category_name}类获取到 {len(page_policies)} 条政策，累计 {collected_count} 条")

                    # 添加延时避免被封
                    import time
                    time.sleep(2)

                except Exception as e:
                    logger.error(f"获取发改委{category_name}类政策失败: {e}")
                    continue

            df = pd.DataFrame(all_policies)
            logger.info(f"发改委政策获取完成，总计 {len(df)} 条")
            return df

        except Exception as e:
            logger.error(f"获取发改委政策失败: {str(e)}")
            return pd.DataFrame()

    def get_policy_content(self, url):
        """
        获取政策详细内容

        Args:
            url (str): 政策链接

        Returns:
            str: 政策内容
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取正文内容
            content_elem = soup.find('div', class_='pages_content') or soup.find('div', class_='content')
            if content_elem:
                return content_elem.get_text(strip=True)
            else:
                return ""

        except Exception as e:
            logger.error(f"获取政策内容失败: {str(e)}")
            return ""


