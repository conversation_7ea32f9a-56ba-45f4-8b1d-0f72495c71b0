"""
增强版资金流分析器
优化五级资金流层级联动和游资目标股票预测
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedFundFlowAnalyzer:
    """
    增强版资金流分析器

    功能：
    1. 五级资金流精确分析（北向、机构、融资、游资、散户）
    2. 游资目标股票预测算法
    3. 资金流层级联动分析
    4. 实时资金流监控
    """

    def __init__(self, config=None):
        """
        初始化增强版资金流分析器

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # 分析参数
        self.lookback_days = self.config.get('lookback_days', 30)
        self.hot_money_threshold = self.config.get('hot_money_threshold', 50000000)  # 5000万
        self.large_order_threshold = self.config.get('large_order_threshold', 1000000)  # 100万

        # 权重配置（根据市值调整）
        self.tier_weights = {
            'large_cap': {
                'northbound': 0.35,
                'institutional': 0.30,
                'margin': 0.15,
                'hot_money': 0.10,
                'retail': 0.10
            },
            'mid_cap': {
                'northbound': 0.25,
                'institutional': 0.25,
                'margin': 0.20,
                'hot_money': 0.20,
                'retail': 0.10
            },
            'small_cap': {
                'northbound': 0.10,
                'institutional': 0.20,
                'margin': 0.25,
                'hot_money': 0.30,
                'retail': 0.15
            }
        }

        logger.info("增强版资金流分析器初始化完成")

    def get_enhanced_northbound_flow(self, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取增强版北向资金流数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pd.DataFrame: 北向资金流数据
        """
        try:
            logger.info("获取北向资金流数据...")

            # 获取北向资金流汇总
            flow_summary = ak.stock_hsgt_fund_flow_summary_em()

            if flow_summary.empty:
                logger.warning("北向资金流汇总数据为空")
                return pd.DataFrame()

            # 处理数据
            northbound_data = []

            # 获取最新的北向资金数据
            for _, row in flow_summary.iterrows():
                if '北向' in str(row.get('资金方向', '')):
                    northbound_data.append({
                        'date': datetime.now().date(),
                        'net_flow': float(row.get('资金净流入', 0)),
                        'buy_amount': float(row.get('买入成交额', 0)),
                        'sell_amount': float(row.get('卖出成交额', 0)),
                        'total_amount': float(row.get('成交额', 0)),
                        'flow_strength': self._calculate_flow_strength(
                            float(row.get('资金净流入', 0)),
                            float(row.get('成交额', 0))
                        )
                    })

            df = pd.DataFrame(northbound_data)
            logger.info(f"获取到 {len(df)} 条北向资金流数据")
            return df

        except Exception as e:
            logger.error(f"获取北向资金流失败: {e}")
            return pd.DataFrame()

    def get_enhanced_institutional_flow(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取增强版机构资金流数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pd.DataFrame: 机构资金流数据
        """
        try:
            logger.info(f"获取 {stock_code} 机构资金流数据...")

            institutional_data = []

            # 1. 获取龙虎榜机构数据
            try:
                lhb_data = ak.stock_lhb_detail_em(symbol=stock_code, start_date=start_date, end_date=end_date)

                if not lhb_data.empty:
                    # 筛选机构席位
                    institutional_seats = lhb_data[
                        lhb_data['营业部名称'].str.contains('机构|基金|保险|券商|信托', na=False)
                    ]

                    if not institutional_seats.empty:
                        for _, row in institutional_seats.iterrows():
                            institutional_data.append({
                                'date': pd.to_datetime(row['交易日期']).date(),
                                'institution_type': self._classify_institution(row['营业部名称']),
                                'buy_amount': float(row.get('买入金额', 0)),
                                'sell_amount': float(row.get('卖出金额', 0)),
                                'net_amount': float(row.get('买入金额', 0)) - float(row.get('卖出金额', 0)),
                                'seat_name': row['营业部名称']
                            })

                        logger.info(f"从龙虎榜获取到 {len(institutional_seats)} 条机构交易数据")

            except Exception as e:
                logger.warning(f"获取龙虎榜机构数据失败: {e}")

            # 2. 获取基金持仓变化数据（如果可用）
            try:
                # 这里可以添加基金持仓变化的分析
                # fund_holdings = ak.fund_portfolio_hold_em(symbol=stock_code)
                pass
            except Exception as e:
                logger.debug(f"获取基金持仓数据失败: {e}")

            df = pd.DataFrame(institutional_data)
            if not df.empty:
                # 按日期聚合
                df_agg = df.groupby(['date', 'institution_type']).agg({
                    'buy_amount': 'sum',
                    'sell_amount': 'sum',
                    'net_amount': 'sum'
                }).reset_index()

                logger.info(f"获取到 {len(df_agg)} 条机构资金流数据")
                return df_agg
            else:
                logger.warning(f"{stock_code} 无机构资金流数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取机构资金流失败: {e}")
            return pd.DataFrame()

    def get_enhanced_hot_money_flow(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取增强版游资资金流数据（核心功能）

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pd.DataFrame: 游资资金流数据
        """
        try:
            logger.info(f"获取 {stock_code} 游资资金流数据...")

            hot_money_data = []

            # 获取龙虎榜数据分析游资
            try:
                lhb_data = ak.stock_lhb_detail_em(symbol=stock_code, start_date=start_date, end_date=end_date)

                if not lhb_data.empty:
                    # 筛选游资席位（非机构席位）
                    hot_money_seats = lhb_data[
                        ~lhb_data['营业部名称'].str.contains('机构|基金|保险|券商总部|信托', na=False)
                    ]

                    if not hot_money_seats.empty:
                        for _, row in hot_money_seats.iterrows():
                            buy_amount = float(row.get('买入金额', 0))
                            sell_amount = float(row.get('卖出金额', 0))

                            # 判断是否为大资金（游资特征）
                            if buy_amount > self.hot_money_threshold or sell_amount > self.hot_money_threshold:
                                hot_money_data.append({
                                    'date': pd.to_datetime(row['交易日期']).date(),
                                    'seat_name': row['营业部名称'],
                                    'buy_amount': buy_amount,
                                    'sell_amount': sell_amount,
                                    'net_amount': buy_amount - sell_amount,
                                    'activity_score': self._calculate_hot_money_activity_score(
                                        buy_amount, sell_amount, row['营业部名称']
                                    ),
                                    'seat_type': self._classify_hot_money_seat(row['营业部名称'])
                                })

                        logger.info(f"识别到 {len(hot_money_data)} 条游资交易数据")

            except Exception as e:
                logger.warning(f"获取龙虎榜游资数据失败: {e}")

            df = pd.DataFrame(hot_money_data)
            if not df.empty:
                # 按日期聚合游资数据
                df_agg = df.groupby('date').agg({
                    'buy_amount': 'sum',
                    'sell_amount': 'sum',
                    'net_amount': 'sum',
                    'activity_score': 'mean',
                    'seat_name': 'count'  # 活跃席位数
                }).reset_index()

                df_agg.rename(columns={'seat_name': 'active_seats_count'}, inplace=True)

                # 计算游资强度指标
                df_agg['hot_money_strength'] = self._calculate_hot_money_strength(df_agg)

                logger.info(f"获取到 {len(df_agg)} 条游资资金流数据")
                return df_agg
            else:
                logger.warning(f"{stock_code} 无游资资金流数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取游资资金流失败: {e}")
            return pd.DataFrame()

    def get_enhanced_retail_flow(self, stock_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        获取增强版散户资金流数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pd.DataFrame: 散户资金流数据
        """
        try:
            logger.info(f"获取 {stock_code} 散户资金流数据...")

            retail_data = []

            # 1. 获取股东户数变化
            try:
                shareholder_data = ak.stock_zh_a_gdhs(symbol=stock_code)

                if not shareholder_data.empty:
                    # 计算股东户数变化率
                    shareholder_data['户数变化率'] = shareholder_data['股东户数'].pct_change()

                    for _, row in shareholder_data.tail(10).iterrows():  # 最近10期数据
                        retail_data.append({
                            'date': pd.to_datetime(row['截止日期']).date(),
                            'shareholder_count': int(row['股东户数']),
                            'shareholder_change_rate': float(row.get('户数变化率', 0)),
                            'avg_holding_value': float(row.get('人均持股金额', 0)),
                            'retail_concentration': self._calculate_retail_concentration(
                                int(row['股东户数']), float(row.get('人均持股金额', 0))
                            )
                        })

                    logger.info(f"获取到 {len(retail_data)} 条股东户数数据")

            except Exception as e:
                logger.warning(f"获取股东户数数据失败: {e}")

            # 2. 获取个股资金流（小单为主的散户资金）
            try:
                individual_flow = ak.stock_individual_detail_em(symbol=stock_code)

                if not individual_flow.empty:
                    # 提取小单资金流（通常代表散户）
                    small_order_flow = individual_flow.get('小单净流入', 0)

                    if retail_data:
                        retail_data[-1]['small_order_net_flow'] = float(small_order_flow)
                    else:
                        retail_data.append({
                            'date': datetime.now().date(),
                            'small_order_net_flow': float(small_order_flow),
                            'shareholder_count': 0,
                            'shareholder_change_rate': 0,
                            'avg_holding_value': 0,
                            'retail_concentration': 0.5
                        })

                    logger.info("获取到个股小单资金流数据")

            except Exception as e:
                logger.warning(f"获取个股资金流数据失败: {e}")

            df = pd.DataFrame(retail_data)
            if not df.empty:
                # 计算散户情绪指标
                df['retail_sentiment'] = self._calculate_retail_sentiment(df)

                logger.info(f"获取到 {len(df)} 条散户资金流数据")
                return df
            else:
                logger.warning(f"{stock_code} 无散户资金流数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取散户资金流失败: {e}")
            return pd.DataFrame()

    def predict_hot_money_targets(self, market_data: Dict, sentiment_data: Dict) -> List[Dict]:
        """
        预测游资目标股票（核心算法）

        Args:
            market_data: 市场数据
            sentiment_data: 情绪数据

        Returns:
            List[Dict]: 游资目标股票预测结果
        """
        try:
            logger.info("开始预测游资目标股票...")

            predictions = []

            # 游资偏好特征权重
            feature_weights = {
                'price_momentum': 0.25,      # 价格动量
                'volume_surge': 0.20,        # 成交量放大
                'news_sentiment': 0.15,      # 新闻情绪
                'technical_pattern': 0.15,   # 技术形态
                'market_cap_size': 0.10,     # 市值大小
                'sector_rotation': 0.10,     # 板块轮动
                'policy_catalyst': 0.05      # 政策催化
            }

            # 模拟股票池（实际应用中从市场数据获取）
            stock_pool = [
                '000001', '000002', '000858', '002415', '300059',
                '600036', '600519', '600887', '601318', '601888'
            ]

            for stock_code in stock_pool:
                try:
                    # 计算各项特征分数
                    scores = self._calculate_hot_money_preference_scores(
                        stock_code, market_data, sentiment_data
                    )

                    # 计算综合评分
                    total_score = sum(
                        scores.get(feature, 0.5) * weight
                        for feature, weight in feature_weights.items()
                    )

                    # 计算预测置信度
                    confidence = self._calculate_prediction_confidence(scores)

                    if total_score > 0.6:  # 阈值筛选
                        predictions.append({
                            'stock_code': stock_code,
                            'prediction_score': total_score,
                            'confidence': confidence,
                            'key_factors': self._identify_key_factors(scores, feature_weights),
                            'risk_level': self._assess_risk_level(scores),
                            'time_horizon': self._estimate_time_horizon(scores),
                            'entry_signals': self._generate_entry_signals(scores)
                        })

                except Exception as e:
                    logger.debug(f"分析股票 {stock_code} 失败: {e}")
                    continue

            # 按预测分数排序
            predictions.sort(key=lambda x: x['prediction_score'], reverse=True)

            logger.info(f"预测到 {len(predictions)} 只游资目标股票")
            return predictions[:10]  # 返回前10只

        except Exception as e:
            logger.error(f"预测游资目标股票失败: {e}")
            return []

    def analyze_tier_linkage(self, stock_code: str, all_tier_data: Dict) -> Dict:
        """
        分析五级资金流层级联动效应

        Args:
            stock_code: 股票代码
            all_tier_data: 所有层级数据

        Returns:
            Dict: 层级联动分析结果
        """
        try:
            logger.info(f"分析 {stock_code} 资金流层级联动...")

            linkage_analysis = {
                'stock_code': stock_code,
                'linkage_strength': 0.0,
                'dominant_tier': None,
                'flow_direction': 'neutral',
                'synchronization_score': 0.0,
                'leading_indicators': [],
                'lagging_indicators': [],
                'anomaly_detection': {}
            }

            # 提取各层级净流入数据
            tier_flows = {}
            for tier_name, tier_data in all_tier_data.items():
                if not tier_data.empty and 'net_amount' in tier_data.columns:
                    tier_flows[tier_name] = tier_data['net_amount'].values
                elif not tier_data.empty and 'net_flow' in tier_data.columns:
                    tier_flows[tier_name] = tier_data['net_flow'].values

            if len(tier_flows) < 2:
                logger.warning(f"{stock_code} 层级数据不足，无法进行联动分析")
                return linkage_analysis

            # 计算层级间相关性
            correlations = self._calculate_tier_correlations(tier_flows)

            # 计算联动强度
            linkage_strength = np.mean([abs(corr) for corr in correlations.values()])
            linkage_analysis['linkage_strength'] = linkage_strength

            # 识别主导层级
            tier_strengths = {}
            for tier_name, flows in tier_flows.items():
                tier_strengths[tier_name] = np.mean(np.abs(flows))

            dominant_tier = max(tier_strengths, key=tier_strengths.get)
            linkage_analysis['dominant_tier'] = dominant_tier

            # 计算整体流向
            total_flow = sum(np.sum(flows) for flows in tier_flows.values())
            if total_flow > 0:
                linkage_analysis['flow_direction'] = 'inflow'
            elif total_flow < 0:
                linkage_analysis['flow_direction'] = 'outflow'
            else:
                linkage_analysis['flow_direction'] = 'neutral'

            # 计算同步性评分
            sync_score = self._calculate_synchronization_score(tier_flows)
            linkage_analysis['synchronization_score'] = sync_score

            # 识别领先和滞后指标
            leading, lagging = self._identify_leading_lagging_indicators(tier_flows)
            linkage_analysis['leading_indicators'] = leading
            linkage_analysis['lagging_indicators'] = lagging

            # 异常检测
            anomalies = self._detect_flow_anomalies(tier_flows)
            linkage_analysis['anomaly_detection'] = anomalies

            logger.info(f"{stock_code} 层级联动分析完成，联动强度: {linkage_strength:.3f}")
            return linkage_analysis

        except Exception as e:
            logger.error(f"层级联动分析失败: {e}")
            return linkage_analysis

    def _calculate_flow_strength(self, net_flow: float, total_amount: float) -> float:
        """计算资金流强度"""
        if total_amount == 0:
            return 0.5
        return min(1.0, max(0.0, 0.5 + (net_flow / total_amount)))

    def _classify_institution(self, seat_name: str) -> str:
        """分类机构类型"""
        if '基金' in seat_name:
            return 'mutual_fund'
        elif '保险' in seat_name:
            return 'insurance'
        elif '券商' in seat_name:
            return 'broker'
        elif '信托' in seat_name:
            return 'trust'
        else:
            return 'other_institution'

    def _calculate_hot_money_activity_score(self, buy_amount: float, sell_amount: float, seat_name: str) -> float:
        """计算游资活跃度评分"""
        # 基础活跃度（基于交易金额）
        total_amount = buy_amount + sell_amount
        base_score = min(1.0, total_amount / (self.hot_money_threshold * 2))

        # 席位类型调整
        if '温州' in seat_name or '宁波' in seat_name or '杭州' in seat_name:
            base_score *= 1.2  # 知名游资集中地区

        return min(1.0, base_score)

    def _classify_hot_money_seat(self, seat_name: str) -> str:
        """分类游资席位类型"""
        if '温州' in seat_name:
            return 'wenzhou_capital'
        elif '宁波' in seat_name:
            return 'ningbo_capital'
        elif '杭州' in seat_name:
            return 'hangzhou_capital'
        elif '深圳' in seat_name:
            return 'shenzhen_capital'
        else:
            return 'other_hot_money'

    def _calculate_hot_money_strength(self, df: pd.DataFrame) -> pd.Series:
        """计算游资强度指标"""
        if df.empty:
            return pd.Series()

        # 综合考虑净流入、活跃席位数、活跃度评分
        strength = (
            df['net_amount'].abs() / df['net_amount'].abs().max() * 0.5 +
            df['active_seats_count'] / df['active_seats_count'].max() * 0.3 +
            df['activity_score'] * 0.2
        )

        return strength.fillna(0.5)

    def _calculate_retail_concentration(self, shareholder_count: int, avg_holding_value: float) -> float:
        """计算散户集中度"""
        if shareholder_count == 0:
            return 0.5

        # 股东户数越少，人均持股金额越高，集中度越高
        concentration = 1 / (1 + np.log(shareholder_count + 1)) + avg_holding_value / 1000000
        return min(1.0, max(0.0, concentration))

    def _calculate_retail_sentiment(self, df: pd.DataFrame) -> pd.Series:
        """计算散户情绪指标"""
        if df.empty:
            return pd.Series()

        # 基于股东户数变化率和小单资金流
        sentiment = pd.Series(0.5, index=df.index)

        if 'shareholder_change_rate' in df.columns:
            # 股东户数减少（集中度提高）为正面信号
            sentiment += -df['shareholder_change_rate'] * 0.3

        if 'small_order_net_flow' in df.columns:
            # 小单净流入为正面信号
            max_flow = df['small_order_net_flow'].abs().max()
            if max_flow > 0:
                sentiment += df['small_order_net_flow'] / max_flow * 0.7

        return sentiment.clip(0, 1)

    def _calculate_hot_money_preference_scores(self, stock_code: str, market_data: Dict, sentiment_data: Dict) -> Dict:
        """计算游资偏好评分"""
        scores = {
            'price_momentum': 0.5,
            'volume_surge': 0.5,
            'news_sentiment': 0.5,
            'technical_pattern': 0.5,
            'market_cap_size': 0.5,
            'sector_rotation': 0.5,
            'policy_catalyst': 0.5
        }

        try:
            # 价格动量评分（简化实现）
            if 'price_change' in market_data:
                price_change = market_data['price_change']
                scores['price_momentum'] = min(1.0, max(0.0, 0.5 + price_change / 0.2))

            # 成交量放大评分
            if 'volume_ratio' in market_data:
                volume_ratio = market_data['volume_ratio']
                scores['volume_surge'] = min(1.0, max(0.0, volume_ratio / 3))

            # 新闻情绪评分
            if 'sentiment_score' in sentiment_data:
                sentiment_score = sentiment_data['sentiment_score']
                scores['news_sentiment'] = (sentiment_score + 1) / 2  # 转换到[0,1]

            # 市值大小评分（游资偏好中小盘）
            if 'market_cap' in market_data:
                market_cap = market_data['market_cap']
                if market_cap < 5e10:  # 小于500亿
                    scores['market_cap_size'] = 0.8
                elif market_cap < 1e11:  # 小于1000亿
                    scores['market_cap_size'] = 0.6
                else:
                    scores['market_cap_size'] = 0.3

        except Exception as e:
            logger.debug(f"计算游资偏好评分失败: {e}")

        return scores

    def _calculate_prediction_confidence(self, scores: Dict) -> float:
        """计算预测置信度"""
        # 基于评分的方差和极值
        score_values = list(scores.values())
        score_std = np.std(score_values)
        score_max = max(score_values)

        # 方差小且有明显优势特征的置信度高
        confidence = score_max * (1 - score_std)
        return min(1.0, max(0.0, confidence))

    def _identify_key_factors(self, scores: Dict, weights: Dict) -> List[str]:
        """识别关键因子"""
        # 计算加权评分
        weighted_scores = {k: scores.get(k, 0.5) * weights.get(k, 0.1) for k in scores.keys()}

        # 返回前3个关键因子
        sorted_factors = sorted(weighted_scores.items(), key=lambda x: x[1], reverse=True)
        return [factor for factor, _ in sorted_factors[:3]]

    def _assess_risk_level(self, scores: Dict) -> str:
        """评估风险水平"""
        avg_score = np.mean(list(scores.values()))
        score_std = np.std(list(scores.values()))

        if avg_score > 0.7 and score_std < 0.2:
            return 'low'
        elif avg_score > 0.5 and score_std < 0.3:
            return 'medium'
        else:
            return 'high'

    def _estimate_time_horizon(self, scores: Dict) -> str:
        """估计时间周期"""
        momentum_score = scores.get('price_momentum', 0.5)
        volume_score = scores.get('volume_surge', 0.5)

        if momentum_score > 0.8 and volume_score > 0.8:
            return 'short_term'  # 1-3天
        elif momentum_score > 0.6:
            return 'medium_term'  # 1-2周
        else:
            return 'long_term'  # 1个月以上

    def _generate_entry_signals(self, scores: Dict) -> List[str]:
        """生成入场信号"""
        signals = []

        if scores.get('price_momentum', 0) > 0.7:
            signals.append('price_breakout')

        if scores.get('volume_surge', 0) > 0.8:
            signals.append('volume_spike')

        if scores.get('news_sentiment', 0) > 0.7:
            signals.append('positive_catalyst')

        return signals

    def _calculate_tier_correlations(self, tier_flows: Dict) -> Dict:
        """计算层级间相关性"""
        correlations = {}
        tier_names = list(tier_flows.keys())

        for i in range(len(tier_names)):
            for j in range(i + 1, len(tier_names)):
                tier1, tier2 = tier_names[i], tier_names[j]
                flows1, flows2 = tier_flows[tier1], tier_flows[tier2]

                # 确保长度一致
                min_len = min(len(flows1), len(flows2))
                if min_len > 1:
                    corr = np.corrcoef(flows1[:min_len], flows2[:min_len])[0, 1]
                    correlations[f"{tier1}_{tier2}"] = corr if not np.isnan(corr) else 0.0

        return correlations

    def _calculate_synchronization_score(self, tier_flows: Dict) -> float:
        """计算同步性评分"""
        if len(tier_flows) < 2:
            return 0.0

        # 计算所有层级的方向一致性
        directions = []
        for flows in tier_flows.values():
            if len(flows) > 0:
                avg_flow = np.mean(flows)
                directions.append(1 if avg_flow > 0 else -1 if avg_flow < 0 else 0)

        if not directions:
            return 0.0

        # 计算方向一致性
        positive_count = sum(1 for d in directions if d > 0)
        negative_count = sum(1 for d in directions if d < 0)
        neutral_count = sum(1 for d in directions if d == 0)

        total_count = len(directions)
        max_consistent = max(positive_count, negative_count, neutral_count)

        return max_consistent / total_count

    def _identify_leading_lagging_indicators(self, tier_flows: Dict) -> Tuple[List[str], List[str]]:
        """识别领先和滞后指标"""
        leading = []
        lagging = []

        tier_names = list(tier_flows.keys())

        # 简化实现：基于波动性和趋势强度
        tier_metrics = {}
        for tier_name, flows in tier_flows.items():
            if len(flows) > 1:
                volatility = np.std(flows)
                trend_strength = abs(np.mean(flows))
                tier_metrics[tier_name] = {'volatility': volatility, 'trend': trend_strength}

        # 波动性高的通常是领先指标
        sorted_by_volatility = sorted(tier_metrics.items(),
                                    key=lambda x: x[1]['volatility'], reverse=True)

        mid_point = len(sorted_by_volatility) // 2
        leading = [item[0] for item in sorted_by_volatility[:mid_point]]
        lagging = [item[0] for item in sorted_by_volatility[mid_point:]]

        return leading, lagging

    def _detect_flow_anomalies(self, tier_flows: Dict) -> Dict:
        """检测资金流异常"""
        anomalies = {}

        for tier_name, flows in tier_flows.items():
            if len(flows) > 3:
                # 使用3σ规则检测异常
                mean_flow = np.mean(flows)
                std_flow = np.std(flows)

                if std_flow > 0:
                    z_scores = np.abs((flows - mean_flow) / std_flow)
                    anomaly_indices = np.where(z_scores > 3)[0]

                    if len(anomaly_indices) > 0:
                        anomalies[tier_name] = {
                            'anomaly_count': len(anomaly_indices),
                            'anomaly_ratio': len(anomaly_indices) / len(flows),
                            'max_z_score': float(np.max(z_scores)),
                            'anomaly_values': flows[anomaly_indices].tolist()
                        }

        return anomalies

    def generate_comprehensive_report(self, stock_code: str, analysis_results: Dict) -> Dict:
        """
        生成综合分析报告

        Args:
            stock_code: 股票代码
            analysis_results: 分析结果

        Returns:
            Dict: 综合报告
        """
        try:
            logger.info(f"生成 {stock_code} 综合资金流分析报告...")

            report = {
                'stock_code': stock_code,
                'analysis_timestamp': datetime.now().isoformat(),
                'overall_score': 0.0,
                'risk_assessment': 'medium',
                'investment_recommendation': 'hold',
                'key_insights': [],
                'tier_analysis': {},
                'prediction_results': {},
                'risk_factors': [],
                'opportunity_factors': []
            }

            # 计算综合评分
            tier_scores = []
            for tier_name, tier_data in analysis_results.get('tier_data', {}).items():
                if not tier_data.empty:
                    # 简化评分计算
                    if 'net_amount' in tier_data.columns:
                        avg_flow = tier_data['net_amount'].mean()
                        tier_score = 0.5 + np.tanh(avg_flow / 1000000) * 0.5  # 标准化到[0,1]
                        tier_scores.append(tier_score)

            if tier_scores:
                report['overall_score'] = np.mean(tier_scores)

            # 风险评估
            if report['overall_score'] > 0.7:
                report['risk_assessment'] = 'low'
                report['investment_recommendation'] = 'buy'
            elif report['overall_score'] > 0.3:
                report['risk_assessment'] = 'medium'
                report['investment_recommendation'] = 'hold'
            else:
                report['risk_assessment'] = 'high'
                report['investment_recommendation'] = 'sell'

            # 关键洞察
            linkage_data = analysis_results.get('linkage_analysis', {})
            if linkage_data.get('linkage_strength', 0) > 0.6:
                report['key_insights'].append('资金流层级联动性强，市场共识度高')

            if linkage_data.get('dominant_tier'):
                dominant = linkage_data['dominant_tier']
                report['key_insights'].append(f'{dominant}资金为主导力量')

            # 预测结果
            predictions = analysis_results.get('hot_money_predictions', [])
            if predictions:
                report['prediction_results'] = {
                    'predicted_targets': len(predictions),
                    'avg_confidence': np.mean([p['confidence'] for p in predictions]),
                    'top_target': predictions[0] if predictions else None
                }

            # 风险因子
            anomalies = linkage_data.get('anomaly_detection', {})
            if anomalies:
                report['risk_factors'].append('检测到资金流异常波动')

            if report['overall_score'] < 0.3:
                report['risk_factors'].append('整体资金流偏弱')

            # 机会因子
            if report['overall_score'] > 0.7:
                report['opportunity_factors'].append('多层级资金流入，上涨动能充足')

            if linkage_data.get('flow_direction') == 'inflow':
                report['opportunity_factors'].append('资金净流入，买盘活跃')

            logger.info(f"{stock_code} 综合报告生成完成，综合评分: {report['overall_score']:.3f}")
            return report

        except Exception as e:
            logger.error(f"生成综合报告失败: {e}")
            return {
                'stock_code': stock_code,
                'analysis_timestamp': datetime.now().isoformat(),
                'overall_score': 0.5,
                'risk_assessment': 'unknown',
                'investment_recommendation': 'hold',
                'error': str(e)
            }
