"""
PolicyDataSource - 政策和新闻数据源类
实现您要求的所有API接口
"""

import pandas as pd
import akshare as ak
from datetime import datetime, timedelta
import requests
from bs4 import BeautifulSoup

from utils.logger import logger
from utils.error_utils import retry
from utils.cache_utils import cached


class PolicyDataSource:
    """政策和新闻数据源类"""

    def __init__(self):
        """初始化政策数据源"""
        self.default_lookback_days = 7
        logger.info("PolicyDataSource initialized")

    # ==================== 政策文件获取 ====================

    @cached(expiry=3600)
    @retry(max_retries=3, delay=2)
    def get_gov_policy(self, page=1, limit=1000):
        """
        获取国务院政策文件库政策（扩充到1000条）

        Args:
            page (int): 页码
            limit (int): 每页数量（默认1000条）

        Returns:
            pandas.DataFrame: 政策数据
        """
        try:
            all_policies = []
            base_url = "http://www.gov.cn/zhengce/zhengceku.htm"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # 分页获取政策数据
            current_page = page
            collected_count = 0

            while collected_count < limit:
                try:
                    # 构建分页URL
                    if current_page == 1:
                        url = base_url
                    else:
                        url = f"http://www.gov.cn/zhengce/zhengceku_{current_page}.htm"

                    logger.info(f"正在获取国务院政策第 {current_page} 页...")
                    response = requests.get(url, headers=headers, timeout=30)
                    response.encoding = 'utf-8'
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 解析政策列表
                    policy_items = soup.select('.news_box li')

                    if not policy_items:
                        logger.warning(f"第 {current_page} 页没有找到政策数据，停止获取")
                        break

                    page_policies = []
                    for item in policy_items:
                        if collected_count >= limit:
                            break

                        try:
                            title_elem = item.find('a')
                            if title_elem:
                                title = title_elem.text.strip()
                                link = title_elem.get('href', '')
                                if link and not link.startswith('http'):
                                    link = "http://www.gov.cn" + link

                                date_elem = item.find('span')
                                date_str = date_elem.text.strip() if date_elem else ''

                                # 获取政策详细内容（可选）
                                content = ""
                                if link:
                                    try:
                                        content = self.get_policy_content(link)
                                    except:
                                        pass  # 内容获取失败不影响主流程

                                page_policies.append({
                                    'title': title,
                                    'link': link,
                                    'date': date_str,
                                    'content': content,
                                    'source': '国务院',
                                    'page': current_page
                                })
                                collected_count += 1

                        except Exception as e:
                            logger.warning(f"解析政策项目失败: {e}")
                            continue

                    all_policies.extend(page_policies)
                    logger.info(f"第 {current_page} 页获取到 {len(page_policies)} 条政策，累计 {collected_count} 条")

                    # 添加延时避免被封
                    import time
                    time.sleep(2)
                    current_page += 1

                except Exception as e:
                    logger.error(f"获取第 {current_page} 页失败: {e}")
                    current_page += 1
                    continue

            df = pd.DataFrame(all_policies)
            logger.info(f"国务院政策获取完成，总计 {len(df)} 条")
            return df

        except Exception as e:
            logger.error(f"获取国务院政策失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=3600)
    @retry(max_retries=3, delay=2)
    def get_ndrc_policy(self, page=1, limit=1000):
        """
        获取发改委政策（扩充到1000条）

        Args:
            page (int): 页码
            limit (int): 每页数量（默认1000条）

        Returns:
            pandas.DataFrame: 政策数据
        """
        try:
            all_policies = []
            base_url = "https://www.ndrc.gov.cn/xxgk/zcfb/"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            # 分页获取政策数据
            current_page = page
            collected_count = 0

            while collected_count < limit:
                try:
                    # 构建分页URL
                    if current_page == 1:
                        url = base_url
                    else:
                        url = f"https://www.ndrc.gov.cn/xxgk/zcfb/index_{current_page}.html"

                    logger.info(f"正在获取发改委政策第 {current_page} 页...")
                    response = requests.get(url, headers=headers, timeout=30)
                    response.encoding = 'utf-8'
                    soup = BeautifulSoup(response.text, 'html.parser')

                    # 解析政策列表（尝试多种选择器）
                    policy_items = soup.select('.list_01 li') or soup.select('.list li') or soup.select('li')

                    if not policy_items:
                        logger.warning(f"第 {current_page} 页没有找到政策数据，停止获取")
                        break

                    page_policies = []
                    for item in policy_items:
                        if collected_count >= limit:
                            break

                        try:
                            title_elem = item.find('a')
                            if title_elem:
                                title = title_elem.text.strip()
                                link = title_elem.get('href', '')
                                if link and not link.startswith('http'):
                                    link = "https://www.ndrc.gov.cn" + link

                                # 尝试多种日期选择器
                                date_elem = item.find('.date') or item.find('span') or item.find('.time')
                                date_str = date_elem.text.strip() if date_elem else ''

                                # 获取政策详细内容（可选）
                                content = ""
                                if link:
                                    try:
                                        content = self.get_policy_content(link)
                                    except:
                                        pass  # 内容获取失败不影响主流程

                                page_policies.append({
                                    'title': title,
                                    'link': link,
                                    'date': date_str,
                                    'content': content,
                                    'source': '发改委',
                                    'page': current_page
                                })
                                collected_count += 1

                        except Exception as e:
                            logger.warning(f"解析发改委政策项目失败: {e}")
                            continue

                    all_policies.extend(page_policies)
                    logger.info(f"第 {current_page} 页获取到 {len(page_policies)} 条政策，累计 {collected_count} 条")

                    # 添加延时避免被封
                    import time
                    time.sleep(3)  # 发改委网站延时稍长
                    current_page += 1

                except Exception as e:
                    logger.error(f"获取发改委第 {current_page} 页失败: {e}")
                    current_page += 1
                    continue

            df = pd.DataFrame(all_policies)
            logger.info(f"发改委政策获取完成，总计 {len(df)} 条")
            return df

        except Exception as e:
            logger.error(f"获取发改委政策失败: {str(e)}")
            return pd.DataFrame()

    def get_policy_content(self, url):
        """
        获取政策详细内容

        Args:
            url (str): 政策链接

        Returns:
            str: 政策内容
        """
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.encoding = 'utf-8'
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取正文内容
            content_elem = soup.find('div', class_='pages_content') or soup.find('div', class_='content')
            if content_elem:
                return content_elem.get_text(strip=True)
            else:
                return ""

        except Exception as e:
            logger.error(f"获取政策内容失败: {str(e)}")
            return ""

    # ==================== 新闻数据获取 ====================

    @cached(expiry=1800)  # 30分钟缓存
    @retry(max_retries=3, delay=2)
    def get_financial_breakfast(self):
        """
        获取财经早餐-东方财富
        接口：stock_info_cjzc_em

        Returns:
            pandas.DataFrame: 财经早餐数据
        """
        try:
            df = ak.stock_info_cjzc_em()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '摘要': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '东方财富财经早餐'
                logger.info(f"获取到 {len(df)} 条财经早餐")
            return df
        except Exception as e:
            logger.error(f"获取财经早餐失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_em(self):
        """
        获取全球财经快讯-东方财富
        接口：stock_info_global_em

        Returns:
            pandas.DataFrame: 全球财经快讯数据
        """
        try:
            df = ak.stock_info_global_em()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '摘要': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '东方财富全球快讯'
                logger.info(f"获取到 {len(df)} 条全球财经快讯")
            return df
        except Exception as e:
            logger.error(f"获取全球财经快讯失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_sina(self):
        """
        获取全球财经快讯-新浪财经
        接口：stock_info_global_sina

        Returns:
            pandas.DataFrame: 新浪财经快讯数据
        """
        try:
            df = ak.stock_info_global_sina()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '时间': 'publish_date',
                    '内容': 'title'  # 新浪接口只有时间和内容
                })
                df['content'] = df['title']  # 内容和标题相同
                df['url'] = ''  # 新浪接口没有链接
                df['source'] = '新浪财经'
                logger.info(f"获取到 {len(df)} 条新浪财经快讯")
            return df
        except Exception as e:
            logger.error(f"获取新浪财经快讯失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_futu(self):
        """
        获取快讯-富途牛牛
        接口：stock_info_global_futu

        Returns:
            pandas.DataFrame: 富途牛牛快讯数据
        """
        try:
            df = ak.stock_info_global_futu()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '富途牛牛'
                logger.info(f"获取到 {len(df)} 条富途牛牛快讯")
            return df
        except Exception as e:
            logger.error(f"获取富途牛牛快讯失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_ths(self):
        """
        获取全球财经直播-同花顺财经
        接口：stock_info_global_ths

        Returns:
            pandas.DataFrame: 同花顺财经数据
        """
        try:
            df = ak.stock_info_global_ths()
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '发布时间': 'publish_date',
                    '链接': 'url'
                })
                df['source'] = '同花顺财经'
                logger.info(f"获取到 {len(df)} 条同花顺财经")
            return df
        except Exception as e:
            logger.error(f"获取同花顺财经失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_global_news_cls(self, symbol="全部"):
        """
        获取电报-财联社
        接口：stock_info_global_cls

        Args:
            symbol (str): 符号筛选

        Returns:
            pandas.DataFrame: 财联社电报数据
        """
        try:
            df = ak.stock_info_global_cls(symbol=symbol)
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '标题': 'title',
                    '内容': 'content',
                    '发布日期': 'publish_date',
                    '发布时间': 'time'
                })
                # 合并日期和时间
                if 'time' in df.columns:
                    df['publish_date'] = df['publish_date'].astype(str) + ' ' + df['time'].astype(str)
                df['url'] = ''  # 财联社接口没有链接
                df['source'] = '财联社'
                logger.info(f"获取到 {len(df)} 条财联社电报")
            return df
        except Exception as e:
            logger.error(f"获取财联社电报失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_broker_news_sina(self, page="1"):
        """
        获取证券原创-新浪财经
        接口：stock_info_broker_sina

        Args:
            page (str): 页码

        Returns:
            pandas.DataFrame: 新浪证券原创数据
        """
        try:
            df = ak.stock_info_broker_sina(page=page)
            if not df.empty:
                # 重命名列以统一格式
                df = df.rename(columns={
                    '时间': 'publish_date',
                    '内容': 'title',
                    '链接': 'url'
                })
                df['content'] = df['title']  # 内容和标题相同
                df['source'] = '新浪证券'
                logger.info(f"获取到 {len(df)} 条新浪证券原创")
            return df
        except Exception as e:
            logger.error(f"获取新浪证券原创失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_stock_news_em(self):
        """
        获取个股新闻-东方财富

        Returns:
            pandas.DataFrame: 个股新闻数据
        """
        try:
            df = ak.stock_news_em()
            if not df.empty:
                logger.info(f"获取到 {len(df)} 条个股新闻")
            return df
        except Exception as e:
            logger.error(f"获取个股新闻失败: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=1800)
    @retry(max_retries=3, delay=2)
    def get_news_main_cx(self):
        """
        获取财经内容精选-财新网

        Returns:
            pandas.DataFrame: 财经内容精选数据
        """
        try:
            df = ak.stock_news_main_cx()
            if not df.empty:
                logger.info(f"获取到 {len(df)} 条财经内容精选")
            return df
        except Exception as e:
            logger.error(f"获取财经内容精选失败: {str(e)}")
            return pd.DataFrame()
