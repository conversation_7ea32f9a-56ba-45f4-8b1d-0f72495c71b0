"""
测试核心决策引擎
验证多因子信号整合、市场状态适应、风险评估和投资决策生成功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('core_decision_engine_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_mock_signals():
    """创建模拟信号数据"""
    
    # 政策信号（积极）
    policy_signals = {
        'policy_impact_score': 0.75,
        'direction': 'positive',
        'strength': 'strong',
        'comprehensive_score': 0.72
    }
    
    # 情绪信号（中性偏积极）
    sentiment_signals = {
        'sentiment_index': 0.3,  # -1到1之间
        'resonance_strength': 0.65,
        'trend': 'improving',
        'comprehensive_sentiment_score': 0.65
    }
    
    # 资金流信号（积极）
    fund_flow_signals = {
        'comprehensive_score': 0.68,
        'net_inflow_ratio': 0.4,
        'main_force_activity': 'high',
        'hot_money_interest': 'high'
    }
    
    # 波动率信号（中性）
    volatility_signals = {
        'current_volatility': 0.22,
        'volatility_signal_strength': 0.55,
        'current_regime': 'normal',
        'trend': 'stable'
    }
    
    return policy_signals, sentiment_signals, fund_flow_signals, volatility_signals

def create_bearish_signals():
    """创建熊市信号数据"""
    
    # 政策信号（消极）
    policy_signals = {
        'policy_impact_score': 0.25,
        'direction': 'negative',
        'strength': 'medium',
        'comprehensive_score': 0.28
    }
    
    # 情绪信号（消极）
    sentiment_signals = {
        'sentiment_index': -0.4,
        'resonance_strength': 0.35,
        'trend': 'declining',
        'comprehensive_sentiment_score': 0.30
    }
    
    # 资金流信号（消极）
    fund_flow_signals = {
        'comprehensive_score': 0.25,
        'net_inflow_ratio': -0.6,
        'main_force_activity': 'low',
        'hot_money_interest': 'low'
    }
    
    # 波动率信号（高波动）
    volatility_signals = {
        'current_volatility': 0.45,
        'volatility_signal_strength': 0.20,
        'current_regime': 'high',
        'trend': 'increasing'
    }
    
    return policy_signals, sentiment_signals, fund_flow_signals, volatility_signals

def test_core_decision_engine():
    """测试核心决策引擎"""
    
    logger.info("=" * 80)
    logger.info("测试核心决策引擎")
    logger.info("=" * 80)
    
    try:
        from engines.decision.core_engine import CoreDecisionEngine
        
        # 初始化决策引擎
        config = {
            'policy_weight': 0.25,
            'sentiment_weight': 0.20,
            'fund_flow_weight': 0.30,
            'volatility_weight': 0.25
        }
        
        engine = CoreDecisionEngine(config=config)
        logger.info("✅ CoreDecisionEngine 初始化成功")
        
        test_results = {}
        
        # 1. 测试积极信号决策
        logger.info("\n" + "=" * 60)
        logger.info("测试积极信号决策生成")
        logger.info("=" * 60)
        
        try:
            policy_signals, sentiment_signals, fund_flow_signals, volatility_signals = create_mock_signals()
            
            decision_result = engine.generate_comprehensive_decision(
                symbol='000001',
                policy_signals=policy_signals,
                sentiment_signals=sentiment_signals,
                fund_flow_signals=fund_flow_signals,
                volatility_signals=volatility_signals
            )
            
            logger.info("✅ 积极信号决策生成完成")
            logger.info(f"   股票代码: {decision_result['symbol']}")
            logger.info(f"   综合评分: {decision_result['composite_score']:.3f}")
            logger.info(f"   投资决策: {decision_result['decision']['action']}")
            logger.info(f"   决策置信度: {decision_result['decision']['confidence']:.3f}")
            logger.info(f"   推荐仓位: {decision_result['position_recommendation']['recommended_position']:.2%}")
            logger.info(f"   市场状态: {decision_result['market_regime']}")
            logger.info(f"   风险等级: {decision_result['risk_assessment']['risk_level']}")
            logger.info(f"   整体置信度: {decision_result['confidence_level']:.3f}")
            
            # 显示信号解释
            if decision_result['signal_explanation']:
                logger.info("   信号解释:")
                for explanation in decision_result['signal_explanation']:
                    logger.info(f"     • {explanation}")
            
            test_results['positive_signals'] = True
            
        except Exception as e:
            logger.error(f"❌ 积极信号决策测试失败: {e}")
            test_results['positive_signals'] = False
        
        # 2. 测试消极信号决策
        logger.info("\n" + "=" * 60)
        logger.info("测试消极信号决策生成")
        logger.info("=" * 60)
        
        try:
            policy_signals, sentiment_signals, fund_flow_signals, volatility_signals = create_bearish_signals()
            
            decision_result = engine.generate_comprehensive_decision(
                symbol='000002',
                policy_signals=policy_signals,
                sentiment_signals=sentiment_signals,
                fund_flow_signals=fund_flow_signals,
                volatility_signals=volatility_signals
            )
            
            logger.info("✅ 消极信号决策生成完成")
            logger.info(f"   股票代码: {decision_result['symbol']}")
            logger.info(f"   综合评分: {decision_result['composite_score']:.3f}")
            logger.info(f"   投资决策: {decision_result['decision']['action']}")
            logger.info(f"   决策置信度: {decision_result['decision']['confidence']:.3f}")
            logger.info(f"   推荐仓位: {decision_result['position_recommendation']['recommended_position']:.2%}")
            logger.info(f"   市场状态: {decision_result['market_regime']}")
            logger.info(f"   风险等级: {decision_result['risk_assessment']['risk_level']}")
            logger.info(f"   决策推理: {decision_result['decision']['reasoning']}")
            
            test_results['negative_signals'] = True
            
        except Exception as e:
            logger.error(f"❌ 消极信号决策测试失败: {e}")
            test_results['negative_signals'] = False
        
        # 3. 测试批量决策分析
        logger.info("\n" + "=" * 60)
        logger.info("测试批量决策分析")
        logger.info("=" * 60)
        
        try:
            # 准备批量测试数据
            symbols = ['000001', '000002', '600519', '000858', '002415']
            
            # 模拟不同股票的信号数据
            all_signals = {
                'policy': {
                    '000001': {'policy_impact_score': 0.75, 'direction': 'positive'},
                    '000002': {'policy_impact_score': 0.25, 'direction': 'negative'},
                    '600519': {'policy_impact_score': 0.65, 'direction': 'positive'},
                    '000858': {'policy_impact_score': 0.45, 'direction': 'neutral'},
                    '002415': {'policy_impact_score': 0.55, 'direction': 'positive'}
                },
                'sentiment': {
                    '000001': {'sentiment_index': 0.3, 'comprehensive_sentiment_score': 0.65},
                    '000002': {'sentiment_index': -0.4, 'comprehensive_sentiment_score': 0.30},
                    '600519': {'sentiment_index': 0.5, 'comprehensive_sentiment_score': 0.75},
                    '000858': {'sentiment_index': 0.1, 'comprehensive_sentiment_score': 0.55},
                    '002415': {'sentiment_index': 0.2, 'comprehensive_sentiment_score': 0.60}
                },
                'fund_flow': {
                    '000001': {'comprehensive_score': 0.68, 'main_force_activity': 'high'},
                    '000002': {'comprehensive_score': 0.25, 'main_force_activity': 'low'},
                    '600519': {'comprehensive_score': 0.72, 'main_force_activity': 'high'},
                    '000858': {'comprehensive_score': 0.48, 'main_force_activity': 'medium'},
                    '002415': {'comprehensive_score': 0.58, 'main_force_activity': 'medium'}
                },
                'volatility': {
                    '000001': {'current_volatility': 0.22, 'volatility_signal_strength': 0.55},
                    '000002': {'current_volatility': 0.45, 'volatility_signal_strength': 0.20},
                    '600519': {'current_volatility': 0.18, 'volatility_signal_strength': 0.70},
                    '000858': {'current_volatility': 0.28, 'volatility_signal_strength': 0.45},
                    '002415': {'current_volatility': 0.25, 'volatility_signal_strength': 0.50}
                }
            }
            
            batch_result = engine.batch_decision_analysis(symbols, all_signals)
            
            logger.info("✅ 批量决策分析完成")
            logger.info(f"   分析股票数: {batch_result['total_symbols']}")
            logger.info(f"   决策统计: {batch_result['summary_statistics']}")
            
            portfolio = batch_result['portfolio_recommendation']
            logger.info(f"   推荐股票数: {len(portfolio['recommended_stocks'])}")
            logger.info(f"   总仓位: {portfolio['total_position']:.2%}")
            logger.info(f"   分散化评分: {portfolio['diversification_score']:.3f}")
            logger.info(f"   平均置信度: {portfolio['average_confidence']:.3f}")
            
            if portfolio['recommended_stocks']:
                logger.info("   推荐股票详情:")
                for stock in portfolio['recommended_stocks'][:3]:  # 显示前3只
                    logger.info(f"     {stock['symbol']}: {stock['action']} (仓位{stock['position']:.2%}, 评分{stock['score']:.3f})")
            
            test_results['batch_analysis'] = True
            
        except Exception as e:
            logger.error(f"❌ 批量决策分析测试失败: {e}")
            test_results['batch_analysis'] = False
        
        return test_results
        
    except Exception as e:
        logger.error(f"❌ 核心决策引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_market_regime_adaptation():
    """测试市场状态适应性"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试市场状态适应性")
    logger.info("=" * 80)
    
    try:
        from engines.decision.core_engine import CoreDecisionEngine
        
        engine = CoreDecisionEngine()
        
        # 测试不同市场状态下的权重调整
        test_scenarios = [
            {
                'name': '牛市场景',
                'volatility': 0.12,
                'sentiment': 0.7,
                'expected_regime': 'bull'
            },
            {
                'name': '熊市场景',
                'volatility': 0.40,
                'sentiment': 0.2,
                'expected_regime': 'bear'
            },
            {
                'name': '震荡市场景',
                'volatility': 0.25,
                'sentiment': 0.5,
                'expected_regime': 'sideways'
            }
        ]
        
        for scenario in test_scenarios:
            logger.info(f"\n🔍 测试{scenario['name']}...")
            
            # 构造测试信号
            volatility_signals = {'current_volatility': scenario['volatility']}
            sentiment_signals = {'sentiment_index': scenario['sentiment'] * 2 - 1}  # 转换到-1到1
            
            # 识别市场状态
            market_regime = engine._identify_market_regime(volatility_signals, sentiment_signals)
            logger.info(f"   识别的市场状态: {market_regime}")
            
            # 测试权重调整
            adjusted_weights = engine._adjust_weights_by_regime(market_regime)
            logger.info(f"   调整后权重: {adjusted_weights}")
            
            # 验证预期
            if market_regime == scenario['expected_regime']:
                logger.info(f"   ✅ 市场状态识别正确")
            else:
                logger.warning(f"   ⚠️ 市场状态识别异常，期望{scenario['expected_regime']}，实际{market_regime}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 市场状态适应性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info(f"开始核心决策引擎测试 - {datetime.now()}")
    
    # 执行各项测试
    engine_results = test_core_decision_engine()
    adaptation_success = test_market_regime_adaptation()
    
    # 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("核心决策引擎测试结果")
    logger.info("=" * 80)
    
    if engine_results:
        success_count = sum(engine_results.values())
        total_count = len(engine_results)
        
        for test_name, success in engine_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            logger.info(f"{status} {test_name}")
        
        logger.info(f"\n基础功能: {success_count}/{total_count} 项测试通过")
    else:
        logger.warning("⚠️ 基础功能测试失败")
        success_count, total_count = 0, 0
    
    adaptation_status = "✅ 成功" if adaptation_success else "❌ 失败"
    logger.info(f"{adaptation_status} 市场状态适应性测试")
    
    # 总体评估
    overall_success = (
        success_count >= 2 and  # 至少2项基础功能通过
        adaptation_success      # 适应性测试成功
    )
    
    if overall_success:
        logger.info("\n🎉 核心决策引擎功能正常！")
        logger.info("🧠 多因子信号整合成功")
        logger.info("📊 市场状态适应性良好")
        logger.info("⚖️ 风险评估机制完善")
        logger.info("💼 投资组合建议合理")
        logger.info("🚀 核心决策引擎开发完成")
    else:
        logger.warning("⚠️ 核心决策引擎存在问题，需要进一步调试")
    
    logger.info(f"\n测试完成时间: {datetime.now()}")
    logger.info("详细日志已保存到: core_decision_engine_test.log")
    
    return overall_success

if __name__ == "__main__":
    main()
