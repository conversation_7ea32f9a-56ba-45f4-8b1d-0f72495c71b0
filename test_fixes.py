#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的系统组件
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from utils.logger import logger

def test_volatility_analyzer():
    """测试波动率分析器修复"""
    print("=" * 50)
    print("测试波动率分析器")
    print("=" * 50)
    
    try:
        from engines.volatility.analyzer import VolatilityAnalyzer
        
        analyzer = VolatilityAnalyzer()
        print("✅ 波动率分析器初始化成功")
        
        # 测试获取历史价格
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        print(f"测试获取股票000001的历史价格 ({start_date} 到 {end_date})")
        prices = analyzer.get_historical_prices('000001', start_date, end_date)
        
        if not prices.empty:
            print(f"✅ 成功获取历史价格数据，共{len(prices)}条记录")
            print(f"数据列: {list(prices.columns)}")
        else:
            print("⚠️ 历史价格数据为空")
            
    except Exception as e:
        print(f"❌ 波动率分析器测试失败: {str(e)}")

def test_fund_flow_analyzer():
    """测试资金流分析器修复"""
    print("=" * 50)
    print("测试资金流分析器")
    print("=" * 50)
    
    try:
        from engines.tiered_fund_flow.analyzer import TieredFundFlowAnalyzer
        
        analyzer = TieredFundFlowAnalyzer()
        print("✅ 资金流分析器初始化成功")
        
        # 测试获取融资融券数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        print(f"测试获取股票000001的融资融券数据 ({start_date} 到 {end_date})")
        margin_data = analyzer.get_margin_flow('000001', start_date, end_date)
        
        if not margin_data.empty:
            print(f"✅ 成功获取融资融券数据，共{len(margin_data)}条记录")
            print(f"数据列: {list(margin_data.columns)}")
        else:
            print("⚠️ 融资融券数据为空（可能是正常的，因为API限制）")
            
    except Exception as e:
        print(f"❌ 资金流分析器测试失败: {str(e)}")

def test_industry_classification():
    """测试行业分类修复"""
    print("=" * 50)
    print("测试行业分类")
    print("=" * 50)
    
    try:
        from utils.stock_utils import get_industry_classification
        
        print("测试申万行业分类...")
        industry_data = get_industry_classification('sw')
        
        if not industry_data.empty:
            print(f"✅ 成功获取申万行业分类，共{len(industry_data)}条记录")
            print(f"数据列: {list(industry_data.columns)}")
            print(f"示例数据: {industry_data.head(3).to_dict('records')}")
        else:
            print("⚠️ 申万行业分类数据为空")
            
    except Exception as e:
        print(f"❌ 行业分类测试失败: {str(e)}")

def test_sentiment_analyzer():
    """测试情绪分析器配置修复"""
    print("=" * 50)
    print("测试情绪分析器")
    print("=" * 50)
    
    try:
        from engines.sentiment.analyzer import SentimentAnalyzer
        
        analyzer = SentimentAnalyzer()
        print("✅ 情绪分析器初始化成功")
        
        # 测试情绪分析
        test_texts = ["央行降准释放流动性", "股市大跌引发恐慌"]
        print(f"测试分析文本: {test_texts}")
        
        results = analyzer.analyze_batch(test_texts)
        
        if results:
            print("✅ 情绪分析成功")
            for result in results:
                print(f"文本: {result['text']}")
                print(f"情绪: {result['sentiment']}, 置信度: {result['confidence']:.3f}")
        else:
            print("⚠️ 情绪分析结果为空")
            
    except Exception as e:
        print(f"❌ 情绪分析器测试失败: {str(e)}")

def test_config_loading():
    """测试配置加载"""
    print("=" * 50)
    print("测试配置加载")
    print("=" * 50)
    
    try:
        from utils.config_loader import config_loader
        
        config = config_loader.get_config()
        print("✅ 配置加载成功")
        
        # 检查情绪分析模型路径配置
        sentiment_config = config.get('engines', {}).get('sentiment', {})
        model_path = sentiment_config.get('model_path', '')
        
        print(f"情绪分析模型路径: {model_path}")
        
        if 'finbert' in model_path:
            print("✅ 情绪分析模型路径配置正确")
        else:
            print("⚠️ 情绪分析模型路径可能需要调整")
            
        # 检查FinBERT模型文件是否存在
        import os
        finbert_path = "models/finbert"
        if os.path.exists(finbert_path):
            files = os.listdir(finbert_path)
            print(f"FinBERT模型文件: {files}")
            
            required_files = ['config.json', 'pytorch_model.bin', 'vocab.txt']
            missing_files = [f for f in required_files if f not in files]
            
            if not missing_files:
                print("✅ FinBERT模型文件完整")
            else:
                print(f"⚠️ FinBERT模型缺少文件: {missing_files}")
        else:
            print("❌ FinBERT模型目录不存在")
            
    except Exception as e:
        print(f"❌ 配置加载测试失败: {str(e)}")

def main():
    """主测试函数"""
    print("🔧 系统修复验证测试")
    print("=" * 60)
    
    # 运行各项测试
    test_config_loading()
    test_industry_classification()
    test_volatility_analyzer()
    test_fund_flow_analyzer()
    test_sentiment_analyzer()
    
    print("=" * 60)
    print("🎯 测试完成！")
    print("请查看上述测试结果，确认修复效果。")

if __name__ == "__main__":
    main()
