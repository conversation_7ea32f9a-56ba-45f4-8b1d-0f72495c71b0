"""
完整测试新闻政策模块所有接口
记录详细运行日志
"""

import pandas as pd
import time
from datetime import datetime
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('news_policy_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_policy_data_source():
    """测试PolicyDataSource类的所有接口"""
    
    logger.info("=" * 80)
    logger.info("开始测试PolicyDataSource类所有接口")
    logger.info("=" * 80)
    
    try:
        from data_sources.policy_data import PolicyDataSource
        policy_data = PolicyDataSource()
        logger.info("✅ PolicyDataSource 初始化成功")
    except Exception as e:
        logger.error(f"❌ PolicyDataSource 初始化失败: {e}")
        return
    
    # 测试结果统计
    results = {}
    total_data_count = 0
    
    # 1. 测试政策数据获取接口
    logger.info("\n" + "=" * 50)
    logger.info("测试政策数据获取接口")
    logger.info("=" * 50)
    
    # 国务院政策（限制为50条进行测试）
    logger.info("\n1. 测试国务院政策获取...")
    start_time = time.time()
    try:
        gov_policies = policy_data.get_gov_policy(page=1, limit=50)
        elapsed = time.time() - start_time
        results['国务院政策'] = {
            'count': len(gov_policies),
            'time': elapsed,
            'status': 'success' if not gov_policies.empty else 'empty'
        }
        total_data_count += len(gov_policies)
        logger.info(f"✅ 国务院政策: {len(gov_policies)} 条, 耗时: {elapsed:.2f}秒")
        if not gov_policies.empty:
            logger.info(f"   列名: {list(gov_policies.columns)}")
            logger.info(f"   示例标题: {gov_policies['title'].iloc[0][:50]}...")
    except Exception as e:
        results['国务院政策'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 国务院政策获取失败: {e}")
    
    # 发改委政策（限制为50条进行测试）
    logger.info("\n2. 测试发改委政策获取...")
    start_time = time.time()
    try:
        ndrc_policies = policy_data.get_ndrc_policy(page=1, limit=50)
        elapsed = time.time() - start_time
        results['发改委政策'] = {
            'count': len(ndrc_policies),
            'time': elapsed,
            'status': 'success' if not ndrc_policies.empty else 'empty'
        }
        total_data_count += len(ndrc_policies)
        logger.info(f"✅ 发改委政策: {len(ndrc_policies)} 条, 耗时: {elapsed:.2f}秒")
        if not ndrc_policies.empty:
            logger.info(f"   列名: {list(ndrc_policies.columns)}")
            logger.info(f"   示例标题: {ndrc_policies['title'].iloc[0][:50]}...")
    except Exception as e:
        results['发改委政策'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 发改委政策获取失败: {e}")
    
    # 2. 测试新闻数据获取接口
    logger.info("\n" + "=" * 50)
    logger.info("测试新闻数据获取接口")
    logger.info("=" * 50)
    
    news_apis = [
        ('get_financial_breakfast', '财经早餐-东方财富'),
        ('get_global_news_em', '全球财经快讯-东方财富'),
        ('get_global_news_sina', '全球财经快讯-新浪财经'),
        ('get_global_news_futu', '快讯-富途牛牛'),
        ('get_global_news_ths', '全球财经直播-同花顺财经'),
        ('get_global_news_cls', '电报-财联社'),
        ('get_broker_news_sina', '证券原创-新浪财经'),
        ('get_stock_news_em', '个股新闻-东方财富'),
        ('get_news_main_cx', '财经内容精选')
    ]
    
    for i, (method_name, description) in enumerate(news_apis, 3):
        logger.info(f"\n{i}. 测试{description}...")
        start_time = time.time()
        try:
            method = getattr(policy_data, method_name)
            
            # 根据方法调用参数
            if method_name == 'get_global_news_cls':
                df = method(symbol='全部')
            elif method_name == 'get_broker_news_sina':
                df = method(page='1')
            else:
                df = method()
            
            elapsed = time.time() - start_time
            results[description] = {
                'count': len(df),
                'time': elapsed,
                'status': 'success' if not df.empty else 'empty'
            }
            total_data_count += len(df)
            
            logger.info(f"✅ {description}: {len(df)} 条, 耗时: {elapsed:.2f}秒")
            if not df.empty:
                logger.info(f"   列名: {list(df.columns)}")
                if 'source' in df.columns:
                    logger.info(f"   数据源: {df['source'].iloc[0]}")
                if 'title' in df.columns:
                    logger.info(f"   示例标题: {df['title'].iloc[0][:50]}...")
                    
        except Exception as e:
            results[description] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
            logger.error(f"❌ {description}获取失败: {e}")
    
    # 3. 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("测试结果汇总")
    logger.info("=" * 80)
    
    success_count = sum(1 for r in results.values() if r['status'] == 'success')
    empty_count = sum(1 for r in results.values() if r['status'] == 'empty')
    error_count = sum(1 for r in results.values() if r['status'] == 'error')
    total_time = sum(r['time'] for r in results.values())
    
    logger.info(f"📊 接口总数: {len(results)}")
    logger.info(f"✅ 成功获取数据: {success_count}")
    logger.info(f"⚠️ 返回空数据: {empty_count}")
    logger.info(f"❌ 调用失败: {error_count}")
    logger.info(f"📈 总数据量: {total_data_count} 条")
    logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
    
    # 详细结果
    logger.info("\n详细结果:")
    for api_name, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "⚠️" if result['status'] == 'empty' else "❌"
        logger.info(f"{status_icon} {api_name}: {result['count']} 条, {result['time']:.2f}秒")
        if result['status'] == 'error':
            logger.info(f"   错误: {result.get('error', '未知错误')}")
    
    return results

def test_news_policy_fetcher():
    """测试NewsPolicyFetcher类"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试NewsPolicyFetcher类")
    logger.info("=" * 80)
    
    try:
        from engines.news_policy.fetcher import NewsPolicyFetcher
        fetcher = NewsPolicyFetcher()
        logger.info("✅ NewsPolicyFetcher 初始化成功")
        
        # 测试市场新闻获取
        logger.info("\n测试市场新闻获取...")
        start_time = time.time()
        market_news = fetcher.fetch_market_news(days_lookback=1)
        elapsed = time.time() - start_time
        
        logger.info(f"✅ 市场新闻获取: {len(market_news)} 条, 耗时: {elapsed:.2f}秒")
        
        if not market_news.empty:
            logger.info(f"   列名: {list(market_news.columns)}")
            if 'source' in market_news.columns:
                source_counts = market_news['source'].value_counts()
                logger.info("   来源分布:")
                for source, count in source_counts.items():
                    logger.info(f"     {source}: {count} 条")
        
        return len(market_news)
        
    except Exception as e:
        logger.error(f"❌ NewsPolicyFetcher 测试失败: {e}")
        return 0

if __name__ == "__main__":
    logger.info(f"开始新闻政策模块完整测试 - {datetime.now()}")
    
    # 测试PolicyDataSource
    policy_results = test_policy_data_source()
    
    # 测试NewsPolicyFetcher
    fetcher_count = test_news_policy_fetcher()
    
    logger.info("\n" + "=" * 80)
    logger.info("测试完成")
    logger.info("=" * 80)
    logger.info(f"测试结束时间: {datetime.now()}")
    logger.info("详细日志已保存到: news_policy_test.log")
