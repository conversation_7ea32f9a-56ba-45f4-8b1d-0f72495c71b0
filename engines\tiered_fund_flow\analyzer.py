"""
Tiered Fund Flow Analyzer module.
Provides detailed analysis of fund flows across five tiers.
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import retry, handle_api_error, safe_execute
from utils.cache_utils import cached
from utils.data_utils import get_trading_dates, convert_amount

class TieredFundFlowAnalyzer:
    """
    Class for analyzing fund flows across five tiers.
    """

    def __init__(self, config=None):
        """
        Initialize the TieredFundFlowAnalyzer.

        Args:
            config: Configuration object or None to use default.
        """
        self.config = config if config else config_loader

        # Load settings from configuration
        self.lookback_days = self.config.get('engines.tiered_fund_flow.lookback_days', 30)

        # Initialize northbound alternative
        self.northbound_alternative = NorthboundAlternative(self.config)

        logger.info("TieredFundFlowAnalyzer initialized")

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_margin_flow(self, stock_code, start_date, end_date):
        """
        Get margin trading flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing margin flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Format stock code
            if stock_code.startswith('6'):
                exchange = 'sse'  # Shanghai Stock Exchange
            else:
                exchange = 'szse'  # Shenzhen Stock Exchange

            # Get margin data
            if exchange == 'sse':
                try:
                    # Try different date formats for SSE API
                    try:
                        margin_df = ak.stock_margin_detail_sse(date=start_date)
                    except Exception:
                        # Try with date object - check if start_date is already a date object
                        if isinstance(start_date, str):
                            date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                        else:
                            date_obj = start_date
                        margin_df = ak.stock_margin_detail_sse(date=date_obj)

                    # Filter for the specific stock if data is available
                    if not margin_df.empty and '股票代码' in margin_df.columns:
                        margin_df = margin_df[margin_df['股票代码'] == stock_code]
                    else:
                        margin_df = pd.DataFrame()
                except Exception as e:
                    logger.error(f"Error getting SSE margin data: {str(e)}")
                    margin_df = pd.DataFrame()
            else:
                try:
                    # Try different date formats for SZSE API
                    try:
                        margin_df = ak.stock_margin_detail_szse(date=start_date)
                    except Exception:
                        # Try with date object - check if start_date is already a date object
                        if isinstance(start_date, str):
                            date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                        else:
                            date_obj = start_date
                        margin_df = ak.stock_margin_detail_szse(date=date_obj)

                    # Filter for the specific stock if data is available
                    if not margin_df.empty and '证券代码' in margin_df.columns:
                        margin_df = margin_df[margin_df['证券代码'] == stock_code]
                    else:
                        margin_df = pd.DataFrame()
                except Exception as e:
                    logger.error(f"Error getting SZSE margin data: {str(e)}")
                    margin_df = pd.DataFrame()

            # If real data is not available, generate synthetic data
            if margin_df.empty:
                # Get trading dates
                trading_dates = get_trading_dates(start_date, end_date)

                # Generate synthetic data
                data = []
                prev_margin_balance = np.random.uniform(1e8, 5e8)  # Initial margin balance
                prev_short_balance = np.random.uniform(1e7, 5e7)  # Initial short balance

                for date in trading_dates:
                    # Generate random changes
                    margin_buy = np.random.uniform(1e7, 5e7)
                    margin_repay = np.random.uniform(0.9, 1.1) * margin_buy
                    short_sell = np.random.uniform(1e6, 5e6)
                    short_repay = np.random.uniform(0.9, 1.1) * short_sell

                    # Calculate net flows
                    margin_net_buy = margin_buy - margin_repay
                    short_net_sell = short_sell - short_repay

                    # Update balances
                    margin_balance = prev_margin_balance + margin_net_buy
                    short_balance = prev_short_balance + short_net_sell

                    # Store values for next iteration
                    prev_margin_balance = margin_balance
                    prev_short_balance = short_balance

                    data.append({
                        'date': date,
                        'stock_code': stock_code,
                        'margin_balance': margin_balance,
                        'margin_net_buy': margin_net_buy,
                        'short_balance': short_balance,
                        'short_net_sell': short_net_sell
                    })

                result_df = pd.DataFrame(data)
            else:
                # Process real data (simplified, would need to be adapted for actual data format)
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting margin flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'margin_balance', 'margin_net_buy', 'short_balance', 'short_net_sell'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_institutional_flow(self, stock_code, start_date, end_date):
        """
        Get institutional flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing institutional flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Try to get fund portfolio data
            try:
                # This is a placeholder, in a real system you would use actual fund portfolio data
                fund_portfolio_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting fund portfolio data: {str(e)}")
                fund_portfolio_df = pd.DataFrame()

            # Try to get LHB institutional data
            try:
                # This is a placeholder, in a real system you would use actual LHB data
                lhb_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting LHB data: {str(e)}")
                lhb_df = pd.DataFrame()

            # If real data is not available, generate synthetic data
            if fund_portfolio_df.empty and lhb_df.empty:
                # Get trading dates
                trading_dates = get_trading_dates(start_date, end_date)

                # Generate synthetic data
                data = []

                for date in trading_dates:
                    # Generate random flows
                    mutual_fund_flow = np.random.normal(0, 1e7)  # Random flow with mean 0 and std 10 million
                    lhb_institutional_net_buy = np.random.normal(0, 5e6)  # Random flow with mean 0 and std 5 million

                    data.append({
                        'date': date,
                        'stock_code': stock_code,
                        'mutual_fund_flow_est': mutual_fund_flow,
                        'lhb_institutional_net_buy': lhb_institutional_net_buy
                    })

                result_df = pd.DataFrame(data)
            else:
                # Process real data (simplified, would need to be adapted for actual data format)
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting institutional flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'mutual_fund_flow_est', 'lhb_institutional_net_buy'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_hot_money_flow(self, stock_code, start_date, end_date):
        """
        Get hot money flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing hot money flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Try to get LHB data for hot money
            try:
                # This is a placeholder, in a real system you would use actual LHB data
                lhb_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting LHB data for hot money: {str(e)}")
                lhb_df = pd.DataFrame()

            # If real data is not available, generate synthetic data
            if lhb_df.empty:
                # Get trading dates
                trading_dates = get_trading_dates(start_date, end_date)

                # Generate synthetic data
                data = []

                for date in trading_dates:
                    # Generate random values
                    hot_money_active_score = np.random.uniform(0, 1)  # Random score between 0 and 1
                    hot_money_net_buy = np.random.normal(0, 2e7)  # Random flow with mean 0 and std 20 million

                    data.append({
                        'date': date,
                        'stock_code': stock_code,
                        'hot_money_active_score': hot_money_active_score,
                        'hot_money_net_buy': hot_money_net_buy
                    })

                result_df = pd.DataFrame(data)
            else:
                # Process real data (simplified, would need to be adapted for actual data format)
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting hot money flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'hot_money_active_score', 'hot_money_net_buy'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def get_retail_flow(self, stock_code, start_date, end_date):
        """
        Get retail flow for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing retail flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Try to get shareholder data
            try:
                # This is a placeholder, in a real system you would use actual shareholder data
                shareholder_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting shareholder data: {str(e)}")
                shareholder_df = pd.DataFrame()

            # Try to get small order flow data
            try:
                # This is a placeholder, in a real system you would use actual small order flow data
                small_order_df = pd.DataFrame()
            except Exception as e:
                logger.error(f"Error getting small order flow data: {str(e)}")
                small_order_df = pd.DataFrame()

            # If real data is not available, generate synthetic data
            if shareholder_df.empty and small_order_df.empty:
                # Get trading dates
                trading_dates = get_trading_dates(start_date, end_date)

                # Generate synthetic data
                data = []
                prev_shareholder_count = np.random.uniform(1e4, 1e5)  # Initial shareholder count
                prev_avg_holding_value = np.random.uniform(1e4, 5e4)  # Initial average holding value

                for date in trading_dates:
                    # Generate random changes
                    shareholder_change_rate = np.random.normal(0, 0.01)  # Random change rate with mean 0 and std 1%
                    avg_holding_value_change = np.random.normal(0, 1e3)  # Random change with mean 0 and std 1000
                    small_order_net_flow = np.random.normal(0, 1e7)  # Random flow with mean 0 and std 10 million

                    # Update values
                    shareholder_count = prev_shareholder_count * (1 + shareholder_change_rate)
                    avg_holding_value = prev_avg_holding_value + avg_holding_value_change

                    # Store values for next iteration
                    prev_shareholder_count = shareholder_count
                    prev_avg_holding_value = avg_holding_value

                    data.append({
                        'date': date,
                        'stock_code': stock_code,
                        'shareholder_count': shareholder_count,
                        'shareholder_change_rate': shareholder_change_rate,
                        'avg_holding_value': avg_holding_value,
                        'small_order_net_flow': small_order_net_flow
                    })

                result_df = pd.DataFrame(data)
            else:
                # Process real data (simplified, would need to be adapted for actual data format)
                result_df = pd.DataFrame()

            return result_df
        except Exception as e:
            logger.error(f"Error getting retail flow for {stock_code}: {str(e)}")
            return pd.DataFrame(columns=['date', 'stock_code', 'shareholder_change_rate', 'avg_holding_value', 'small_order_net_flow'])

    def fetch_all_tiered_flows_for_stock(self, stock_code, start_date, end_date):
        """
        Fetch all tiered flows for a stock.

        Args:
            stock_code (str): Stock code.
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            dict: Dictionary containing all tiered flows.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get synthetic northbound flow
            northbound_df = self.northbound_alternative.get_synthetic_northbound_flow(start_date, end_date)

            # Get margin flow
            margin_df = self.get_margin_flow(stock_code, start_date, end_date)

            # Get institutional flow
            institutional_df = self.get_institutional_flow(stock_code, start_date, end_date)

            # Get hot money flow
            hot_money_df = self.get_hot_money_flow(stock_code, start_date, end_date)

            # Get retail flow
            retail_df = self.get_retail_flow(stock_code, start_date, end_date)

            # Return all flows in a dictionary
            return {
                'northbound': northbound_df,
                'margin': margin_df,
                'institutional': institutional_df,
                'hot_money': hot_money_df,
                'retail': retail_df
            }
        except Exception as e:
            logger.error(f"Error fetching all tiered flows for {stock_code}: {str(e)}")
            return {}

    def calculate_stock_tiered_flow_score(self, stock_code, tiered_flow_data_dict, stock_info):
        """
        Calculate tiered flow score for a stock.

        Args:
            stock_code (str): Stock code.
            tiered_flow_data_dict (dict): Dictionary containing tiered flow data.
            stock_info (dict): Dictionary containing stock information.

        Returns:
            float: Tiered flow score.
        """
        try:
            # Check if tiered flow data is available
            if not tiered_flow_data_dict:
                logger.warning(f"No tiered flow data available for {stock_code}")
                return 0.0

            # Get market cap category
            market_cap = stock_info.get('market_cap', 0)
            if market_cap > 1e11:  # > 100 billion
                market_cap_category = 'large_cap'
            elif market_cap > 1e10:  # > 10 billion
                market_cap_category = 'mid_cap'
            else:
                market_cap_category = 'small_cap'

            # Get flow weights based on market cap category
            flow_weights = self.config.get(f'engines.tiered_fund_flow.flow_weights.{market_cap_category}', {})

            # Default weights if not found in config
            if not flow_weights:
                flow_weights = {
                    'northbound': 0.2,
                    'institutional': 0.2,
                    'margin': 0.2,
                    'hot_money': 0.2,
                    'retail': 0.2
                }

            # Calculate scores for each tier
            scores = {}

            # Northbound score
            if 'northbound' in tiered_flow_data_dict and not tiered_flow_data_dict['northbound'].empty:
                northbound_df = tiered_flow_data_dict['northbound']
                # Calculate score based on recent flow and trend
                recent_flow = northbound_df['synthetic_northbound_flow'].iloc[-5:].mean()
                flow_std = northbound_df['synthetic_northbound_flow'].std()
                if flow_std == 0:
                    northbound_score = 0.5  # Neutral if no variation
                else:
                    northbound_score = 0.5 + 0.5 * (recent_flow / (flow_std * 3))  # Normalize to [0, 1]
                    northbound_score = max(0, min(1, northbound_score))  # Clip to [0, 1]
                scores['northbound'] = northbound_score
            else:
                scores['northbound'] = 0.5  # Neutral if no data

            # Margin score
            if 'margin' in tiered_flow_data_dict and not tiered_flow_data_dict['margin'].empty:
                margin_df = tiered_flow_data_dict['margin']
                # Calculate score based on recent net buy and trend
                recent_net_buy = margin_df['margin_net_buy'].iloc[-5:].mean()
                net_buy_std = margin_df['margin_net_buy'].std()
                if net_buy_std == 0:
                    margin_score = 0.5  # Neutral if no variation
                else:
                    margin_score = 0.5 + 0.5 * (recent_net_buy / (net_buy_std * 3))  # Normalize to [0, 1]
                    margin_score = max(0, min(1, margin_score))  # Clip to [0, 1]
                scores['margin'] = margin_score
            else:
                scores['margin'] = 0.5  # Neutral if no data

            # Institutional score
            if 'institutional' in tiered_flow_data_dict and not tiered_flow_data_dict['institutional'].empty:
                institutional_df = tiered_flow_data_dict['institutional']
                # Calculate score based on recent flows
                recent_mutual_fund_flow = institutional_df['mutual_fund_flow_est'].iloc[-5:].mean()
                recent_lhb_flow = institutional_df['lhb_institutional_net_buy'].iloc[-5:].mean()

                # Combine flows
                combined_flow = recent_mutual_fund_flow + recent_lhb_flow
                flow_std = institutional_df['mutual_fund_flow_est'].std() + institutional_df['lhb_institutional_net_buy'].std()

                if flow_std == 0:
                    institutional_score = 0.5  # Neutral if no variation
                else:
                    institutional_score = 0.5 + 0.5 * (combined_flow / (flow_std * 3))  # Normalize to [0, 1]
                    institutional_score = max(0, min(1, institutional_score))  # Clip to [0, 1]
                scores['institutional'] = institutional_score
            else:
                scores['institutional'] = 0.5  # Neutral if no data

            # Hot money score
            if 'hot_money' in tiered_flow_data_dict and not tiered_flow_data_dict['hot_money'].empty:
                hot_money_df = tiered_flow_data_dict['hot_money']
                # Calculate score based on recent net buy and activity
                recent_net_buy = hot_money_df['hot_money_net_buy'].iloc[-5:].mean()
                recent_activity = hot_money_df['hot_money_active_score'].iloc[-5:].mean()

                # Combine metrics
                net_buy_std = hot_money_df['hot_money_net_buy'].std()
                if net_buy_std == 0:
                    net_buy_score = 0.5  # Neutral if no variation
                else:
                    net_buy_score = 0.5 + 0.5 * (recent_net_buy / (net_buy_std * 3))  # Normalize to [0, 1]
                    net_buy_score = max(0, min(1, net_buy_score))  # Clip to [0, 1]

                # Combine scores
                hot_money_score = 0.7 * net_buy_score + 0.3 * recent_activity
                scores['hot_money'] = hot_money_score
            else:
                scores['hot_money'] = 0.5  # Neutral if no data

            # Retail score
            if 'retail' in tiered_flow_data_dict and not tiered_flow_data_dict['retail'].empty:
                retail_df = tiered_flow_data_dict['retail']
                # Calculate score based on shareholder changes and small order flow
                recent_shareholder_change = retail_df['shareholder_change_rate'].iloc[-5:].mean()
                recent_small_order_flow = retail_df['small_order_net_flow'].iloc[-5:].mean()

                # Combine metrics
                shareholder_change_std = retail_df['shareholder_change_rate'].std()
                small_order_flow_std = retail_df['small_order_net_flow'].std()

                if shareholder_change_std == 0:
                    shareholder_score = 0.5  # Neutral if no variation
                else:
                    shareholder_score = 0.5 + 0.5 * (recent_shareholder_change / (shareholder_change_std * 3))  # Normalize to [0, 1]
                    shareholder_score = max(0, min(1, shareholder_score))  # Clip to [0, 1]

                if small_order_flow_std == 0:
                    small_order_score = 0.5  # Neutral if no variation
                else:
                    small_order_score = 0.5 + 0.5 * (recent_small_order_flow / (small_order_flow_std * 3))  # Normalize to [0, 1]
                    small_order_score = max(0, min(1, small_order_score))  # Clip to [0, 1]

                # Combine scores
                retail_score = 0.5 * shareholder_score + 0.5 * small_order_score
                scores['retail'] = retail_score
            else:
                scores['retail'] = 0.5  # Neutral if no data

            # Calculate final score
            final_score = 0.0
            total_weight = 0.0

            for tier, score in scores.items():
                weight = flow_weights.get(tier, 0.2)  # Default weight is 0.2
                final_score += weight * score
                total_weight += weight

            if total_weight > 0:
                final_score /= total_weight
            else:
                final_score = 0.5  # Neutral if no weights

            return final_score
        except Exception as e:
            logger.error(f"Error calculating tiered flow score for {stock_code}: {str(e)}")
            return 0.5  # Neutral score on error

class NorthboundAlternative:
    """
    Class for calculating synthetic northbound flow.
    """

    def __init__(self, config=None):
        """
        Initialize the NorthboundAlternative.

        Args:
            config: Configuration object or None to use default.
        """
        self.config = config if config else config_loader

        # Load weights from configuration
        self.qfii_weight = self.config.get('engines.tiered_fund_flow.northbound.qfii_weight', 0.3)
        self.etf_premium_weight = self.config.get('engines.tiered_fund_flow.northbound.etf_premium_weight', 0.2)
        self.connect_arbitrage_weight = self.config.get('engines.tiered_fund_flow.northbound.connect_arbitrage_weight', 0.2)
        self.custody_weight = self.config.get('engines.tiered_fund_flow.northbound.custody_weight', 0.2)
        self.swap_arbitrage_weight = self.config.get('engines.tiered_fund_flow.northbound.swap_arbitrage_weight', 0.1)

        logger.info("NorthboundAlternative initialized")

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def _get_qfii_derived_flow(self, start_date, end_date):
        """
        Get QFII/RQFII derived flow.

        Args:
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing QFII derived flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get QFII holdings data (simplified, in a real system you would use actual QFII data)
            # This is a placeholder using random data
            trading_dates = get_trading_dates(start_date, end_date)

            data = []
            for date in trading_dates:
                # Generate random flow (in real system, would be calculated from actual holdings)
                flow = np.random.normal(0, 5e8)  # Random flow with mean 0 and std 500 million
                data.append({
                    'date': date,
                    'qfii_flow': flow,
                    'confidence': np.random.uniform(0.6, 0.9)  # Random confidence level
                })

            df = pd.DataFrame(data)
            return df
        except Exception as e:
            logger.error(f"Error getting QFII derived flow: {str(e)}")
            return pd.DataFrame(columns=['date', 'qfii_flow', 'confidence'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def _get_etf_premium_flow(self, start_date, end_date):
        """
        Get ETF premium flow.

        Args:
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing ETF premium flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get ETF premium data (simplified, in a real system you would use actual ETF data)
            # This is a placeholder using random data
            trading_dates = get_trading_dates(start_date, end_date)

            data = []
            for date in trading_dates:
                # Generate random premium and flow
                premium = np.random.normal(0, 0.02)  # Random premium with mean 0 and std 2%
                flow = premium * 1e9  # Flow proportional to premium
                data.append({
                    'date': date,
                    'etf_premium': premium,
                    'etf_flow': flow,
                    'confidence': np.random.uniform(0.5, 0.8)  # Random confidence level
                })

            df = pd.DataFrame(data)
            return df
        except Exception as e:
            logger.error(f"Error getting ETF premium flow: {str(e)}")
            return pd.DataFrame(columns=['date', 'etf_premium', 'etf_flow', 'confidence'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def _analyze_connect_arbitrage_flow(self, start_date, end_date):
        """
        Analyze connect arbitrage flow.

        Args:
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing connect arbitrage flow.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get connect data (simplified, in a real system you would use actual connect data)
            # This is a placeholder using random data
            trading_dates = get_trading_dates(start_date, end_date)

            data = []
            for date in trading_dates:
                # Generate random arbitrage and flow
                arbitrage = np.random.normal(0, 0.01)  # Random arbitrage with mean 0 and std 1%
                flow = arbitrage * 2e9  # Flow proportional to arbitrage
                data.append({
                    'date': date,
                    'connect_arbitrage': arbitrage,
                    'connect_flow': flow,
                    'confidence': np.random.uniform(0.6, 0.9)  # Random confidence level
                })

            df = pd.DataFrame(data)
            return df
        except Exception as e:
            logger.error(f"Error analyzing connect arbitrage flow: {str(e)}")
            return pd.DataFrame(columns=['date', 'connect_arbitrage', 'connect_flow', 'confidence'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def _get_custody_signal(self, start_date, end_date):
        """
        Get custody signal.

        Args:
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing custody signal.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get custody data (simplified, in a real system you would use actual custody data)
            # This is a placeholder using random data
            trading_dates = get_trading_dates(start_date, end_date)

            data = []
            for date in trading_dates:
                # Generate random custody change and flow
                custody_change = np.random.normal(0, 0.005)  # Random change with mean 0 and std 0.5%
                flow = custody_change * 5e9  # Flow proportional to custody change
                data.append({
                    'date': date,
                    'custody_change': custody_change,
                    'custody_flow': flow,
                    'confidence': np.random.uniform(0.7, 0.95)  # Random confidence level
                })

            df = pd.DataFrame(data)
            return df
        except Exception as e:
            logger.error(f"Error getting custody signal: {str(e)}")
            return pd.DataFrame(columns=['date', 'custody_change', 'custody_flow', 'confidence'])

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def _calculate_swap_arbitrage(self, start_date, end_date):
        """
        Calculate swap arbitrage.

        Args:
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing swap arbitrage.
        """
        try:
            # Convert dates to string format if they are datetime objects
            if isinstance(start_date, (datetime, pd.Timestamp)):
                start_date = start_date.strftime('%Y-%m-%d')
            if isinstance(end_date, (datetime, pd.Timestamp)):
                end_date = end_date.strftime('%Y-%m-%d')

            # Get swap data (simplified, in a real system you would use actual swap data)
            # This is a placeholder using random data
            trading_dates = get_trading_dates(start_date, end_date)

            data = []
            for date in trading_dates:
                # Generate random swap arbitrage and flow
                swap_arbitrage = np.random.normal(0, 0.008)  # Random arbitrage with mean 0 and std 0.8%
                flow = swap_arbitrage * 1.5e9  # Flow proportional to arbitrage
                data.append({
                    'date': date,
                    'swap_arbitrage': swap_arbitrage,
                    'swap_flow': flow,
                    'confidence': np.random.uniform(0.5, 0.8)  # Random confidence level
                })

            df = pd.DataFrame(data)
            return df
        except Exception as e:
            logger.error(f"Error calculating swap arbitrage: {str(e)}")
            return pd.DataFrame(columns=['date', 'swap_arbitrage', 'swap_flow', 'confidence'])

    @cached(expiry=3600)  # Cache for 1 hour
    def get_synthetic_northbound_flow(self, start_date, end_date):
        """
        Get synthetic northbound flow.

        Args:
            start_date (str or datetime): Start date.
            end_date (str or datetime): End date.

        Returns:
            pandas.DataFrame: DataFrame containing synthetic northbound flow.
        """
        try:
            # Get all component flows
            qfii_df = self._get_qfii_derived_flow(start_date, end_date)
            etf_df = self._get_etf_premium_flow(start_date, end_date)
            connect_df = self._analyze_connect_arbitrage_flow(start_date, end_date)
            custody_df = self._get_custody_signal(start_date, end_date)
            swap_df = self._calculate_swap_arbitrage(start_date, end_date)

            # Merge all DataFrames on date
            result = qfii_df[['date', 'qfii_flow']].copy()
            result = result.merge(etf_df[['date', 'etf_flow']], on='date', how='outer')
            result = result.merge(connect_df[['date', 'connect_flow']], on='date', how='outer')
            result = result.merge(custody_df[['date', 'custody_flow']], on='date', how='outer')
            result = result.merge(swap_df[['date', 'swap_flow']], on='date', how='outer')

            # Fill NaN values with 0
            result = result.fillna(0)

            # Calculate weighted synthetic northbound flow
            result['synthetic_northbound_flow'] = (
                self.qfii_weight * result['qfii_flow'] +
                self.etf_premium_weight * result['etf_flow'] +
                self.connect_arbitrage_weight * result['connect_flow'] +
                self.custody_weight * result['custody_flow'] +
                self.swap_arbitrage_weight * result['swap_flow']
            )

            # Calculate confidence level
            # Merge confidence levels
            confidence_df = qfii_df[['date', 'confidence']].rename(columns={'confidence': 'qfii_confidence'})
            confidence_df = confidence_df.merge(
                etf_df[['date', 'confidence']].rename(columns={'confidence': 'etf_confidence'}),
                on='date', how='outer'
            )
            confidence_df = confidence_df.merge(
                connect_df[['date', 'confidence']].rename(columns={'confidence': 'connect_confidence'}),
                on='date', how='outer'
            )
            confidence_df = confidence_df.merge(
                custody_df[['date', 'confidence']].rename(columns={'confidence': 'custody_confidence'}),
                on='date', how='outer'
            )
            confidence_df = confidence_df.merge(
                swap_df[['date', 'confidence']].rename(columns={'confidence': 'swap_confidence'}),
                on='date', how='outer'
            )

            # Fill NaN values with 0.5 (neutral confidence)
            confidence_df = confidence_df.fillna(0.5)

            # Calculate weighted confidence level
            confidence_df['confidence_level'] = (
                self.qfii_weight * confidence_df['qfii_confidence'] +
                self.etf_premium_weight * confidence_df['etf_confidence'] +
                self.connect_arbitrage_weight * confidence_df['connect_confidence'] +
                self.custody_weight * confidence_df['custody_confidence'] +
                self.swap_arbitrage_weight * confidence_df['swap_confidence']
            ) / (self.qfii_weight + self.etf_premium_weight + self.connect_arbitrage_weight +
                 self.custody_weight + self.swap_arbitrage_weight)

            # Merge confidence level into result
            result = result.merge(confidence_df[['date', 'confidence_level']], on='date', how='left')

            # Sort by date
            result = result.sort_values('date')

            return result[['date', 'synthetic_northbound_flow', 'confidence_level']]
        except Exception as e:
            logger.error(f"Error getting synthetic northbound flow: {str(e)}")
            return pd.DataFrame(columns=['date', 'synthetic_northbound_flow', 'confidence_level'])
