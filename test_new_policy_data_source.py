"""
测试重写后的PolicyDataSource类
验证刷新频率控制和去重功能
"""

import pandas as pd
import time
from datetime import datetime
import logging

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('new_policy_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_new_policy_data_source():
    """测试重写后的PolicyDataSource类"""
    
    logger.info("=" * 80)
    logger.info("测试重写后的PolicyDataSource类")
    logger.info("=" * 80)
    
    try:
        from data_sources.policy_data import PolicyDataSource
        policy_data = PolicyDataSource()
        logger.info("✅ 新版PolicyDataSource 初始化成功")
    except Exception as e:
        logger.error(f"❌ PolicyDataSource 初始化失败: {e}")
        return
    
    # 测试结果统计
    results = {}
    total_data_count = 0
    
    # 1. 测试财经早餐（每次都获取）
    logger.info("\n" + "=" * 50)
    logger.info("测试财经早餐（每次都获取）")
    logger.info("=" * 50)
    
    start_time = time.time()
    try:
        breakfast = policy_data.get_financial_breakfast()
        elapsed = time.time() - start_time
        results['财经早餐'] = {
            'count': len(breakfast),
            'time': elapsed,
            'status': 'success' if not breakfast.empty else 'empty'
        }
        total_data_count += len(breakfast)
        logger.info(f"✅ 财经早餐: {len(breakfast)} 条, 耗时: {elapsed:.2f}秒")
        if not breakfast.empty:
            logger.info(f"   列名: {list(breakfast.columns)}")
            logger.info(f"   示例标题: {breakfast['title'].iloc[0][:50]}...")
    except Exception as e:
        results['财经早餐'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 财经早餐获取失败: {e}")
    
    # 2. 测试4小时刷新的接口
    logger.info("\n" + "=" * 50)
    logger.info("测试4小时刷新接口")
    logger.info("=" * 50)
    
    start_time = time.time()
    try:
        global_em = policy_data.get_global_news_em()
        elapsed = time.time() - start_time
        results['全球财经快讯-东方财富'] = {
            'count': len(global_em),
            'time': elapsed,
            'status': 'success' if not global_em.empty else 'empty'
        }
        total_data_count += len(global_em)
        logger.info(f"✅ 全球财经快讯-东方财富: {len(global_em)} 条, 耗时: {elapsed:.2f}秒")
        if not global_em.empty:
            logger.info(f"   示例标题: {global_em['title'].iloc[0][:50]}...")
    except Exception as e:
        results['全球财经快讯-东方财富'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 全球财经快讯-东方财富获取失败: {e}")
    
    # 3. 测试15分钟刷新的接口
    logger.info("\n" + "=" * 50)
    logger.info("测试15分钟刷新接口")
    logger.info("=" * 50)
    
    apis_15min = [
        ('get_global_news_sina', '新浪财经'),
        ('get_global_news_futu', '富途牛牛'),
        ('get_global_news_ths', '同花顺财经'),
        ('get_global_news_cls', '财联社')
    ]
    
    for method_name, description in apis_15min:
        start_time = time.time()
        try:
            method = getattr(policy_data, method_name)
            if method_name == 'get_global_news_cls':
                df = method(symbol='全部')
            else:
                df = method()
            
            elapsed = time.time() - start_time
            results[description] = {
                'count': len(df),
                'time': elapsed,
                'status': 'success' if not df.empty else 'empty'
            }
            total_data_count += len(df)
            
            logger.info(f"✅ {description}: {len(df)} 条, 耗时: {elapsed:.2f}秒")
            if not df.empty:
                logger.info(f"   示例标题: {df['title'].iloc[0][:50]}...")
                    
        except Exception as e:
            results[description] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
            logger.error(f"❌ {description}获取失败: {e}")
    
    # 4. 测试统一获取接口
    logger.info("\n" + "=" * 50)
    logger.info("测试统一获取接口")
    logger.info("=" * 50)
    
    start_time = time.time()
    try:
        all_news = policy_data.get_all_news_with_refresh_control()
        elapsed = time.time() - start_time
        logger.info(f"✅ 统一获取: {len(all_news)} 条, 耗时: {elapsed:.2f}秒")
        
        if not all_news.empty:
            source_counts = all_news['source'].value_counts()
            logger.info("   来源分布:")
            for source, count in source_counts.items():
                logger.info(f"     {source}: {count} 条")
    except Exception as e:
        logger.error(f"❌ 统一获取失败: {e}")
    
    # 5. 测试第二次调用（验证刷新控制）
    logger.info("\n" + "=" * 50)
    logger.info("测试第二次调用（验证刷新控制）")
    logger.info("=" * 50)
    
    logger.info("立即再次调用15分钟刷新的接口，应该被跳过...")
    try:
        sina_2nd = policy_data.get_global_news_sina()
        logger.info(f"新浪财经第二次调用: {len(sina_2nd)} 条（应该为0）")
        
        futu_2nd = policy_data.get_global_news_futu()
        logger.info(f"富途牛牛第二次调用: {len(futu_2nd)} 条（应该为0）")
    except Exception as e:
        logger.error(f"第二次调用测试失败: {e}")
    
    # 6. 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("测试结果汇总")
    logger.info("=" * 80)
    
    success_count = sum(1 for r in results.values() if r['status'] == 'success')
    empty_count = sum(1 for r in results.values() if r['status'] == 'empty')
    error_count = sum(1 for r in results.values() if r['status'] == 'error')
    total_time = sum(r['time'] for r in results.values())
    
    logger.info(f"📊 接口总数: {len(results)}")
    logger.info(f"✅ 成功获取数据: {success_count}")
    logger.info(f"⚠️ 返回空数据: {empty_count}")
    logger.info(f"❌ 调用失败: {error_count}")
    logger.info(f"📈 总数据量: {total_data_count} 条")
    logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
    
    # 详细结果
    logger.info("\n详细结果:")
    for api_name, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "⚠️" if result['status'] == 'empty' else "❌"
        logger.info(f"{status_icon} {api_name}: {result['count']} 条, {result['time']:.2f}秒")
        if result['status'] == 'error':
            logger.info(f"   错误: {result.get('error', '未知错误')}")
    
    return results

if __name__ == "__main__":
    logger.info(f"开始新版新闻政策模块测试 - {datetime.now()}")
    
    # 测试新版PolicyDataSource
    results = test_new_policy_data_source()
    
    logger.info("\n" + "=" * 80)
    logger.info("测试完成")
    logger.info("=" * 80)
    logger.info(f"测试结束时间: {datetime.now()}")
    logger.info("详细日志已保存到: new_policy_test.log")
    
    # 显示缓存文件信息
    import os
    cache_dir = "data/news_cache"
    if os.path.exists(cache_dir):
        cache_files = os.listdir(cache_dir)
        logger.info(f"\n缓存文件: {len(cache_files)} 个")
        for file in cache_files:
            logger.info(f"  - {file}")
    
    fetch_times_file = "data/last_fetch_times.json"
    if os.path.exists(fetch_times_file):
        logger.info(f"\n刷新时间记录文件已创建: {fetch_times_file}")
