#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置调试测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_loading():
    """测试配置加载"""
    print("=" * 50)
    print("配置加载调试测试")
    print("=" * 50)
    
    try:
        from utils.config_loader import config_loader
        
        # 获取完整配置
        config = config_loader.get_config()
        print("✅ 配置文件加载成功")
        
        # 检查engines部分
        print(f"\n📋 完整配置结构:")
        print(f"配置根键: {list(config.keys())}")
        
        if 'engines' in config:
            print(f"engines键存在: {list(config['engines'].keys())}")
            
            if 'sentiment' in config['engines']:
                print(f"sentiment配置: {config['engines']['sentiment']}")
            else:
                print("❌ sentiment配置不存在")
        else:
            print("❌ engines配置不存在")
        
        # 测试嵌套键获取
        print(f"\n🔍 嵌套键测试:")
        sentiment_path = config_loader.get('engines.sentiment.model_path')
        print(f"engines.sentiment.model_path = {sentiment_path}")
        
        # 测试默认值
        sentiment_path_with_default = config_loader.get('engines.sentiment.model_path', 'models/sentiment')
        print(f"engines.sentiment.model_path (with default) = {sentiment_path_with_default}")
        
        # 检查模型文件是否存在
        if sentiment_path:
            if os.path.exists(sentiment_path):
                files = os.listdir(sentiment_path)
                print(f"✅ 模型目录存在: {sentiment_path}")
                print(f"模型文件: {files}")
            else:
                print(f"❌ 模型目录不存在: {sentiment_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_sentiment_analyzer_init():
    """测试情绪分析器初始化"""
    print("\n" + "=" * 50)
    print("情绪分析器初始化测试")
    print("=" * 50)
    
    try:
        from engines.sentiment.analyzer import SentimentAnalyzer
        
        print("🔧 初始化情绪分析器...")
        analyzer = SentimentAnalyzer()
        
        print(f"✅ 情绪分析器初始化成功")
        print(f"模型路径: {analyzer.model_path}")
        print(f"模型对象: {type(analyzer.model)}")
        print(f"分词器对象: {type(analyzer.tokenizer)}")
        print(f"管道对象: {type(analyzer.sentiment_pipeline)}")
        
        # 测试分析功能
        test_text = "央行降准释放流动性，利好股市"
        print(f"\n📝 测试文本: {test_text}")
        
        result = analyzer.analyze_text_sentiment(test_text)
        print(f"情绪分析结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 情绪分析器初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 配置和情绪分析器调试测试")
    print("=" * 60)
    
    # 测试配置加载
    config_ok = test_config_loading()
    
    # 测试情绪分析器
    if config_ok:
        sentiment_ok = test_sentiment_analyzer_init()
    else:
        sentiment_ok = False
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print(f"配置加载: {'✅ 成功' if config_ok else '❌ 失败'}")
    print(f"情绪分析器: {'✅ 成功' if sentiment_ok else '❌ 失败'}")
    
    if config_ok and sentiment_ok:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 存在问题需要修复")

if __name__ == "__main__":
    main()
