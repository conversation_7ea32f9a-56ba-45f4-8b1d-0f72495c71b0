"""
系统操作面板 - 重构版
基于核心模块的图形化操作界面
"""

import os
import sys
import json
import time
import logging
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
from datetime import datetime, timedelta
import threading
import queue
import subprocess

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/operation_panel_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
    ]
)
logger = logging.getLogger("SystemOperationPanel")

class SystemOperationPanel:
    """系统操作面板 - 重构版"""

    def __init__(self):
        """初始化系统操作面板"""
        self.root = tk.Tk()
        self.root.title("政策-流动性-波动率套利系统 - 操作面板")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)

        # 创建任务队列
        self.task_queue = queue.Queue()
        
        # 创建线程事件
        self.stop_event = threading.Event()
        
        # 创建工作线程
        self.worker_thread = None

        # 创建UI组件
        self.create_widgets()

        # 启动工作线程
        self.start_worker_thread()

    def create_widgets(self):
        """创建UI组件"""
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建顶部框架
        self.top_frame = ttk.Frame(self.main_frame)
        self.top_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建标题标签
        title_label = ttk.Label(self.top_frame, text="政策-流动性-波动率套利系统", font=("Arial", 16, "bold"))
        title_label.pack(side=tk.LEFT, padx=5)

        # 创建状态标签
        self.status_label = ttk.Label(self.top_frame, text="🟢 就绪", font=("Arial", 10))
        self.status_label.pack(side=tk.RIGHT, padx=5)

        # 创建内容框架
        self.content_frame = ttk.Frame(self.main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        # 创建左侧操作框架
        self.operation_frame = ttk.LabelFrame(self.content_frame, text="系统操作", padding="10")
        self.operation_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 核心分析按钮
        ttk.Button(self.operation_frame, text="🚀 运行核心分析", 
                  command=self.run_core_analysis, width=20).pack(pady=5)
        
        # 模块测试按钮
        ttk.Button(self.operation_frame, text="🧪 运行模块测试", 
                  command=self.run_module_tests, width=20).pack(pady=5)
        
        # 数据收集按钮
        ttk.Button(self.operation_frame, text="📊 数据收集", 
                  command=self.run_data_collection, width=20).pack(pady=5)
        
        # 分隔线
        ttk.Separator(self.operation_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # 单模块测试
        ttk.Label(self.operation_frame, text="单模块测试:", font=("Arial", 10, "bold")).pack(pady=(5, 2))
        
        ttk.Button(self.operation_frame, text="FinBERT测试", 
                  command=lambda: self.run_single_test("test_finbert_integration.py"), width=20).pack(pady=2)
        
        ttk.Button(self.operation_frame, text="情绪共振测试", 
                  command=lambda: self.run_single_test("test_sentiment_resonance_model.py"), width=20).pack(pady=2)
        
        ttk.Button(self.operation_frame, text="资金流测试", 
                  command=lambda: self.run_single_test("test_real_api_fund_flow.py"), width=20).pack(pady=2)
        
        ttk.Button(self.operation_frame, text="波动率测试", 
                  command=lambda: self.run_single_test("test_enhanced_volatility.py"), width=20).pack(pady=2)
        
        ttk.Button(self.operation_frame, text="决策引擎测试", 
                  command=lambda: self.run_single_test("test_core_decision_engine.py"), width=20).pack(pady=2)

        # 分隔线
        ttk.Separator(self.operation_frame, orient='horizontal').pack(fill=tk.X, pady=10)
        
        # 系统控制
        ttk.Label(self.operation_frame, text="系统控制:", font=("Arial", 10, "bold")).pack(pady=(5, 2))
        
        ttk.Button(self.operation_frame, text="🛑 停止所有任务", 
                  command=self.stop_all_tasks, width=20).pack(pady=2)
        
        ttk.Button(self.operation_frame, text="🗑️ 清空日志", 
                  command=self.clear_log, width=20).pack(pady=2)

        # 创建右侧日志框架
        self.log_frame = ttk.LabelFrame(self.content_frame, text="执行日志", padding="10")
        self.log_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(self.log_frame, wrap=tk.WORD, font=("Consolas", 9))
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.config(state=tk.DISABLED)

        # 创建底部框架
        self.bottom_frame = ttk.Frame(self.main_frame)
        self.bottom_frame.pack(fill=tk.X, pady=(10, 0))

        # 创建版本标签
        version_label = ttk.Label(self.bottom_frame, text="版本: 2.0.0 重构版")
        version_label.pack(side=tk.LEFT, padx=5)

        # 创建退出按钮
        ttk.Button(self.bottom_frame, text="退出", command=self.exit_application).pack(side=tk.RIGHT, padx=5)

    def start_worker_thread(self):
        """启动工作线程"""
        if self.worker_thread is None or not self.worker_thread.is_alive():
            self.stop_event.clear()
            self.worker_thread = threading.Thread(target=self.worker_loop, daemon=True)
            self.worker_thread.start()
            logger.info("工作线程已启动")

    def worker_loop(self):
        """工作线程循环"""
        while not self.stop_event.is_set():
            try:
                # 尝试从队列获取任务，超时1秒
                task = self.task_queue.get(timeout=1)
                
                # 执行任务
                self.execute_task(task)
                
                # 标记任务完成
                self.task_queue.task_done()
            except queue.Empty:
                # 队列为空，继续等待
                pass
            except Exception as e:
                logger.error(f"工作线程异常: {str(e)}")
                self.log_message(f"❌ 错误: {str(e)}")

    def execute_task(self, task):
        """执行任务"""
        task_type = task.get('type')
        command = task.get('command')
        description = task.get('description', '未知任务')

        self.log_message(f"🚀 开始执行: {description}")
        self.update_status(f"🔄 正在执行: {description}")

        try:
            if task_type == 'python_script':
                # 执行Python脚本
                result = subprocess.run([sys.executable, command], 
                                      capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    self.log_message(f"✅ {description} 执行成功")
                    if result.stdout:
                        self.log_message(f"输出: {result.stdout}")
                else:
                    self.log_message(f"❌ {description} 执行失败")
                    if result.stderr:
                        self.log_message(f"错误: {result.stderr}")
                        
            elif task_type == 'python_module':
                # 执行Python模块
                self.log_message(f"📦 导入并执行模块: {command}")
                # 这里可以添加模块导入和执行逻辑
                
        except subprocess.TimeoutExpired:
            self.log_message(f"⏰ {description} 执行超时")
        except Exception as e:
            self.log_message(f"❌ {description} 执行异常: {str(e)}")

        self.update_status("🟢 就绪")

    def run_core_analysis(self):
        """运行核心分析系统"""
        task = {
            'type': 'python_script',
            'command': 'main_new.py',
            'description': '核心分析系统'
        }
        self.task_queue.put(task)

    def run_module_tests(self):
        """运行所有模块测试"""
        tests = [
            ("test_finbert_integration.py", "FinBERT集成测试"),
            ("test_sentiment_resonance_model.py", "情绪共振模型测试"),
            ("test_real_api_fund_flow.py", "资金流分析测试"),
            ("test_enhanced_volatility.py", "波动率分析测试"),
            ("test_core_decision_engine.py", "核心决策引擎测试")
        ]
        
        for test_file, test_name in tests:
            task = {
                'type': 'python_script',
                'command': test_file,
                'description': test_name
            }
            self.task_queue.put(task)

    def run_data_collection(self):
        """运行数据收集"""
        task = {
            'type': 'python_module',
            'command': 'data_collection',
            'description': '数据收集'
        }
        self.task_queue.put(task)

    def run_single_test(self, test_file):
        """运行单个测试"""
        task = {
            'type': 'python_script',
            'command': test_file,
            'description': f'单模块测试: {test_file}'
        }
        self.task_queue.put(task)

    def stop_all_tasks(self):
        """停止所有任务"""
        self.stop_event.set()
        # 清空任务队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
                self.task_queue.task_done()
            except queue.Empty:
                break
        
        self.log_message("🛑 所有任务已停止")
        self.update_status("🟡 已停止")
        
        # 重新启动工作线程
        self.start_worker_thread()

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def log_message(self, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # 在UI中显示
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 记录到日志文件
        logger.info(message)

    def update_status(self, status):
        """更新状态标签"""
        self.status_label.config(text=status)
        self.root.update_idletasks()

    def exit_application(self):
        """退出应用程序"""
        self.stop_event.set()
        self.root.quit()
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        self.log_message("🎯 政策-流动性-波动率套利系统操作面板已启动")
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = SystemOperationPanel()
        app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        logger.error(f"应用程序启动失败: {e}")

if __name__ == "__main__":
    main()
