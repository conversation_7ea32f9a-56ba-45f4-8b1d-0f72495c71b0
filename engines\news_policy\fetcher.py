"""
News and Policy Fetcher module.
Responsible for fetching news and policy information from various sources.
"""

import os
import re
import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

# Real imports for web scraping and RSS
try:
    import requests
    from bs4 import BeautifulSoup
    import feedparser
    SCRAPING_AVAILABLE = True
except ImportError:
    logger.warning("Web scraping libraries not available. Some features will be limited.")
    SCRAPING_AVAILABLE = False

    # Fallback classes
    class BeautifulSoup:
        def __init__(self, text, parser):
            self.text = text
        def find_all(self, tag, **kwargs):
            return []

    class feedparser:
        @staticmethod
        def parse(url):
            class Feed:
                def __init__(self):
                    self.feed = type('FeedInfo', (), {'title': 'Mock Feed'})
                    self.entries = []
            return Feed()

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import retry, handle_api_error, safe_execute
from utils.cache_utils import cached
from utils.network_utils import make_request, fetch_all_async

class NewsPolicyFetcher:
    """
    Class for fetching news and policy information.
    """

    def __init__(self, config=None):
        """
        Initialize the NewsPolicyFetcher.

        Args:
            config: Configuration object or None to use default.
        """
        self.config = config if config else config_loader

        # Load sources from configuration
        self.policy_sources = self.config.get('engines.news_policy.sources.policy', [])
        self.news_sources = self.config.get('engines.news_policy.sources.news', [])
        self.rss_feeds = self.config.get('engines.news_policy.rss_feeds', [])

        # Default lookback days
        self.default_lookback_days = self.config.get('engines.news_policy.lookback_days', 7)

        logger.info(f"NewsPolicyFetcher initialized with {len(self.policy_sources)} policy sources, "
                   f"{len(self.news_sources)} news sources, and {len(self.rss_feeds)} RSS feeds")

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def fetch_akshare_news(self, news_type='stock_news_em', days_lookback=None):
        """
        Fetch news using AKShare APIs.

        Args:
            news_type (str): Type of news to fetch.
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched news.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        try:
            if news_type == 'stock_news_em':
                # 获取东方财富股票新闻
                df = ak.stock_news_em()
                if not df.empty:
                    # 重命名列以统一格式
                    df = df.rename(columns={
                        '新闻标题': 'title',
                        '新闻内容': 'content',
                        '发布时间': 'publish_date',
                        '新闻链接': 'url'
                    })
                    df['source'] = '东方财富'
                    df['doc_type'] = 'news'

            elif news_type == 'stock_news_main_cx':
                # 获取财经内容精选
                df = ak.stock_news_main_cx()
                if not df.empty:
                    df = df.rename(columns={
                        '标题': 'title',
                        '内容': 'content',
                        '时间': 'publish_date',
                        '链接': 'url'
                    })
                    df['source'] = '财经精选'
                    df['doc_type'] = 'news'

            elif news_type == 'news_cctv':
                # 获取央视新闻
                df = ak.news_cctv()
                if not df.empty:
                    df = df.rename(columns={
                        '标题': 'title',
                        '内容': 'content',
                        '时间': 'publish_date',
                        '链接': 'url'
                    })
                    df['source'] = '央视新闻'
                    df['doc_type'] = 'news'
            else:
                logger.warning(f"Unknown news type: {news_type}")
                return pd.DataFrame()

            if df.empty:
                return pd.DataFrame()

            # 处理日期格式
            if 'publish_date' in df.columns:
                df['publish_date'] = pd.to_datetime(df['publish_date'], errors='coerce').dt.date

                # 过滤日期
                cutoff_date = datetime.now().date() - timedelta(days=days_lookback)
                df = df[df['publish_date'] >= cutoff_date]

            logger.info(f"Successfully fetched {len(df)} news items from {news_type}")
            return df

        except Exception as e:
            logger.error(f"Error fetching news from AKShare {news_type}: {str(e)}")
            return pd.DataFrame()

    @cached(expiry=3600)  # Cache for 1 hour
    @retry(max_retries=3, delay=2)
    def fetch_policy_documents(self, source_name=None, days_lookback=None):
        """
        Fetch policy documents using web scraping (simplified implementation).

        Args:
            source_name (str, optional): Source name.
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched documents.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        # For now, return empty DataFrame as web scraping requires more complex setup
        # In production, this would implement proper web scraping for government websites
        logger.info(f"Policy document fetching for {source_name} - placeholder implementation")

        # Create a sample structure for policy documents
        return pd.DataFrame(columns=['title', 'content', 'publish_date', 'source', 'url', 'doc_type'])

    @cached(expiry=3600)  # Cache for 1 hour
    def fetch_policy_rss_feeds(self, days_lookback=None):
        """
        Fetch policy information from RSS feeds.

        Args:
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched policy information.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        results = []

        for feed_url in self.rss_feeds:
            try:
                feed = feedparser.parse(feed_url)

                for entry in feed.entries:
                    # Extract publish date
                    if hasattr(entry, 'published_parsed'):
                        publish_date = datetime(*entry.published_parsed[:6]).date()
                    elif hasattr(entry, 'updated_parsed'):
                        publish_date = datetime(*entry.updated_parsed[:6]).date()
                    else:
                        publish_date = datetime.now().date()

                    # Check if within lookback period
                    cutoff_date = datetime.now().date() - timedelta(days=days_lookback)
                    if publish_date < cutoff_date:
                        continue

                    # Extract source
                    source = feed.feed.title if hasattr(feed.feed, 'title') else 'unknown'

                    # Extract content
                    if hasattr(entry, 'summary'):
                        content = entry.summary
                    elif hasattr(entry, 'description'):
                        content = entry.description
                    else:
                        content = ""

                    results.append({
                        'publish_date': publish_date,
                        'source': source,
                        'title': entry.title if hasattr(entry, 'title') else "",
                        'summary': content,
                        'link': entry.link if hasattr(entry, 'link') else "",
                        'doc_type': 'policy_rss'
                    })
            except Exception as e:
                logger.error(f"Error fetching RSS feed {feed_url}: {str(e)}")

        # Convert to DataFrame
        if not results:
            return pd.DataFrame()

        return pd.DataFrame(results)

    @cached(expiry=3600)  # Cache for 1 hour
    def fetch_market_news(self, days_lookback=None):
        """
        Fetch market news using AKShare APIs.

        Args:
            days_lookback (int, optional): Number of days to look back.

        Returns:
            pandas.DataFrame: DataFrame containing the fetched news.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        # 使用AKShare获取多种新闻源
        news_types = ['stock_news_em', 'stock_news_main_cx', 'news_cctv']
        all_news = []

        for news_type in news_types:
            try:
                df = self.fetch_akshare_news(news_type, days_lookback)
                if not df.empty:
                    all_news.append(df)
                    logger.info(f"Fetched {len(df)} news items from {news_type}")
            except Exception as e:
                logger.error(f"Error fetching {news_type}: {str(e)}")

        # 合并所有新闻
        if not all_news:
            logger.warning("No news data fetched from any source")
            return pd.DataFrame()

        combined_df = pd.concat(all_news, ignore_index=True)

        # 去重（基于标题）
        if 'title' in combined_df.columns:
            combined_df = combined_df.drop_duplicates(subset=['title'], keep='first')

        logger.info(f"Total news items after deduplication: {len(combined_df)}")
        return combined_df

    @safe_execute(default_return=[])
    def _fetch_news_from_source(self, source, url, days_lookback):
        """
        Fetch news from a specific source.

        Args:
            source (str): Source name.
            url (str): Source URL.
            days_lookback (int): Number of days to look back.

        Returns:
            list: List of news items.
        """
        response = make_request(url)
        soup = BeautifulSoup(response.text, 'html.parser')

        # This is a simplified implementation
        # In a real system, you would need to adapt the parsing logic for each source

        news_items = []

        # Find news links (this is a generic approach, might need customization for each source)
        for a in soup.find_all('a', href=True):
            if 'news' in a['href'] or 'article' in a['href']:
                title = a.text.strip()
                if title and len(title) > 5:  # Filter out short or empty titles
                    # Extract date from link or use current date
                    date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', a['href'])
                    if date_match:
                        date_str = date_match.group(1)
                        try:
                            publish_date = datetime.strptime(date_str, '%Y-%m-%d').date()
                        except ValueError:
                            try:
                                publish_date = datetime.strptime(date_str, '%Y/%m/%d').date()
                            except ValueError:
                                publish_date = datetime.now().date()
                    else:
                        publish_date = datetime.now().date()

                    # Check if within lookback period
                    cutoff_date = datetime.now().date() - timedelta(days=days_lookback)
                    if publish_date >= cutoff_date:
                        news_items.append({
                            'publish_date': publish_date,
                            'source': source,
                            'title': title,
                            'content': "",  # Would need to fetch the article content
                            'url': a['href'],
                            'keywords': []  # Would need to extract keywords
                        })

        return news_items

    def get_market_information(self, days_lookback=None, include_news=True, include_policy_docs=True, include_policy_rss=True):
        """
        Get market information from all sources.

        Args:
            days_lookback (int, optional): Number of days to look back.
            include_news (bool): Whether to include news.
            include_policy_docs (bool): Whether to include policy documents.
            include_policy_rss (bool): Whether to include policy RSS feeds.

        Returns:
            pandas.DataFrame: DataFrame containing all market information.
        """
        if days_lookback is None:
            days_lookback = self.default_lookback_days

        dfs = []

        # Fetch policy documents
        if include_policy_docs:
            for source in self.policy_sources:
                try:
                    df = self.fetch_policy_documents(source, days_lookback)
                    if not df.empty:
                        dfs.append(df)
                except Exception as e:
                    logger.error(f"Error fetching policy documents from {source}: {str(e)}")

        # Fetch policy RSS feeds
        if include_policy_rss:
            try:
                df = self.fetch_policy_rss_feeds(days_lookback)
                if not df.empty:
                    dfs.append(df)
            except Exception as e:
                logger.error(f"Error fetching policy RSS feeds: {str(e)}")

        # Fetch news using AKShare
        if include_news:
            try:
                df = self.fetch_market_news(days_lookback)
                if not df.empty:
                    dfs.append(df)
                    logger.info(f"Successfully fetched {len(df)} news items")
            except Exception as e:
                logger.error(f"Error fetching market news: {str(e)}")

        # Combine all DataFrames
        if not dfs:
            logger.warning("No data fetched from any source")
            return pd.DataFrame()

        result = pd.concat(dfs, ignore_index=True)

        # Sort by publish date (newest first)
        if 'publish_date' in result.columns:
            result = result.sort_values('publish_date', ascending=False)

        logger.info(f"Total market information items: {len(result)}")
        return result
