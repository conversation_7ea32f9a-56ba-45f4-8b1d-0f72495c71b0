"""
测试改进后的政策抓取功能
基于学习的爬虫技术改进
"""

import pandas as pd
import time
from datetime import datetime
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('improved_policy_scraping_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def test_improved_policy_scraping():
    """测试改进后的政策抓取功能"""
    
    logger.info("=" * 80)
    logger.info("测试改进后的政策抓取功能")
    logger.info("基于GitHub政策爬虫项目学习成果")
    logger.info("=" * 80)
    
    try:
        # 导入改进后的PolicyDataSource
        from data_sources.policy_data import PolicyDataSource
        policy_data = PolicyDataSource()
        logger.info("✅ PolicyDataSource 初始化成功")
    except Exception as e:
        logger.error(f"❌ PolicyDataSource 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 测试结果统计
    results = {}
    
    # 1. 测试改进后的国务院政策抓取（限制为20条进行快速测试）
    logger.info("\n" + "=" * 60)
    logger.info("测试改进后的国务院政策抓取")
    logger.info("=" * 60)
    
    start_time = time.time()
    try:
        gov_policies = policy_data.get_gov_policy(page=1, limit=20)
        elapsed = time.time() - start_time
        results['国务院政策'] = {
            'count': len(gov_policies),
            'time': elapsed,
            'status': 'success' if not gov_policies.empty else 'empty'
        }
        
        logger.info(f"✅ 国务院政策: {len(gov_policies)} 条, 耗时: {elapsed:.2f}秒")
        if not gov_policies.empty:
            logger.info(f"   列名: {list(gov_policies.columns)}")
            logger.info(f"   示例标题: {gov_policies['title'].iloc[0][:50]}...")
            logger.info(f"   示例来源: {gov_policies['source'].iloc[0]}")
            
            # 显示数据源分布
            if 'source' in gov_policies.columns:
                source_counts = gov_policies['source'].value_counts()
                logger.info("   数据源分布:")
                for source, count in source_counts.items():
                    logger.info(f"     {source}: {count} 条")
            
            # 显示前5条政策
            logger.info("\n前5条政策:")
            for i, row in gov_policies.head(5).iterrows():
                logger.info(f"  {i+1}. {row['title'][:60]}... ({row['source']})")
                if row['date']:
                    logger.info(f"      日期: {row['date']}")
        else:
            logger.warning("⚠️ 国务院政策抓取返回空数据")
            
    except Exception as e:
        results['国务院政策'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 国务院政策抓取失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. 测试改进后的发改委政策抓取（限制为20条进行快速测试）
    logger.info("\n" + "=" * 60)
    logger.info("测试改进后的发改委政策抓取")
    logger.info("=" * 60)
    
    start_time = time.time()
    try:
        ndrc_policies = policy_data.get_ndrc_policy(page=1, limit=20)
        elapsed = time.time() - start_time
        results['发改委政策'] = {
            'count': len(ndrc_policies),
            'time': elapsed,
            'status': 'success' if not ndrc_policies.empty else 'empty'
        }
        
        logger.info(f"✅ 发改委政策: {len(ndrc_policies)} 条, 耗时: {elapsed:.2f}秒")
        if not ndrc_policies.empty:
            logger.info(f"   列名: {list(ndrc_policies.columns)}")
            logger.info(f"   示例标题: {ndrc_policies['title'].iloc[0][:50]}...")
            logger.info(f"   示例来源: {ndrc_policies['source'].iloc[0]}")
            
            # 显示分类分布
            if 'category' in ndrc_policies.columns:
                category_counts = ndrc_policies['category'].value_counts()
                logger.info("   分类分布:")
                for category, count in category_counts.items():
                    logger.info(f"     {category}: {count} 条")
            
            # 显示前5条政策
            logger.info("\n前5条政策:")
            for i, row in ndrc_policies.head(5).iterrows():
                logger.info(f"  {i+1}. {row['title'][:60]}... ({row['source']})")
                if row['date']:
                    logger.info(f"      日期: {row['date']}")
        else:
            logger.warning("⚠️ 发改委政策抓取返回空数据")
            
    except Exception as e:
        results['发改委政策'] = {'count': 0, 'time': 0, 'status': 'error', 'error': str(e)}
        logger.error(f"❌ 发改委政策抓取失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 测试统一获取接口
    logger.info("\n" + "=" * 60)
    logger.info("测试统一政策获取接口")
    logger.info("=" * 60)
    
    try:
        # 获取所有政策数据
        all_policies = []
        
        if results['国务院政策']['status'] == 'success':
            gov_data = policy_data.get_gov_policy(limit=10)
            if not gov_data.empty:
                all_policies.append(gov_data)
                
        if results['发改委政策']['status'] == 'success':
            ndrc_data = policy_data.get_ndrc_policy(limit=10)
            if not ndrc_data.empty:
                all_policies.append(ndrc_data)
        
        if all_policies:
            combined_policies = pd.concat(all_policies, ignore_index=True)
            logger.info(f"✅ 统一获取: {len(combined_policies)} 条政策")
            
            # 显示来源分布
            source_counts = combined_policies['source'].value_counts()
            logger.info("   来源分布:")
            for source, count in source_counts.head(10).items():
                logger.info(f"     {source}: {count} 条")
        else:
            logger.warning("⚠️ 统一获取失败，没有有效数据")
            
    except Exception as e:
        logger.error(f"❌ 统一获取失败: {e}")
    
    # 4. 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("改进后政策抓取测试结果")
    logger.info("=" * 80)
    
    success_count = sum(1 for r in results.values() if r['status'] == 'success')
    empty_count = sum(1 for r in results.values() if r['status'] == 'empty')
    error_count = sum(1 for r in results.values() if r['status'] == 'error')
    total_policies = sum(r['count'] for r in results.values())
    total_time = sum(r['time'] for r in results.values())
    
    logger.info(f"📊 政策源总数: {len(results)}")
    logger.info(f"✅ 成功获取数据: {success_count}")
    logger.info(f"⚠️ 返回空数据: {empty_count}")
    logger.info(f"❌ 抓取失败: {error_count}")
    logger.info(f"📈 总政策数量: {total_policies}")
    logger.info(f"⏱️ 总耗时: {total_time:.2f} 秒")
    
    # 详细结果
    logger.info("\n详细结果:")
    for source_name, result in results.items():
        status_icon = "✅" if result['status'] == 'success' else "⚠️" if result['status'] == 'empty' else "❌"
        logger.info(f"{status_icon} {source_name}: {result['count']} 条, {result['time']:.2f}秒")
        if result['status'] == 'error':
            logger.info(f"   错误: {result.get('error', '未知错误')}")
    
    # 改进效果评估
    logger.info("\n" + "=" * 60)
    logger.info("改进效果评估")
    logger.info("=" * 60)
    
    if total_policies > 0:
        logger.info("🎉 政策抓取改进成功！")
        logger.info("✅ 多数据源策略有效")
        logger.info("✅ 智能解析技术工作正常")
        logger.info("✅ 数据去重和清理功能正常")
        
        if total_policies >= 10:
            logger.info("🚀 可以将限制提高到1000条进行大规模抓取")
        else:
            logger.info("⚠️ 数据量较少，建议进一步优化抓取策略")
    else:
        logger.warning("❌ 政策抓取仍然失败，需要进一步调试")
    
    # 判断测试是否成功
    success = total_policies > 0
    
    return success, total_policies

if __name__ == "__main__":
    logger.info(f"开始改进后政策抓取测试 - {datetime.now()}")
    
    success, total_policies = test_improved_policy_scraping()
    
    logger.info("\n" + "=" * 80)
    logger.info("测试完成")
    logger.info("=" * 80)
    logger.info(f"测试结束时间: {datetime.now()}")
    logger.info("详细日志已保存到: improved_policy_scraping_test.log")
    
    if success:
        logger.info(f"🎯 下一步: 验证数据库存储流程")
        logger.info(f"📊 获取到 {total_policies} 条政策数据，可以进行数据库对接测试")
    else:
        logger.info("🔧 需要进一步调试政策抓取逻辑")
        logger.info("💡 建议检查网络连接和目标网站结构变化")
