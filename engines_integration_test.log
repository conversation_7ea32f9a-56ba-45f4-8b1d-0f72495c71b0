2025-05-27 13:56:59,937 - __main__ - INFO - 开始engines和data_sources整合测试 - 2025-05-27 13:56:59.937141
2025-05-27 13:56:59,937 - __main__ - INFO - ================================================================================
2025-05-27 13:56:59,938 - __main__ - INFO - 测试统一数据获取层 (data_sources)
2025-05-27 13:56:59,938 - __main__ - INFO - ================================================================================
2025-05-27 13:56:59,938 - __main__ - INFO - 
1. 测试PolicyDataSource...
2025-05-27 13:57:01,067 - __main__ - INFO - ✅ 财经早餐: 400 条
2025-05-27 13:57:01,067 - __main__ - INFO - 
2. 测试MarketDataSource...
2025-05-27 13:57:06,971 - __main__ - INFO - ✅ 指数数据: 8406 条
2025-05-27 13:57:06,972 - __main__ - INFO - 
3. 测试FundFlowDataSource...
2025-05-27 13:57:07,141 - __main__ - INFO - ✅ 北向南向资金流: 4 条
2025-05-27 13:57:07,144 - __main__ - INFO - 
================================================================================
2025-05-27 13:57:07,144 - __main__ - INFO - 测试统一分析引擎层 (engines)
2025-05-27 13:57:07,144 - __main__ - INFO - ================================================================================
2025-05-27 13:57:07,144 - __main__ - INFO - 
1. 测试SentimentAnalyzer...
2025-05-27 13:57:10,658 - __main__ - ERROR - ❌ SentimentAnalyzer测试失败: cannot import name 'FundFlowAnalyzer' from 'engines.fund_flow.analyzer' (D:\System_Developing\policy_liquidity_volatility_arbitrage\engines\fund_flow\analyzer.py)
2025-05-27 13:57:10,658 - __main__ - INFO - 
2. 测试FundFlowAnalyzer...
2025-05-27 13:57:10,658 - __main__ - ERROR - ❌ FundFlowAnalyzer测试失败: cannot import name 'FundFlowAnalyzer' from 'engines.fund_flow.analyzer' (D:\System_Developing\policy_liquidity_volatility_arbitrage\engines\fund_flow\analyzer.py)
2025-05-27 13:57:10,658 - __main__ - INFO - 
3. 测试VolatilityAnalyzer...
2025-05-27 13:57:10,660 - __main__ - ERROR - ❌ VolatilityAnalyzer测试失败: cannot import name 'FundFlowAnalyzer' from 'engines.fund_flow.analyzer' (D:\System_Developing\policy_liquidity_volatility_arbitrage\engines\fund_flow\analyzer.py)
2025-05-27 13:57:10,660 - __main__ - INFO - 
4. 测试向后兼容性...
2025-05-27 13:57:10,660 - __main__ - ERROR - ❌ 向后兼容性测试失败: cannot import name 'FundFlowAnalyzer' from 'engines.fund_flow.analyzer' (D:\System_Developing\policy_liquidity_volatility_arbitrage\engines\fund_flow\analyzer.py)
2025-05-27 13:57:10,660 - __main__ - INFO - 
================================================================================
2025-05-27 13:57:10,660 - __main__ - INFO - 测试核心模块集成
2025-05-27 13:57:10,661 - __main__ - INFO - ================================================================================
2025-05-27 13:57:10,667 - data_storage - INFO - 数据存储初始化完成
2025-05-27 13:57:10,667 - jieba - DEBUG - Building prefix dict from the default dictionary ...
2025-05-27 13:57:10,668 - jieba - DEBUG - Loading model from cache C:\Users\<USER>\AppData\Local\Temp\jieba.cache
2025-05-27 13:57:10,977 - jieba - DEBUG - Loading model cost 0.310 seconds.
2025-05-27 13:57:10,977 - jieba - DEBUG - Prefix dict has been built successfully.
2025-05-27 13:57:10,980 - __main__ - INFO - ✅ UnifiedDataCollector 初始化成功
2025-05-27 13:57:10,980 - __main__ - INFO - ✅ 包含PolicyDataSource
2025-05-27 13:57:10,980 - __main__ - INFO - ✅ 包含MarketDataSource
2025-05-27 13:57:10,980 - __main__ - INFO - ✅ 包含FundFlowDataSource
2025-05-27 13:57:10,980 - __main__ - INFO - 
================================================================================
2025-05-27 13:57:10,980 - __main__ - INFO - 测试数据流连接
2025-05-27 13:57:10,980 - __main__ - INFO - ================================================================================
2025-05-27 13:57:10,980 - __main__ - INFO - 1. 测试数据获取 → 数据库流程...
2025-05-27 13:57:11,693 - __main__ - INFO - ✅ 获取到 400 条新闻数据
2025-05-27 13:57:11,695 - __main__ - ERROR - ❌ 数据流测试失败: ContentProcessor.__init__() missing 2 required positional arguments: 'data_access' and 'file_storage'
2025-05-27 13:57:11,695 - __main__ - INFO - 
================================================================================
2025-05-27 13:57:11,695 - __main__ - INFO - 整合测试结果报告
2025-05-27 13:57:11,695 - __main__ - INFO - ================================================================================
2025-05-27 13:57:11,695 - __main__ - INFO - 
📊 数据获取层 (data_sources):
2025-05-27 13:57:11,696 - __main__ - INFO -    PolicyDataSource: 400 条新闻
2025-05-27 13:57:11,696 - __main__ - INFO -    MarketDataSource: 8406 条市场数据
2025-05-27 13:57:11,696 - __main__ - INFO -    FundFlowDataSource: 4 条资金流数据
2025-05-27 13:57:11,696 - __main__ - INFO - 
🔧 分析引擎层 (engines):
2025-05-27 13:57:11,696 - __main__ - INFO -    SentimentAnalyzer: ❌
2025-05-27 13:57:11,697 - __main__ - INFO -    FundFlowAnalyzer: ❌
2025-05-27 13:57:11,697 - __main__ - INFO -    VolatilityAnalyzer: ❌
2025-05-27 13:57:11,697 - __main__ - INFO -    向后兼容性: ❌
2025-05-27 13:57:11,697 - __main__ - INFO - 
🔗 核心模块集成: ✅
2025-05-27 13:57:11,697 - __main__ - INFO - 🌊 数据流连接: ❌
2025-05-27 13:57:11,697 - __main__ - INFO - 
🎯 整合成功率: 50.0% (4/8)
2025-05-27 13:57:11,697 - __main__ - WARNING - ❌ 整合存在问题，需要进一步调试
2025-05-27 13:57:11,697 - __main__ - INFO - 
================================================================================
2025-05-27 13:57:11,697 - __main__ - INFO - 整合测试完成
2025-05-27 13:57:11,697 - __main__ - INFO - ================================================================================
2025-05-27 13:57:11,697 - __main__ - INFO - 测试结束时间: 2025-05-27 13:57:11.697936
2025-05-27 13:57:11,697 - __main__ - WARNING - ⚠️ 整合需要进一步完善
2025-05-27 13:57:11,699 - __main__ - INFO - 详细日志已保存到: engines_integration_test.log
