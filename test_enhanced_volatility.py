"""
测试增强版波动率分析器
验证GARCH参数优化、政策波动率溢价、资金流-波动率耦合等功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置详细日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_volatility_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_mock_price_data(days=100):
    """创建模拟价格数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    
    # 模拟价格路径
    returns = np.random.normal(0.001, 0.02, days)  # 日收益率
    prices = [100]  # 初始价格
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    price_data = pd.DataFrame({
        'date': dates,
        'close': prices,
        'open': np.array(prices) * (1 + np.random.normal(0, 0.005, days)),
        'high': np.array(prices) * (1 + np.abs(np.random.normal(0, 0.01, days))),
        'low': np.array(prices) * (1 - np.abs(np.random.normal(0, 0.01, days))),
        'volume': np.random.randint(1000000, 10000000, days)
    })
    
    return price_data

def create_mock_fund_flow_data(days=100):
    """创建模拟资金流数据"""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=days, freq='D')
    
    fund_flow_data = pd.DataFrame({
        'date': dates,
        'net_flow': np.random.normal(0, 50000000, days),  # 净流入
        'main_inflow': np.random.uniform(0, 100000000, days),
        'main_outflow': np.random.uniform(0, 100000000, days)
    })
    
    return fund_flow_data

def create_mock_policy_events():
    """创建模拟政策事件"""
    policy_events = [
        {
            'date': '2024-02-15',
            'type': 'monetary_policy',
            'importance': 0.8,
            'description': '央行降准0.5个百分点'
        },
        {
            'date': '2024-03-10',
            'type': 'fiscal_policy',
            'importance': 0.6,
            'description': '财政部发布减税政策'
        },
        {
            'date': '2024-04-05',
            'type': 'regulatory_policy',
            'importance': 0.7,
            'description': '证监会发布新规'
        }
    ]
    
    return policy_events

def test_enhanced_volatility_analyzer():
    """测试增强版波动率分析器"""
    
    logger.info("=" * 80)
    logger.info("测试增强版波动率分析器")
    logger.info("=" * 80)
    
    try:
        from engines.volatility.enhanced_analyzer import EnhancedVolatilityAnalyzer
        
        # 初始化分析器
        config = {
            'garch_p_range': [1, 2],
            'garch_q_range': [1, 2],
            'garch_distribution': 'normal'
        }
        
        analyzer = EnhancedVolatilityAnalyzer(config=config)
        logger.info("✅ EnhancedVolatilityAnalyzer 初始化成功")
        
        # 创建测试数据
        price_data = create_mock_price_data(100)
        fund_flow_data = create_mock_fund_flow_data(100)
        policy_events = create_mock_policy_events()
        
        logger.info(f"📊 测试数据准备完成:")
        logger.info(f"   价格数据: {len(price_data)} 天")
        logger.info(f"   资金流数据: {len(fund_flow_data)} 天")
        logger.info(f"   政策事件: {len(policy_events)} 个")
        
        test_results = {}
        
        # 1. 测试GARCH参数优化和波动率预测
        logger.info("\n" + "=" * 60)
        logger.info("测试GARCH参数优化和波动率预测")
        logger.info("=" * 60)
        
        try:
            garch_result = analyzer.calculate_enhanced_garch_volatility(price_data, forecast_horizon=5)
            
            logger.info("✅ GARCH波动率预测完成")
            logger.info(f"   最优参数: p={garch_result['optimal_parameters']['p']}, q={garch_result['optimal_parameters']['q']}")
            logger.info(f"   当前年化波动率: {garch_result['current_volatility']:.3f}")
            logger.info(f"   预测期数: {garch_result['forecast_horizon']}")
            logger.info(f"   模型AIC: {garch_result['model_fit_quality']['aic']:.2f}")
            
            if len(garch_result['volatility_forecast']) > 0:
                logger.info(f"   5日波动率预测: {garch_result['volatility_forecast']}")
            
            test_results['garch_volatility'] = True
            
        except Exception as e:
            logger.error(f"❌ GARCH波动率测试失败: {e}")
            test_results['garch_volatility'] = False
        
        # 2. 测试政策波动率溢价
        logger.info("\n" + "=" * 60)
        logger.info("测试政策波动率溢价")
        logger.info("=" * 60)
        
        try:
            policy_vol_result = analyzer.calculate_policy_volatility_premium(price_data, policy_events)
            
            logger.info("✅ 政策波动率溢价计算完成")
            logger.info(f"   政策波动率溢价: {policy_vol_result['policy_volatility_premium']:.4f}")
            logger.info(f"   政策波动率比率: {policy_vol_result['policy_volatility_ratio']:.3f}")
            logger.info(f"   政策期间波动率: {policy_vol_result['policy_period_volatility']:.3f}")
            logger.info(f"   正常期间波动率: {policy_vol_result['normal_period_volatility']:.3f}")
            logger.info(f"   分析的政策事件数: {policy_vol_result['policy_events_analyzed']}")
            
            if policy_vol_result['policy_type_impact']:
                logger.info("   政策类型影响:")
                for policy_type, impact in policy_vol_result['policy_type_impact'].items():
                    logger.info(f"     {policy_type}: 平均影响 {impact['avg_impact']:.3f}")
            
            test_results['policy_volatility'] = True
            
        except Exception as e:
            logger.error(f"❌ 政策波动率溢价测试失败: {e}")
            test_results['policy_volatility'] = False
        
        # 3. 测试资金流-波动率耦合分析
        logger.info("\n" + "=" * 60)
        logger.info("测试资金流-波动率耦合分析")
        logger.info("=" * 60)
        
        try:
            coupling_result = analyzer.analyze_fund_flow_volatility_coupling(price_data, fund_flow_data)
            
            logger.info("✅ 资金流-波动率耦合分析完成")
            logger.info(f"   同期相关性: {coupling_result['contemporaneous_correlation']:.3f}")
            logger.info(f"   耦合强度: {coupling_result['coupling_strength']:.3f}")
            logger.info(f"   耦合制度: {coupling_result['coupling_regime']}")
            logger.info(f"   数据点数: {coupling_result['data_points']}")
            
            if coupling_result['lag_correlations']:
                logger.info("   滞后相关性:")
                for lag, corr in coupling_result['lag_correlations'].items():
                    logger.info(f"     {lag}: {corr:.3f}")
            
            if coupling_result['directional_impact']:
                directional = coupling_result['directional_impact']
                logger.info(f"   流入时平均波动率: {directional['inflow_avg_volatility']:.3f}")
                logger.info(f"   流出时平均波动率: {directional['outflow_avg_volatility']:.3f}")
                logger.info(f"   不对称比率: {directional['asymmetry_ratio']:.3f}")
            
            test_results['coupling_analysis'] = True
            
        except Exception as e:
            logger.error(f"❌ 资金流-波动率耦合分析测试失败: {e}")
            test_results['coupling_analysis'] = False
        
        # 4. 测试波动率制度识别
        logger.info("\n" + "=" * 60)
        logger.info("测试波动率制度识别")
        logger.info("=" * 60)
        
        try:
            regime_result = analyzer.identify_volatility_regime(price_data, window=20)
            
            logger.info("✅ 波动率制度识别完成")
            logger.info(f"   当前波动率: {regime_result['current_volatility']:.3f}")
            logger.info(f"   当前制度: {regime_result['current_regime']}")
            logger.info(f"   波动率分位数: {regime_result['volatility_percentile']:.3f}")
            
            if regime_result['regime_statistics']:
                stats = regime_result['regime_statistics']
                logger.info(f"   制度统计 - 总期数: {stats['total_periods']}")
                
                if 'regime_frequencies' in stats:
                    logger.info("   制度频率:")
                    for regime, freq in stats['regime_frequencies'].items():
                        logger.info(f"     {regime}: {freq:.3f}")
            
            if regime_result['regime_transition_probability']:
                logger.info("   制度转换概率矩阵已计算")
            
            test_results['regime_identification'] = True
            
        except Exception as e:
            logger.error(f"❌ 波动率制度识别测试失败: {e}")
            test_results['regime_identification'] = False
        
        return test_results
        
    except Exception as e:
        logger.error(f"❌ 增强版波动率分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def test_multifactor_volatility_forecast():
    """测试多因子波动率预测"""
    
    logger.info("\n" + "=" * 80)
    logger.info("测试多因子波动率预测")
    logger.info("=" * 80)
    
    try:
        from engines.volatility.enhanced_analyzer import EnhancedVolatilityAnalyzer
        
        analyzer = EnhancedVolatilityAnalyzer()
        
        # 创建测试数据
        price_data = create_mock_price_data(60)  # 更多数据用于训练
        fund_flow_data = create_mock_fund_flow_data(60)
        sentiment_data = {'sentiment_score': 0.3}
        
        logger.info("🔮 开始多因子波动率预测...")
        
        forecast_result = analyzer.forecast_multifactor_volatility(
            price_data, fund_flow_data, sentiment_data, forecast_days=5
        )
        
        logger.info("✅ 多因子波动率预测完成")
        logger.info(f"   预测天数: {forecast_result['forecast_horizon']}")
        logger.info(f"   训练样本数: {forecast_result['training_samples']}")
        logger.info(f"   模型评分: {forecast_result['model_score']:.3f}")
        logger.info(f"   当前波动率: {forecast_result['current_volatility']:.3f}")
        
        if forecast_result['volatility_forecast']:
            logger.info(f"   波动率预测: {forecast_result['volatility_forecast']}")
        
        if forecast_result['feature_importance']:
            logger.info("   特征重要性:")
            for feature, importance in list(forecast_result['feature_importance'].items())[:5]:
                logger.info(f"     {feature}: {importance:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 多因子波动率预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    logger.info(f"开始增强版波动率分析器测试 - {datetime.now()}")
    
    # 执行各项测试
    analyzer_results = test_enhanced_volatility_analyzer()
    multifactor_success = test_multifactor_volatility_forecast()
    
    # 生成测试报告
    logger.info("\n" + "=" * 80)
    logger.info("增强版波动率分析器测试结果")
    logger.info("=" * 80)
    
    if analyzer_results:
        success_count = sum(analyzer_results.values())
        total_count = len(analyzer_results)
        
        for test_name, success in analyzer_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            logger.info(f"{status} {test_name}")
        
        logger.info(f"\n基础功能: {success_count}/{total_count} 项测试通过")
    else:
        logger.warning("⚠️ 基础功能测试失败")
        success_count, total_count = 0, 0
    
    multifactor_status = "✅ 成功" if multifactor_success else "❌ 失败"
    logger.info(f"{multifactor_status} 多因子波动率预测")
    
    # 总体评估
    overall_success = (
        success_count >= 3 and  # 至少3项基础功能通过
        multifactor_success     # 多因子预测成功
    )
    
    if overall_success:
        logger.info("\n🎉 增强版波动率分析器功能正常！")
        logger.info("📈 GARCH参数优化已实现")
        logger.info("🏛️ 政策波动率溢价模型已完善")
        logger.info("🔗 资金流-波动率耦合分析已优化")
        logger.info("🎯 波动率制度识别功能正常")
        logger.info("🚀 波动率分析模块完善完成")
    else:
        logger.warning("⚠️ 增强版波动率分析器存在问题，需要进一步调试")
    
    logger.info(f"\n测试完成时间: {datetime.now()}")
    logger.info("详细日志已保存到: enhanced_volatility_test.log")
    
    return overall_success

if __name__ == "__main__":
    main()
