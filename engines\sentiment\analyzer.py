"""
Sentiment Analyzer module.
Responsible for analyzing sentiment in news and other text data.
"""

import os
import re
import pandas as pd
import numpy as np
from datetime import datetime
import jieba
import jieba.analyse
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification

from utils.logger import logger
from utils.config_loader import config_loader
from utils.error_utils import safe_execute
from utils.cache_utils import cached

class SentimentAnalyzer:
    """
    Class for analyzing sentiment in text data.
    """

    def __init__(self, config=None, model_path=None):
        """
        Initialize the SentimentAnalyzer.

        Args:
            config: Configuration object or None to use default.
            model_path (str, optional): Path to sentiment model.
        """
        self.config = config if config else config_loader

        # Load model path from configuration if not provided
        if model_path is None:
            model_path = self.config.get('engines.sentiment.model_path', 'models/sentiment')

        self.model_path = model_path

        # Initialize sentiment dictionary
        self._init_sentiment_dict()

        # Initialize NLP model
        self._init_nlp_model()

        logger.info("SentimentAnalyzer initialized")

    def _init_sentiment_dict(self):
        """
        Initialize sentiment dictionary.
        """
        # Load positive and negative word lists
        # This is a simplified approach
        # In a real system, you would use more comprehensive dictionaries

        self.positive_words = set([
            "利好", "上涨", "增长", "提高", "扩大", "促进", "支持", "优化", "改善",
            "机遇", "机会", "利润", "盈利", "收益", "红利", "股息", "分红", "回购",
            "突破", "创新高", "牛市", "牛", "强势", "领涨", "爆发", "暴涨", "大涨"
        ])

        self.negative_words = set([
            "利空", "下跌", "下滑", "减少", "收缩", "限制", "禁止", "恶化", "下降",
            "风险", "危机", "亏损", "亏", "损失", "减持", "抛售", "套现", "减仓",
            "破位", "创新低", "熊市", "熊", "弱势", "领跌", "崩盘", "暴跌", "大跌"
        ])

        logger.info("Sentiment dictionary initialized")

    def _init_nlp_model(self):
        """
        Initialize NLP model.
        """
        try:
            # Check if model directory exists
            if not os.path.exists(self.model_path):
                logger.warning(f"Sentiment model path {self.model_path} not found. Using default model.")
                # Use default model from Hugging Face
                self.tokenizer = AutoTokenizer.from_pretrained("bert-base-chinese")
                self.model = AutoModelForSequenceClassification.from_pretrained("bert-base-chinese")
            else:
                # Load model from local path
                self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
                self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)

            # Create pipeline
            self.sentiment_pipeline = pipeline("sentiment-analysis", model=self.model, tokenizer=self.tokenizer)

            logger.info("NLP model initialized")
        except Exception as e:
            logger.error(f"Error initializing NLP model: {str(e)}")
            logger.info("Using dictionary-based sentiment analysis as fallback")

            # Set model to None to use dictionary-based analysis as fallback
            self.tokenizer = None
            self.model = None
            self.sentiment_pipeline = None

    @safe_execute(default_return=0.0)
    def analyze_text_sentiment(self, text):
        """
        Analyze sentiment in text.

        Args:
            text (str): Text to analyze.

        Returns:
            float: Sentiment score (-1.0 to 1.0).
        """
        if not text:
            return 0.0

        # Try using NLP model first
        if self.sentiment_pipeline:
            try:
                # Limit text length to avoid token limit issues
                text_for_sentiment = text[:512]  # Use first 512 characters
                result = self.sentiment_pipeline(text_for_sentiment)

                # Convert result to score
                if result[0]['label'] == 'POSITIVE':
                    return result[0]['score']
                elif result[0]['label'] == 'NEGATIVE':
                    return -result[0]['score']
                else:
                    return 0.0
            except Exception as e:
                logger.error(f"Error analyzing sentiment with NLP model: {str(e)}")
                logger.info("Falling back to dictionary-based sentiment analysis")

        # Fall back to dictionary-based analysis
        words = jieba.lcut(text)

        positive_count = sum(1 for word in words if word in self.positive_words)
        negative_count = sum(1 for word in words if word in self.negative_words)

        total_count = positive_count + negative_count

        if total_count == 0:
            return 0.0

        return (positive_count - negative_count) / total_count

    @safe_execute(default_return=[])
    def analyze_batch(self, texts):
        """
        Analyze sentiment for a batch of texts.

        Args:
            texts (list): List of texts to analyze.

        Returns:
            list: List of sentiment analysis results.
        """
        results = []

        for text in texts:
            sentiment_score = self.analyze_text_sentiment(text)

            # Convert score to sentiment label
            if sentiment_score > 0.2:
                sentiment = 'positive'
            elif sentiment_score < -0.2:
                sentiment = 'negative'
            else:
                sentiment = 'neutral'

            # Calculate confidence (absolute value of score)
            confidence = abs(sentiment_score)

            results.append({
                'text': text,
                'sentiment': sentiment,
                'confidence': confidence,
                'sentiment_score': sentiment_score
            })

        return results

    @safe_execute(default_return=pd.DataFrame())
    def analyze_news_df(self, news_df):
        """
        Analyze sentiment in news DataFrame.

        Args:
            news_df (pandas.DataFrame): DataFrame containing news.

        Returns:
            pandas.DataFrame: DataFrame with sentiment scores.
        """
        # Filter news only
        if 'doc_type' in news_df.columns:
            df = news_df[news_df['doc_type'] == 'news'].copy()
        else:
            df = news_df.copy()

        if df.empty:
            return df

        # Add sentiment score column
        df['sentiment_score'] = 0.0

        # Analyze sentiment for each news item
        for i, row in df.iterrows():
            # Get text to analyze
            if 'content' in row and row['content']:
                text = row['content']
            elif 'summary' in row and row['summary']:
                text = row['summary']
            elif 'title' in row and row['title']:
                text = row['title']
            else:
                text = ""

            # Analyze sentiment
            sentiment_score = self.analyze_text_sentiment(text)
            df.at[i, 'sentiment_score'] = sentiment_score

        return df

    @safe_execute(default_return={})
    def get_stock_sentiment(self, stock_code, news_df):
        """
        Get sentiment for a specific stock.

        Args:
            stock_code (str): Stock code.
            news_df (pandas.DataFrame): DataFrame containing news.

        Returns:
            dict: Stock sentiment information.
        """
        # Filter news related to the stock
        stock_news = []

        for _, row in news_df.iterrows():
            # Check if stock code is in title or content
            if 'title' in row and stock_code in row['title']:
                stock_news.append(row)
            elif 'content' in row and stock_code in row['content']:
                stock_news.append(row)

        if not stock_news:
            return {
                'stock_code': stock_code,
                'sentiment_score': 0.0,
                'news_count': 0,
                'latest_sentiment': 0.0,
                'sentiment_trend': 'neutral'
            }

        # Convert to DataFrame
        stock_news_df = pd.DataFrame(stock_news)

        # Analyze sentiment
        stock_news_df = self.analyze_news_df(stock_news_df)

        # Calculate average sentiment
        avg_sentiment = stock_news_df['sentiment_score'].mean()

        # Get latest sentiment
        if 'publish_date' in stock_news_df.columns:
            latest_news = stock_news_df.sort_values('publish_date', ascending=False).iloc[0]
            latest_sentiment = latest_news['sentiment_score']
        else:
            latest_sentiment = avg_sentiment

        # Determine sentiment trend
        if latest_sentiment > avg_sentiment + 0.1:
            sentiment_trend = 'improving'
        elif latest_sentiment < avg_sentiment - 0.1:
            sentiment_trend = 'deteriorating'
        else:
            sentiment_trend = 'stable'

        return {
            'stock_code': stock_code,
            'sentiment_score': avg_sentiment,
            'news_count': len(stock_news_df),
            'latest_sentiment': latest_sentiment,
            'sentiment_trend': sentiment_trend
        }

    @safe_execute(default_return=pd.DataFrame())
    def get_market_sentiment(self, news_df):
        """
        Get overall market sentiment.

        Args:
            news_df (pandas.DataFrame): DataFrame containing news.

        Returns:
            dict: Market sentiment information.
        """
        # Analyze sentiment for all news
        sentiment_df = self.analyze_news_df(news_df)

        if sentiment_df.empty:
            return {
                'market_sentiment_score': 0.0,
                'positive_news_ratio': 0.0,
                'negative_news_ratio': 0.0,
                'neutral_news_ratio': 1.0,
                'sentiment_trend': 'neutral'
            }

        # Calculate average sentiment
        avg_sentiment = sentiment_df['sentiment_score'].mean()

        # Count positive, negative, and neutral news
        positive_count = (sentiment_df['sentiment_score'] > 0.2).sum()
        negative_count = (sentiment_df['sentiment_score'] < -0.2).sum()
        neutral_count = len(sentiment_df) - positive_count - negative_count

        # Calculate ratios
        total_count = len(sentiment_df)
        positive_ratio = positive_count / total_count if total_count > 0 else 0.0
        negative_ratio = negative_count / total_count if total_count > 0 else 0.0
        neutral_ratio = neutral_count / total_count if total_count > 0 else 1.0

        # Determine sentiment trend
        if 'publish_date' in sentiment_df.columns:
            # Sort by date
            sentiment_df = sentiment_df.sort_values('publish_date')

            # Calculate rolling average
            if len(sentiment_df) >= 10:
                rolling_sentiment = sentiment_df['sentiment_score'].rolling(window=5).mean()

                # Get first and last valid rolling sentiment
                first_valid = rolling_sentiment.dropna().iloc[0]
                last_valid = rolling_sentiment.dropna().iloc[-1]

                # Determine trend
                if last_valid > first_valid + 0.1:
                    sentiment_trend = 'improving'
                elif last_valid < first_valid - 0.1:
                    sentiment_trend = 'deteriorating'
                else:
                    sentiment_trend = 'stable'
            else:
                sentiment_trend = 'neutral'
        else:
            sentiment_trend = 'neutral'

        return {
            'market_sentiment_score': avg_sentiment,
            'positive_news_ratio': positive_ratio,
            'negative_news_ratio': negative_ratio,
            'neutral_news_ratio': neutral_ratio,
            'sentiment_trend': sentiment_trend
        }
