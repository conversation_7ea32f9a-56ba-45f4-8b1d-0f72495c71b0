"""
核心决策引擎
整合政策、情绪、资金流、波动率等多维度信号，生成综合投资决策
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional, Union
import json
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CoreDecisionEngine:
    """
    核心决策引擎

    功能：
    1. 多因子信号整合
    2. 市场状态适应性调整
    3. 风险评估和仓位管理
    4. 投资决策生成
    5. 信号强度评分
    """

    def __init__(self, config=None):
        """
        初始化核心决策引擎

        Args:
            config: 配置参数
        """
        self.config = config or {}

        # 信号权重配置
        self.signal_weights = {
            'policy': self.config.get('policy_weight', 0.25),
            'sentiment': self.config.get('sentiment_weight', 0.20),
            'fund_flow': self.config.get('fund_flow_weight', 0.30),
            'volatility': self.config.get('volatility_weight', 0.25)
        }

        # 决策阈值
        self.decision_thresholds = {
            'strong_buy': 0.75,
            'buy': 0.60,
            'hold': 0.40,
            'sell': 0.25,
            'strong_sell': 0.10
        }

        # 市场状态参数
        self.market_regimes = {
            'bull': {'volatility_threshold': 0.15, 'sentiment_threshold': 0.6},
            'bear': {'volatility_threshold': 0.35, 'sentiment_threshold': 0.3},
            'sideways': {'volatility_threshold': 0.25, 'sentiment_threshold': 0.5}
        }

        # 风险控制参数
        self.risk_params = {
            'max_position_size': 0.10,  # 单股最大仓位
            'max_sector_exposure': 0.30,  # 单行业最大敞口
            'volatility_limit': 0.40,  # 波动率上限
            'correlation_limit': 0.70  # 相关性上限
        }

        logger.info("核心决策引擎初始化完成")

    def generate_comprehensive_decision(self, symbol: str,
                                      policy_signals: Dict,
                                      sentiment_signals: Dict,
                                      fund_flow_signals: Dict,
                                      volatility_signals: Dict,
                                      market_data: Dict = None) -> Dict:
        """
        生成综合投资决策

        Args:
            symbol: 股票代码
            policy_signals: 政策信号
            sentiment_signals: 情绪信号
            fund_flow_signals: 资金流信号
            volatility_signals: 波动率信号
            market_data: 市场数据

        Returns:
            Dict: 综合决策结果
        """
        try:
            logger.info(f"为 {symbol} 生成综合投资决策...")

            # 1. 信号标准化和评分
            normalized_signals = self._normalize_signals({
                'policy': policy_signals,
                'sentiment': sentiment_signals,
                'fund_flow': fund_flow_signals,
                'volatility': volatility_signals
            })

            # 2. 市场状态识别
            market_regime = self._identify_market_regime(volatility_signals, sentiment_signals)

            # 3. 动态权重调整
            adjusted_weights = self._adjust_weights_by_regime(market_regime)

            # 4. 综合评分计算
            composite_score = self._calculate_composite_score(normalized_signals, adjusted_weights)

            # 5. 风险评估
            risk_assessment = self._assess_risk(symbol, volatility_signals, fund_flow_signals)

            # 6. 决策生成
            decision = self._generate_decision(composite_score, risk_assessment)

            # 7. 仓位建议
            position_recommendation = self._calculate_position_size(
                decision, risk_assessment, composite_score
            )

            # 8. 信号解释
            signal_explanation = self._explain_signals(normalized_signals, adjusted_weights)

            result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'composite_score': composite_score,
                'decision': decision,
                'position_recommendation': position_recommendation,
                'market_regime': market_regime,
                'risk_assessment': risk_assessment,
                'signal_breakdown': normalized_signals,
                'signal_weights': adjusted_weights,
                'signal_explanation': signal_explanation,
                'confidence_level': self._calculate_confidence(normalized_signals, risk_assessment)
            }

            logger.info(f"{symbol} 决策完成: {decision['action']} (评分: {composite_score:.3f})")
            return result

        except Exception as e:
            logger.error(f"生成综合决策失败: {e}")
            return self._get_empty_decision_result(symbol)

    def _normalize_signals(self, signals: Dict) -> Dict:
        """标准化各类信号到0-1区间"""
        try:
            normalized = {}

            # 政策信号标准化
            if 'policy' in signals and signals['policy']:
                policy_score = 0.5  # 默认中性
                if 'policy_impact_score' in signals['policy']:
                    policy_score = max(0, min(1, signals['policy']['policy_impact_score']))
                elif 'comprehensive_score' in signals['policy']:
                    policy_score = max(0, min(1, signals['policy']['comprehensive_score']))

                normalized['policy'] = {
                    'score': policy_score,
                    'direction': signals['policy'].get('direction', 'neutral'),
                    'strength': signals['policy'].get('strength', 'medium')
                }
            else:
                normalized['policy'] = {'score': 0.5, 'direction': 'neutral', 'strength': 'medium'}

            # 情绪信号标准化
            if 'sentiment' in signals and signals['sentiment']:
                sentiment_score = 0.5  # 默认中性
                if 'sentiment_index' in signals['sentiment']:
                    # 假设情绪指数在-1到1之间，转换到0-1
                    sentiment_score = (signals['sentiment']['sentiment_index'] + 1) / 2
                elif 'comprehensive_sentiment_score' in signals['sentiment']:
                    sentiment_score = max(0, min(1, signals['sentiment']['comprehensive_sentiment_score']))

                normalized['sentiment'] = {
                    'score': sentiment_score,
                    'resonance_strength': signals['sentiment'].get('resonance_strength', 0.5),
                    'trend': signals['sentiment'].get('trend', 'stable')
                }
            else:
                normalized['sentiment'] = {'score': 0.5, 'resonance_strength': 0.5, 'trend': 'stable'}

            # 资金流信号标准化
            if 'fund_flow' in signals and signals['fund_flow']:
                fund_flow_score = 0.5  # 默认中性
                if 'comprehensive_score' in signals['fund_flow']:
                    fund_flow_score = max(0, min(1, signals['fund_flow']['comprehensive_score']))
                elif 'net_inflow_ratio' in signals['fund_flow']:
                    # 资金净流入比例标准化
                    ratio = signals['fund_flow']['net_inflow_ratio']
                    fund_flow_score = max(0, min(1, (ratio + 1) / 2))

                normalized['fund_flow'] = {
                    'score': fund_flow_score,
                    'main_force_activity': signals['fund_flow'].get('main_force_activity', 'medium'),
                    'hot_money_interest': signals['fund_flow'].get('hot_money_interest', 'medium')
                }
            else:
                normalized['fund_flow'] = {'score': 0.5, 'main_force_activity': 'medium', 'hot_money_interest': 'medium'}

            # 波动率信号标准化
            if 'volatility' in signals and signals['volatility']:
                volatility_score = 0.5  # 默认中性
                if 'volatility_signal_strength' in signals['volatility']:
                    volatility_score = max(0, min(1, signals['volatility']['volatility_signal_strength']))
                elif 'current_volatility' in signals['volatility']:
                    # 基于波动率水平计算信号强度
                    vol = signals['volatility']['current_volatility']
                    if vol < 0.15:  # 低波动率，正面信号
                        volatility_score = 0.7
                    elif vol > 0.35:  # 高波动率，负面信号
                        volatility_score = 0.3
                    else:
                        volatility_score = 0.5

                normalized['volatility'] = {
                    'score': volatility_score,
                    'regime': signals['volatility'].get('current_regime', 'normal'),
                    'trend': signals['volatility'].get('trend', 'stable')
                }
            else:
                normalized['volatility'] = {'score': 0.5, 'regime': 'normal', 'trend': 'stable'}

            return normalized

        except Exception as e:
            logger.error(f"信号标准化失败: {e}")
            return {
                'policy': {'score': 0.5, 'direction': 'neutral', 'strength': 'medium'},
                'sentiment': {'score': 0.5, 'resonance_strength': 0.5, 'trend': 'stable'},
                'fund_flow': {'score': 0.5, 'main_force_activity': 'medium', 'hot_money_interest': 'medium'},
                'volatility': {'score': 0.5, 'regime': 'normal', 'trend': 'stable'}
            }

    def _identify_market_regime(self, volatility_signals: Dict, sentiment_signals: Dict) -> str:
        """识别市场状态"""
        try:
            # 获取波动率水平
            volatility_level = 0.25  # 默认值
            if volatility_signals and 'current_volatility' in volatility_signals:
                volatility_level = volatility_signals['current_volatility']

            # 获取情绪水平
            sentiment_level = 0.5  # 默认中性
            if sentiment_signals and 'sentiment_index' in sentiment_signals:
                sentiment_level = (sentiment_signals['sentiment_index'] + 1) / 2

            # 市场状态判断
            if (volatility_level < self.market_regimes['bull']['volatility_threshold'] and
                sentiment_level > self.market_regimes['bull']['sentiment_threshold']):
                return 'bull'
            elif (volatility_level > self.market_regimes['bear']['volatility_threshold'] or
                  sentiment_level < self.market_regimes['bear']['sentiment_threshold']):
                return 'bear'
            else:
                return 'sideways'

        except Exception as e:
            logger.warning(f"市场状态识别失败: {e}")
            return 'sideways'

    def _adjust_weights_by_regime(self, market_regime: str) -> Dict:
        """根据市场状态调整权重"""
        try:
            base_weights = self.signal_weights.copy()

            if market_regime == 'bull':
                # 牛市：增加情绪和资金流权重
                base_weights['sentiment'] *= 1.2
                base_weights['fund_flow'] *= 1.1
                base_weights['policy'] *= 0.9
                base_weights['volatility'] *= 0.8
            elif market_regime == 'bear':
                # 熊市：增加政策和波动率权重
                base_weights['policy'] *= 1.3
                base_weights['volatility'] *= 1.2
                base_weights['sentiment'] *= 0.8
                base_weights['fund_flow'] *= 0.9
            # sideways市场保持原权重

            # 权重归一化
            total_weight = sum(base_weights.values())
            adjusted_weights = {k: v / total_weight for k, v in base_weights.items()}

            return adjusted_weights

        except Exception as e:
            logger.warning(f"权重调整失败: {e}")
            return self.signal_weights

    def _calculate_composite_score(self, normalized_signals: Dict, weights: Dict) -> float:
        """计算综合评分"""
        try:
            composite_score = 0.0

            for signal_type, weight in weights.items():
                if signal_type in normalized_signals:
                    signal_score = normalized_signals[signal_type]['score']
                    composite_score += signal_score * weight

            return max(0.0, min(1.0, composite_score))

        except Exception as e:
            logger.error(f"综合评分计算失败: {e}")
            return 0.5

    def _assess_risk(self, symbol: str, volatility_signals: Dict, fund_flow_signals: Dict) -> Dict:
        """评估风险"""
        try:
            risk_level = 'medium'
            risk_score = 0.5
            risk_factors = []

            # 波动率风险
            if volatility_signals and 'current_volatility' in volatility_signals:
                vol = volatility_signals['current_volatility']
                if vol > self.risk_params['volatility_limit']:
                    risk_factors.append('高波动率')
                    risk_score += 0.2
                elif vol < 0.10:
                    risk_factors.append('低波动率机会')
                    risk_score -= 0.1

            # 资金流风险
            if fund_flow_signals and 'main_force_activity' in fund_flow_signals:
                activity = fund_flow_signals['main_force_activity']
                if activity == 'low':
                    risk_factors.append('主力资金不活跃')
                    risk_score += 0.1
                elif activity == 'high':
                    risk_factors.append('主力资金活跃')
                    risk_score -= 0.1

            # 风险等级判断
            if risk_score > 0.7:
                risk_level = 'high'
            elif risk_score < 0.3:
                risk_level = 'low'
            else:
                risk_level = 'medium'

            return {
                'risk_level': risk_level,
                'risk_score': max(0.0, min(1.0, risk_score)),
                'risk_factors': risk_factors
            }

        except Exception as e:
            logger.error(f"风险评估失败: {e}")
            return {'risk_level': 'medium', 'risk_score': 0.5, 'risk_factors': []}

    def _generate_decision(self, composite_score: float, risk_assessment: Dict) -> Dict:
        """生成投资决策"""
        try:
            # 基于综合评分的初始决策
            if composite_score >= self.decision_thresholds['strong_buy']:
                action = 'strong_buy'
            elif composite_score >= self.decision_thresholds['buy']:
                action = 'buy'
            elif composite_score >= self.decision_thresholds['hold']:
                action = 'hold'
            elif composite_score >= self.decision_thresholds['sell']:
                action = 'sell'
            else:
                action = 'strong_sell'

            # 风险调整
            if risk_assessment['risk_level'] == 'high':
                if action in ['strong_buy', 'buy']:
                    action = 'hold'  # 高风险时降级决策
                elif action == 'hold':
                    action = 'sell'
            elif risk_assessment['risk_level'] == 'low':
                if action == 'hold' and composite_score > 0.55:
                    action = 'buy'  # 低风险时可以升级决策

            return {
                'action': action,
                'confidence': self._get_action_confidence(action, composite_score),
                'reasoning': self._get_decision_reasoning(action, composite_score, risk_assessment)
            }

        except Exception as e:
            logger.error(f"决策生成失败: {e}")
            return {'action': 'hold', 'confidence': 0.5, 'reasoning': '决策生成异常，建议观望'}

    def _calculate_position_size(self, decision: Dict, risk_assessment: Dict, composite_score: float) -> Dict:
        """计算仓位建议"""
        try:
            action = decision['action']
            base_position = 0.0

            # 基础仓位
            if action == 'strong_buy':
                base_position = 0.08
            elif action == 'buy':
                base_position = 0.05
            elif action == 'hold':
                base_position = 0.02  # 保持小仓位
            else:
                base_position = 0.0

            # 风险调整
            risk_multiplier = 1.0
            if risk_assessment['risk_level'] == 'high':
                risk_multiplier = 0.5
            elif risk_assessment['risk_level'] == 'low':
                risk_multiplier = 1.2

            # 信号强度调整
            signal_multiplier = composite_score * 1.5  # 0.75倍到1.5倍

            # 最终仓位
            final_position = base_position * risk_multiplier * signal_multiplier
            final_position = min(final_position, self.risk_params['max_position_size'])

            return {
                'recommended_position': final_position,
                'max_position': self.risk_params['max_position_size'],
                'risk_adjusted': risk_multiplier != 1.0,
                'position_reasoning': f"基础仓位{base_position:.2%}，风险调整{risk_multiplier:.1f}倍，信号强度调整{signal_multiplier:.1f}倍"
            }

        except Exception as e:
            logger.error(f"仓位计算失败: {e}")
            return {'recommended_position': 0.0, 'max_position': 0.1, 'risk_adjusted': False, 'position_reasoning': '仓位计算异常'}

    def _explain_signals(self, normalized_signals: Dict, weights: Dict) -> List[str]:
        """解释信号来源"""
        explanations = []

        try:
            for signal_type, weight in weights.items():
                if signal_type in normalized_signals:
                    signal = normalized_signals[signal_type]
                    score = signal['score']

                    if signal_type == 'policy':
                        direction = signal.get('direction', 'neutral')
                        explanations.append(f"政策信号({weight:.1%}权重): {direction}方向，评分{score:.2f}")
                    elif signal_type == 'sentiment':
                        trend = signal.get('trend', 'stable')
                        explanations.append(f"情绪信号({weight:.1%}权重): {trend}趋势，评分{score:.2f}")
                    elif signal_type == 'fund_flow':
                        activity = signal.get('main_force_activity', 'medium')
                        explanations.append(f"资金流信号({weight:.1%}权重): 主力{activity}活跃，评分{score:.2f}")
                    elif signal_type == 'volatility':
                        regime = signal.get('regime', 'normal')
                        explanations.append(f"波动率信号({weight:.1%}权重): {regime}状态，评分{score:.2f}")

            return explanations

        except Exception as e:
            logger.error(f"信号解释失败: {e}")
            return ["信号解释生成失败"]

    def _get_action_confidence(self, action: str, composite_score: float) -> float:
        """获取决策置信度"""
        try:
            # 基于评分与阈值的距离计算置信度
            if action == 'strong_buy':
                distance = composite_score - self.decision_thresholds['strong_buy']
                confidence = 0.8 + min(0.2, distance * 4)
            elif action == 'buy':
                distance = min(
                    composite_score - self.decision_thresholds['buy'],
                    self.decision_thresholds['strong_buy'] - composite_score
                )
                confidence = 0.6 + min(0.3, distance * 6)
            elif action == 'hold':
                distance = min(
                    composite_score - self.decision_thresholds['sell'],
                    self.decision_thresholds['buy'] - composite_score
                )
                confidence = 0.5 + min(0.3, distance * 3)
            elif action == 'sell':
                distance = min(
                    composite_score - self.decision_thresholds['strong_sell'],
                    self.decision_thresholds['hold'] - composite_score
                )
                confidence = 0.6 + min(0.3, distance * 6)
            else:  # strong_sell
                distance = self.decision_thresholds['strong_sell'] - composite_score
                confidence = 0.8 + min(0.2, distance * 4)

            return max(0.3, min(1.0, confidence))

        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.5

    def _get_decision_reasoning(self, action: str, composite_score: float, risk_assessment: Dict) -> str:
        """获取决策推理"""
        try:
            reasoning_parts = []

            # 评分说明
            reasoning_parts.append(f"综合评分{composite_score:.3f}")

            # 决策说明
            action_descriptions = {
                'strong_buy': '强烈买入 - 多重积极信号汇聚',
                'buy': '买入 - 积极信号占主导',
                'hold': '持有 - 信号中性或存在分歧',
                'sell': '卖出 - 消极信号增多',
                'strong_sell': '强烈卖出 - 多重风险信号'
            }
            reasoning_parts.append(action_descriptions.get(action, '未知决策'))

            # 风险说明
            risk_level = risk_assessment.get('risk_level', 'medium')
            if risk_level == 'high':
                reasoning_parts.append('高风险环境，建议谨慎')
            elif risk_level == 'low':
                reasoning_parts.append('低风险环境，可适度积极')

            # 风险因素
            risk_factors = risk_assessment.get('risk_factors', [])
            if risk_factors:
                reasoning_parts.append(f"关注: {', '.join(risk_factors)}")

            return ' | '.join(reasoning_parts)

        except Exception as e:
            logger.error(f"决策推理生成失败: {e}")
            return f"{action}决策，评分{composite_score:.3f}"

    def _calculate_confidence(self, normalized_signals: Dict, risk_assessment: Dict) -> float:
        """计算整体置信度"""
        try:
            # 信号一致性评估
            scores = [signal['score'] for signal in normalized_signals.values()]
            score_std = np.std(scores)

            # 一致性越高，置信度越高
            consistency_score = max(0.3, 1.0 - score_std * 2)

            # 风险调整
            risk_score = risk_assessment.get('risk_score', 0.5)
            risk_adjusted_confidence = consistency_score * (1.0 - (risk_score - 0.5) * 0.5)

            return max(0.3, min(1.0, risk_adjusted_confidence))

        except Exception as e:
            logger.error(f"置信度计算失败: {e}")
            return 0.5

    def _get_empty_decision_result(self, symbol: str) -> Dict:
        """获取空的决策结果"""
        return {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'composite_score': 0.5,
            'decision': {'action': 'hold', 'confidence': 0.3, 'reasoning': '数据不足，建议观望'},
            'position_recommendation': {'recommended_position': 0.0, 'max_position': 0.1, 'risk_adjusted': False, 'position_reasoning': '数据不足'},
            'market_regime': 'sideways',
            'risk_assessment': {'risk_level': 'medium', 'risk_score': 0.5, 'risk_factors': ['数据不足']},
            'signal_breakdown': {},
            'signal_weights': self.signal_weights,
            'signal_explanation': ['数据不足，无法生成有效信号'],
            'confidence_level': 0.3
        }

    def batch_decision_analysis(self, symbols: List[str],
                              all_signals: Dict) -> Dict:
        """
        批量决策分析

        Args:
            symbols: 股票代码列表
            all_signals: 所有股票的信号数据

        Returns:
            Dict: 批量决策结果
        """
        try:
            logger.info(f"开始批量决策分析，股票数量: {len(symbols)}")

            batch_results = {}
            summary_stats = {
                'strong_buy': 0,
                'buy': 0,
                'hold': 0,
                'sell': 0,
                'strong_sell': 0
            }

            for symbol in symbols:
                try:
                    # 获取该股票的各类信号
                    policy_signals = all_signals.get('policy', {}).get(symbol, {})
                    sentiment_signals = all_signals.get('sentiment', {}).get(symbol, {})
                    fund_flow_signals = all_signals.get('fund_flow', {}).get(symbol, {})
                    volatility_signals = all_signals.get('volatility', {}).get(symbol, {})

                    # 生成决策
                    decision_result = self.generate_comprehensive_decision(
                        symbol, policy_signals, sentiment_signals,
                        fund_flow_signals, volatility_signals
                    )

                    batch_results[symbol] = decision_result

                    # 统计决策分布
                    action = decision_result['decision']['action']
                    if action in summary_stats:
                        summary_stats[action] += 1

                except Exception as e:
                    logger.error(f"股票 {symbol} 决策分析失败: {e}")
                    batch_results[symbol] = self._get_empty_decision_result(symbol)
                    summary_stats['hold'] += 1

            # 生成投资组合建议
            portfolio_recommendation = self._generate_portfolio_recommendation(batch_results)

            result = {
                'timestamp': datetime.now().isoformat(),
                'total_symbols': len(symbols),
                'individual_decisions': batch_results,
                'summary_statistics': summary_stats,
                'portfolio_recommendation': portfolio_recommendation
            }

            logger.info(f"批量决策分析完成: {summary_stats}")
            return result

        except Exception as e:
            logger.error(f"批量决策分析失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'total_symbols': len(symbols),
                'individual_decisions': {},
                'summary_statistics': {'hold': len(symbols)},
                'portfolio_recommendation': {'recommended_stocks': [], 'total_position': 0.0}
            }

    def _generate_portfolio_recommendation(self, batch_results: Dict) -> Dict:
        """生成投资组合建议"""
        try:
            recommended_stocks = []
            total_position = 0.0

            # 按综合评分排序
            sorted_results = sorted(
                batch_results.items(),
                key=lambda x: x[1]['composite_score'],
                reverse=True
            )

            # 选择评分最高且决策为买入的股票
            for symbol, result in sorted_results:
                action = result['decision']['action']
                if action in ['strong_buy', 'buy'] and total_position < 0.8:  # 最大80%仓位
                    position = result['position_recommendation']['recommended_position']
                    if total_position + position <= 0.8:
                        recommended_stocks.append({
                            'symbol': symbol,
                            'action': action,
                            'position': position,
                            'score': result['composite_score'],
                            'confidence': result['confidence_level'],
                            'reasoning': result['decision']['reasoning']
                        })
                        total_position += position

                    if len(recommended_stocks) >= 10:  # 最多10只股票
                        break

            return {
                'recommended_stocks': recommended_stocks,
                'total_position': total_position,
                'diversification_score': len(recommended_stocks) / 10.0,
                'average_confidence': np.mean([stock['confidence'] for stock in recommended_stocks]) if recommended_stocks else 0.0
            }

        except Exception as e:
            logger.error(f"投资组合建议生成失败: {e}")
            return {'recommended_stocks': [], 'total_position': 0.0, 'diversification_score': 0.0, 'average_confidence': 0.0}
