2025-05-27 14:25:50,348 - __main__ - INFO - 开始情绪共振模型测试 - 2025-05-27 14:25:50.348098
2025-05-27 14:25:50,348 - __main__ - INFO - ================================================================================
2025-05-27 14:25:50,348 - __main__ - INFO - 测试情绪共振模型
2025-05-27 14:25:50,348 - __main__ - INFO - ================================================================================
2025-05-27 14:25:50,348 - engines.sentiment.resonance_model - INFO - 情绪共振模型初始化完成
2025-05-27 14:25:50,348 - __main__ - INFO - ✅ SentimentResonanceModel 初始化成功
2025-05-27 14:25:50,348 - __main__ - INFO - 📊 测试数据准备完成:
2025-05-27 14:25:50,348 - __main__ - INFO -    新闻数据: 4 条
2025-05-27 14:25:50,348 - __main__ - INFO -    政策数据: 3 条
2025-05-27 14:25:50,348 - __main__ - INFO - 
开始执行情绪共振分析...
2025-05-27 14:25:50,348 - engines.sentiment.resonance_model - INFO - 开始分析情绪共振效应...
2025-05-27 14:25:54,811 - engines.sentiment.resonance_model - INFO - 情绪共振分析完成
2025-05-27 14:25:54,811 - __main__ - INFO - 
============================================================
2025-05-27 14:25:54,811 - __main__ - INFO - 分析结果验证
2025-05-27 14:25:54,811 - __main__ - INFO - ============================================================
2025-05-27 14:25:54,811 - __main__ - INFO - ✅ 结果结构完整
2025-05-27 14:25:54,811 - __main__ - INFO - 
============================================================
2025-05-27 14:25:54,811 - __main__ - INFO - 情绪共振分析结果
2025-05-27 14:25:54,811 - __main__ - INFO - ============================================================
2025-05-27 14:25:54,811 - __main__ - INFO - 📰 新闻情绪分析:
2025-05-27 14:25:54,811 - __main__ - INFO -    总数量: 4
2025-05-27 14:25:54,811 - __main__ - INFO -    平均情绪: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    加权情绪: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    最终情绪: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    正面比例: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    负面比例: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    情绪趋势: insufficient_data
2025-05-27 14:25:54,811 - __main__ - INFO - 
📋 政策情绪分析:
2025-05-27 14:25:54,811 - __main__ - INFO -    总数量: 3
2025-05-27 14:25:54,811 - __main__ - INFO -    平均情绪: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    加权情绪: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    最终情绪: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    正面比例: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    负面比例: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    情绪趋势: insufficient_data
2025-05-27 14:25:54,811 - __main__ - INFO - 
🔄 共振效应分析:
2025-05-27 14:25:54,811 - __main__ - INFO -    存在共振: False
2025-05-27 14:25:54,811 - __main__ - INFO -    共振类型: None
2025-05-27 14:25:54,811 - __main__ - INFO -    共振强度: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    共振方向: neutral
2025-05-27 14:25:54,811 - __main__ - INFO -    置信度: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO - 
📈 综合指标:
2025-05-27 14:25:54,811 - __main__ - INFO -    综合情绪指数: 0.000
2025-05-27 14:25:54,811 - __main__ - INFO -    市场压力指数: 0.000
2025-05-27 14:25:54,815 - __main__ - INFO -    情绪波动率: 0.000
2025-05-27 14:25:54,815 - __main__ - INFO -    信息密度: 7
2025-05-27 14:25:54,815 - __main__ - INFO - 
🔮 情绪预测:
2025-05-27 14:25:54,815 - __main__ - INFO -    预测情绪: 0.000
2025-05-27 14:25:54,815 - __main__ - INFO -    预测时间: 24h
2025-05-27 14:25:54,815 - __main__ - INFO -    预测置信度: 0.700
2025-05-27 14:25:54,815 - __main__ - INFO -    趋势方向: down
2025-05-27 14:25:54,815 - __main__ - INFO - 
🎯 模型置信度: 0.600
2025-05-27 14:25:54,815 - __main__ - INFO - 
============================================================
2025-05-27 14:25:54,815 - __main__ - INFO - 测试边界情况
2025-05-27 14:25:54,815 - __main__ - INFO - ============================================================
2025-05-27 14:25:54,815 - engines.sentiment.resonance_model - INFO - 开始分析情绪共振效应...
2025-05-27 14:25:54,815 - engines.sentiment.resonance_model - INFO - 情绪共振分析完成
2025-05-27 14:25:54,815 - __main__ - WARNING - ⚠️ 空数据处理可能有问题
2025-05-27 14:25:54,815 - engines.sentiment.resonance_model - INFO - 开始分析情绪共振效应...
2025-05-27 14:25:55,308 - engines.sentiment.resonance_model - INFO - 情绪共振分析完成
2025-05-27 14:25:55,308 - __main__ - INFO - ✅ 单一数据源处理正确
2025-05-27 14:25:55,308 - __main__ - INFO - 
🎉 情绪共振模型测试完成！
2025-05-27 14:25:55,308 - __main__ - INFO - 
============================================================
2025-05-27 14:25:55,308 - __main__ - INFO - 测试FinBERT模型集成
2025-05-27 14:25:55,308 - __main__ - INFO - ============================================================
2025-05-27 14:25:55,586 - __main__ - INFO - ✅ SentimentAnalyzer 初始化成功
2025-05-27 14:25:55,586 - __main__ - INFO - 测试情绪分析:
2025-05-27 14:25:55,616 - __main__ - INFO -   1. 央行降准释放流动性，股市迎来重大利好... → 情绪分数: 0.000
2025-05-27 14:25:55,636 - __main__ - INFO -   2. 通胀压力加大，市场担忧货币政策收紧... → 情绪分数: 0.000
2025-05-27 14:25:55,664 - __main__ - INFO -   3. 科技股表现平稳，投资者保持观望态度... → 情绪分数: 0.000
2025-05-27 14:25:55,664 - __main__ - INFO - ✅ FinBERT集成测试成功
2025-05-27 14:25:55,666 - __main__ - INFO - 
================================================================================
2025-05-27 14:25:55,666 - __main__ - INFO - 情绪共振模型测试结果
2025-05-27 14:25:55,666 - __main__ - INFO - ================================================================================
2025-05-27 14:25:55,666 - __main__ - INFO - ✅ 成功 情绪共振模型
2025-05-27 14:25:55,666 - __main__ - INFO - ✅ 成功 FinBERT集成
2025-05-27 14:25:55,666 - __main__ - INFO - 
总体结果: 2/2 项测试通过
2025-05-27 14:25:55,666 - __main__ - INFO - 🎉 情绪共振模型基本功能正常！
2025-05-27 14:25:55,666 - __main__ - INFO - 📊 可以进行下一步的系统集成测试
2025-05-27 14:25:55,666 - __main__ - INFO - 
测试完成时间: 2025-05-27 14:25:55.666134
2025-05-27 14:25:55,666 - __main__ - INFO - 详细日志已保存到: sentiment_resonance_test.log
